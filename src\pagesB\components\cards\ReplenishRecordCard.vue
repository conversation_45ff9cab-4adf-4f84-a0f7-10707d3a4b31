<template>
  <view class="card">
    <view class="card_cargo">
      <view> {{ oosNums }} </view>
      <view>{{ goodsName }}</view>
      <view>{{ goodsNum }}</view>
    </view>
    <view class="replenish_info flexRowBetween">
      <view>
        <view>补货人：</view>
        <view>{{ info.bhName }}</view>
      </view>
      <view>
        <view>补货时间：</view>
        <view>{{ time }}</view>
      </view>
    </view>
  </view>
</template>

<script>
import { timestampToTime } from "@/common/tools/utils";
export default {
  name: "ReplanishRecordCard",
  props: {
    info: { type: String | Object, default: {} },
  },
  computed: {
    goodsName() {
      return this.info?.content?.[this.info.oos_nums]?.goods_name || "暂无";
    },
    goodsNum() {
      return this.info?.content?.[this.info.oos_nums]?.qu_value || "暂无";
    },
    oosNums() {
      return this.info.oos_nums.length == 1
        ? "0" + this.info.oos_nums
        : this.info.oos_nums || "暂无";
    },
    time() {
      return timestampToTime(this.info.time * 1000);
    },
  },
};
</script>

<style lang="scss" scoped>
.card {
  &_cargo {
    display: flex;
    color: $textBlack;
    font-size: $font-size-middle;
    font-weight: bold;
    height: 86rpx;
    border-bottom: 2rpx solid $dividerColor;
    > view {
      flex: 1;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }
  .replenish_info {
    padding: 30rpx;
    > view {
      display: flex;
      > view {
        font-size: $font-size-small;
        &:first-child {
          color: $textDarkGray;
        }
        &:last-child {
          color: $textBlack;
        }
      }
    }
  }
}
</style>