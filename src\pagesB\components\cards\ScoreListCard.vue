<template>
  <view class="card" @click="goEdit">
    <view class="content">
      <view class="content-img">
        <image class="img" :src="vDefaultIcon" />
      </view>
      <view class="content-box">
        <view>
          <view>类型：</view>
          <view>{{ info.task_cat }}</view>
        </view>
        <view>
          <view>名称：</view>
          <view>{{ info.name }}</view>
        </view>
        <view>
          <view>积分：</view>
          <view>{{ info.point_number }}</view>
        </view>
        <view>
          <view>状态：</view>
          <view :style="info.status == 1 ? '' : 'color:red'">{{
    info.status == 1 ? "正常" : "暂停"
  }}</view>
        </view>
        <view>
          <view>完成：</view>
          <view class="finish-num">{{ info.task_times }}</view>
        </view>
      </view>
    </view>
    <view class="trash" @click.stop="del">
      <BaseIcon name="trash" color="red" />
    </view>
    <view class="task-record" @click.stop="goDetail">查看任务记录</view>
    <BaseModal :show.sync="isShowDelModal" @confirm="confirmDel" content="您的任务将会删除，此操作不可恢复，是否继续删除？" title="温馨提示" />
  </view>
</template>

<script>
import BaseIcon from "@/components/base/BaseIcon.vue";
import BaseModal from "@/components/base/BaseModal.vue";
export default {
  components: { BaseIcon, BaseModal },
  name: "ScoreListCard",
  props: { info: { type: Object, default: {} } },
  data() {
    return {
      isShowDelModal: false,
    };
  },
  methods: {
    goDetail() {
      uni.navigateTo({ url: "/pagesB/score/ScoreDetails?id=" + this.info.id });
    },
    del() {
      this.isShowDelModal = true;
    },
    async confirmDel() {
      try {
        let data = {
          ids: this.info.id,
        };
        await this.$u.api.delTask(data)
        this.isShowSuccess("删除成功", 0, () => {
            this.$emit("refresh");
          });
      }catch(error){
        console.log('错误信息',error)
      }
     
    },
    goEdit() {
      uni.navigateTo({
        url:
          "/pagesB/score/ScoreTaskAdd?from=edit&data=" +
          JSON.stringify(this.info),
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.card {
  position: relative;
  padding: 20rpx;

  .trash {
    position: absolute;
    right: 12rpx;
    top: 12rpx;
  }

  .task-record {
    position: absolute;
    right: 20rpx;
    bottom: 12rpx;
    color: $themeComColor;
  }
}

.content {
  display: flex;

  &-img {
    width: 200rpx;
    height: 200rpx;
    flex-shrink: 0;

    .img {
      width: 100%;
      height: 100%;
    }
  }

  &-box {
    flex: 1;
    margin-left: 20rpx;

    >view {
      display: flex;
      align-items: center;

      >view {
        &:first-child {
          color: $textDarkGray;
          font-size: $font-size-middle;
        }

        &:last-child {
          color: $textBlack;
          font-size: $font-size-base;
        }
      }

      .finish-num {
        color: $themeComColor !important;
      }
    }
  }
}
</style>