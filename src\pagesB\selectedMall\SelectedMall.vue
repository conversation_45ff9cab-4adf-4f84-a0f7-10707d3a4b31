<template>
  <view>
    <view class="sticker">
      <BaseNavbar title="商城订单" />
      <BaseTabs :current="curTabIndex" :list="tabList" :isShowBar="false" @change="tabChange" />
    </view>

    <ComList :loading-type="loadingType">
      <SelectedMallCard
        v-for="item in listData"
        :key="item.id"
        :info="item"
        @confirm="confirmCard"
      />
    </ComList>
  </view>
</template>

<script>
import BaseNavbar from "@/components/base/BaseNavbar.vue";
import BaseTabs from "../../components/base/BaseTabs.vue";
import myPull from "@/mixins/myPull.js";
import ComList from "@/components/list/ComList.vue";
import SelectedMallCard from "../components/cards/SelectedMallCard.vue";
export default {
  components: { BaseNavbar, BaseTabs, ComList, SelectedMallCard },
  data() {
    return {
      tabList: [
        {
          name: "全部",
          status: "",
        },
        {
          name: "待处理",
          status: 0,
        },
        {
          name: "已处理",
          status: 1,
        },
        {
          name: "已发货",
          status: 2,
        },
      ],
      curTabIndex: 0,
    };
  },
  methods: {
    tabChange(e) {
      this.curTabIndex = e;
      this.refresh();
    },
    getList(page, done) {
      let data = {
        page,
        limit: 10,
        status: this.tabList[this.curTabIndex].status,
      };
      this.$u.api.selectedMallGetOrderList(data).then((res) => {
        done(res.data.list);
      });
    },
    confirmCard(msg) {
      this.isShowSuccess(msg + "成功", 0, this.refresh);
    },
  },
  onLoad(opt) {
    this.refresh();
  },
  mixins: [myPull()],
};
</script>

<style lang="scss">
page {
  background-color: $pageBgColor;
}
</style>
<style lang="scss" scoped>
</style>