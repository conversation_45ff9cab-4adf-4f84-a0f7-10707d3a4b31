<template>
  <u-popup v-model="newShow" :mode="mode" :mask="mask" :mask-close-able="maskClose" :height="height"
    :negativeTop="negativeTop" :custom-style="customStyle" :safe-area-inset-bottom="safeArea" :closeable="closeable"
    :width="width" :border-radius="radius" @close="close" :z-index="zIndex">
    <slot />
  </u-popup>
</template>

<script>
export default {
  props: {
    show: { type: <PERSON>olean, default: false },
    mode: { type: String, default: "bottom" }, //top / right / bottom / center
    mask: { type: Boolean, default: true }, //是否显示遮罩
    length: { type: [Number,String], default: "auto" }, //mode=left 有效
    maskClose: { type: Boolean, default: true }, //点击遮罩是否可以关闭弹出层
    height: { type: [Number,String], default: "auto" },
    width: { type: [Number,String], default: "auto" },
    negativeTop: { type: [Number,String], default: 0 }, //往上偏移的值，单位任意，数值则默认为rpx单位
    customStyle: {
      type: Object,
      default: function () {
      return {};
    },
    },
    safeArea: { type: Boolean, default: false }, //是否开启安全区域
    closeable: { type: String | Boolean, default: false },
    radius: { type: [Number,String], default: "auto" }, //弹窗圆角值
    zIndex: { type: [Number,String], default: 999999 },
  },
  methods:{
    close(){
      this.$emit("close"); 
    }
  },
  computed: {
    newShow: {
      get: function () {
        return this.show;
      },
      set: function (val) {
        return this.$emit("update:show", val);
      },
    },
  },
};
</script>

<style lang="scss" scoped>
::v-deep .u-drawer {
  z-index: 99999;
}
</style>