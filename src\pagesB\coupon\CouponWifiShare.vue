<template>
  <view>
    <BaseNavbar title="我的分享海报" />
    <view class="content">
      <view class="main" id="main" :style="{ backgroundColor }">
        <block v-for="(item, i) in canvasList" :key="i">
          <view class="drag-wrap">
            <!-- 图片 -->
            <view v-if="item.type === 'image' || item.type === 'bg-image'">
              <img
                :data-id="item.id"
                :style="{
                  top: item.top + 'px',
                  left: item.left + 'px',
                  width: item.width + 'px',
                  height: item.height + 'px',
                  transform: 'rotate(' + item.rotate + 'deg)',
                  borderRadius: item.radius + 'px',
                }"
                class="drag-item"
                :class="{ active: item.active }"
                :src="item.path"
                alt=""
                srcset=""
              />
            </view>

            <!-- 文本 -->
            <view v-if="item.type === 'text'">
              <text
                class="drag-item"
                :data-id="item.id"
                :class="{ active: item.active }"
                :style="{
                  top: item.top + 'px',
                  left: item.left + 'px',
                  width: item.width + 'px',
                  height: item.height + 'px',
                  transform: 'rotate(' + item.rotate + 'deg)',
                  color: item.color,
                  fontSize: item.size + 'px',
                  textDecoration: item.textDecoration,
                  textAlign: item.textAlign,
                  fontWeight: item.fontWeight,
                }"
                >{{ item.text }}</text
              >
            </view>
          </view>
        </block>
      </view>
    </view>
    <view class="btn" :style="{ bottom: vIphoneXBottomHeight + 20 + 'rpx' }">
      <view class="btn_box" @click="isShowCanvas = true">
        <image class="btn_img" src="/pagesB/static/img/coupon/album.png" />
        <view class="btn_text">海报下载</view>
      </view>
    </view>
    <CanvasTemplate
      :show="isShowCanvas"
      :canvasList="canvasList"
      :canvas="canvas"
      :backgroundColor="backgroundColor"
      @finish="isShowCanvas = false"
    />
  </view>
</template>
<script>
import BaseNavbar from "@/components/base/BaseNavbar.vue";
import CanvasTemplate from "./components/CanvasTemplate.vue";
export default {
  components: { BaseNavbar, CanvasTemplate },
  data() {
    return {
      canvasList: [
        {
          id: 1,
          type: "bg-image",
          path: "https://towel.wrlsw.com/upload/20220820/036e3bae0b41f397f9b199da11e34cb4.png",
          width: 50,
          height: 50,
          active: false,
          radius: 0,
          rotate: 0,
          top: 0,
          left: 0,
        },

        {
          id: 2,
          type: "text",
          top: uni.upx2px(636),
          left: uni.upx2px(88),
          text: "打造智慧门店    共建数字生态",
          size: uni.upx2px(32),
          color: "#fff",
          width: 300,
          height: 100,
          active: false,
          radius: 0,
          rotate: 0,
          textAlign: "left",
          fontWeight: "normal",
        },
        {
          id: 3,
          type: "text",
          top: uni.upx2px(700),
          left: uni.upx2px(230),
          width: 300,
          height: 100,
          text: "",
          size: uni.upx2px(32),
          color: "#fff",
          active: false,
          radius: 0,
          rotate: 0,
          textAlign: "left",
          fontWeight: "bold",
        },
        {
          id: 4,
          type: "image",
          path: "",
          width: uni.upx2px(400),
          height: uni.upx2px(400),
          active: false,
          radius: 0,
          rotate: 0,
          top: uni.upx2px(150),
          left: uni.upx2px(94),
        },
      ],
      canvas: {
        width: 0,
        height: 0,
      },
      isShowCanvas: false,
      backgroundColor: "#fff",
    };
  },

  methods: {
    //获取主体框大小
    getMainSize() {
      let query = uni.createSelectorQuery();
      query
        .select("#main")
        .boundingClientRect((res) => {
          let { width, height } = res;
          this.canvasList.forEach((item) => {
            if (item.type.includes("bg-image")) {
              item.width = width;
              item.height = height;
            }
          });
          this.canvas = {
            width,
            height,
          };
        })
        .exec();
    },
  },
  onLoad(opt) {
    const { hotelName } = opt;

    this.getMainSize();

    this.canvasList[2].text = hotelName;
    this.canvasList[3].path = this.vMiniCode;
  },
};
</script>

<style lang="scss">
page {
  background-color: $pageBgColor;
}
</style>
<style scoped  lang='scss'>
.content {
  padding: 80rpx;
  .main {
    position: relative;

    height: 770rpx;
    border-radius: 20rpx;

    .bg-img {
      position: absolute;
      width: 100%;
      height: 100%;
    }
    .drag-wrap {
      position: relative;
      .scale-icon {
        position: absolute;
        width: 40rpx;
        height: 40rpx;
      }
      .drag-item {
        position: absolute;
        display: block;
        overflow: hidden;
        word-break: break-all;
        line-height: 1;

        box-sizing: border-box;
        &.active {
          border-color: #fff;
        }
      }
      .scale-icon {
        transform: translate(-50%, -50%);
      }
    }
  }
}
.btn {
  position: fixed;
  bottom: 20rpx;
  left: 0;
  right: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  &_box {
    display: flex;
    flex-direction: column;
    align-items: center;
  }
  &_img {
    width: 100rpx;
    height: 100rpx;
  }
  &_text {
    color: #5a5a5a;
    font-size: 26rpx;
  }
}
</style>