<template>
  <view>
    <BaseNavbar title="广告审核详情" />
    <ComList :loadingType="loadingType">
      <view class="card" v-for="item in listData" :key="item.id">
        <view class="item">
          <view class="item-title">订单编号：</view>
          <view class="item-txt">{{ item.order_sn }}</view>
        </view>
        <view class="item">
          <view class="item-title">设备编号：</view>
          <view class="item-txt">{{ item.device_sn }}</view>
        </view>
        <view class="item">
          <view class="item-title">投放时间：</view>
          <view class="item-txt">{{ item.date }}</view>
        </view>
        <view class="item">
          <view class="item-title">投放价格：</view>
          <view class="item-txt">{{ item.price }}</view>
        </view>
        <view class="item">
          <view class="item-title">广告类型：</view>
          <view class="item-txt">{{ type[item.type] }}</view>
        </view>
      </view>
    </ComList>
  </view>
</template>
<script>
import BaseNavbar from "../../components/base/BaseNavbar.vue";
import ComList from "../../components/list/ComList.vue";
export default {
  components: { BaseNavbar, ComList },
  data() {
    return {
      order: "",
      loadingType: -1,
      listData: [],
      type: {
        1: '插屏广告',
        2: '弹屏广告',
      }
    };
  },
  methods: {

    getDetails() {
      let data = {
        order_sn: this.order
      };

      this.$u.api.getMemberApplyDetail(data).then(res => {

        this.loadingType = res?.length > 0 ? '2' : '3'
        this.listData = res
      })
    }
  },
  onLoad(opt) {
    this.order = opt?.order;

    this.getDetails()
  },
};
</script>
<style lang="scss">
page {
  background-color: $pageBgColor;
}
</style>
<style scoped lang="scss">
.card {
  padding: 20rpx;
  margin-bottom: 20rpx;
}

.item {
  display: flex;
  line-height: 1.6;

  &-title {
    color: $textDarkGray;
    flex-shrink: 0;
  }

  &-txt {
    color: $textBlack;
  }
}
</style>
