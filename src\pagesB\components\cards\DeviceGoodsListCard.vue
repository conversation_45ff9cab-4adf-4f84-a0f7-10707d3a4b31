<template>
  <view class="goodsCard">
    <view class="content">
      <view class="cargo-way flexColumnAllCenter">
        <view class="way">{{
          String(info.num).length == 1 ? "0" + info.num : info.num
        }}</view>
        <view>{{ info.unit == 3 ? vCargoLanes : vCargoLanes }}</view>
      </view>
      <image class="goods-img" :src="info.original_img || vDefaultIcon" />
      <view class="goods-content flexColumnBetween">
        <view class="name">{{ info.goods_name || "请选择商品" }}</view>
        <view>
          <text>售价：</text>
          <text class="text-red">{{ info.good_price || 0 }}元 </text>
          <block v-if="info.second_price" >
            <text style="margin-left: 10rpx;">第二次售价：</text>
            <text class="text-red">{{ info.second_price || 0 }}元</text>
          </block>
          <block v-if="info.vip_price * 1" class="margin_left">
            <text style="margin-left: 10rpx;">会员价：</text>
            <text class="text-red">{{ info.vip_price || 0 }}元</text>
          </block>
        </view>
        <!-- <view>
          <text>是否热卖：</text>
          <text :class="info.is_hot ? 'text-red' : ''">{{ info.is_hot ? '是' : '否' }}</text>
        </view> -->
        <block v-if="info.unit != 3">
          <view class="flexRowBetween">
            <!-- <view>
              <text>容量：</text>
              <text class="text-black">300</text>
            </view> -->
            <!-- <view>
              <text>库存：</text>
              <text class="text-black">{{ info.stock || 0 }}</text>
            </view> -->
            <!-- <view>
              <text>最大库存：</text>
              <text class="text-black">{{ info.default_stock || 0 }}</text>
            </view> -->
          </view>
        </block>
        <!-- <block v-if="info.unit === 3">
          <view class="flexRowBetween">
            <view>
              <text>{{ info.goods_id == 5 ? '驾驶' : '骑行' }}时长(分钟)：</text>
              <text class="text-black">{{ info.game_time || 0 }}</text>
            </view>
          </view>
        </block> -->
        <!-- <block v-if="info.unit === 2">
          <view class="flexRowBetween">
            <view>
              <text>免费长度(CM)：</text>
              <text class="text-black">{{ info.free_length || 0 }}</text>
            </view>
            <view>
              <text>支付长度(CM)：</text>
              <text class="text-black">{{ info.pay_length || 0 }}</text>
            </view>
          </view>
        </block> -->
      </view>
    </view>
    <view class="btn flexRowBetween">

      <block v-if="from == 'place'">
        <BaseButton type="default" width="310" @onClick="del">删 除</BaseButton>
        <BaseButton type="primary" width="310" @onClick="edit">编 辑</BaseButton>

      </block>
      <block v-else>
        <BaseButton shape="circle" type="default" width="280" @onClick="supply">库存补充</BaseButton>
        <BaseButton shape="circle" type="default" width="280" @onClick="open">出 货</BaseButton>
      </block>
    </view>
  </view>
</template>

<script>
import BaseButton from "@/components/base/BaseButton.vue";
export default {
  components: { BaseButton },
  props: {
    info: {
      type: Object,
      default: function () {
        return {};
      }
    },
    index: { type: [Number, String], default: "01" },
    from: { type: String, default: "" },
  },
  methods: {
    del() {
      this.$emit("del", this.info);
    },
    edit() {
      this.$emit("edit", this.info);
    },
    supply() {
      this.$emit("supply");
    },
    open() {
      this.$emit("open");
    },


  },
};
</script>

<style lang="scss" scoped>
.goodsCard {
  background-color: $uni-bg-color;
  border-radius: $cardRadius;
  margin-bottom: 30rpx;
}

.content {
  display: flex;
  box-sizing: border-box;
  padding: 20rpx 30rpx;
  border-bottom: 2rpx solid $dividerColor;

  .cargo-way {
    color: $textBlack;
    font-size: $font-size-xlarge;
    font-weight: 700;
  }

  .goods-img {
    width: 150rpx;
    height: 150rpx;
    border-radius: $imgRadius;
    // background-color: $textGray;
    margin-left: 28rpx;
  }

  .goods-content {
    flex: 1;
    margin-left: 19rpx;
    font-size: $font-size-small;
    color: $textDarkGray;

    .name {
      color: $textBlack;
      font-size: $font-size-middle;
    }

    .text-black {
      color: $textBlack;
    }

    .text-red {
      color: $mainRed;
    }
  }
}

.btn {
  box-sizing: border-box;
  padding: 20rpx;
}
</style>