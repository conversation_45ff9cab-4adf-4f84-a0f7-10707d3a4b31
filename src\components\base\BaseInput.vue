<template>
  <view
    class="input"
    :style="{ background: bgColor }"
    @click="onClick"
    v-if="def"
  >
    <view>
      <view class="img-box" v-if="leftImg.show">
        <img class="img-icon" :src="leftImg.src" :style="leftImg.style" />
      </view>
      <u-input
        v-if="type == 'password'"
        v-model="newValue"
        :type="types"
        :placeholder="placeholder"
        :placeholderStyle="placeholderStyle"
        :custom-style="customStyle"
        :height="height"
        :focus="focus"
        @focus="onFocus"
		    @blur="handleBlur"
        :select-open="show"
        :clearable="false"
        :disabled="disabled"
        :maxlength="maxlength"
        @click="onClick"
        @input="onInput"
        :password-icon="false"
      ></u-input>
      <u-input
        v-else-if="type != 'select'"
        v-model="newValue"
        :type="type"
        :placeholder="placeholder"
        :placeholderStyle="placeholderStyle"
        :custom-style="customStyle"
        :height="height"
        :select-open="show"
        :clearable="false"
        :disabled="disabled"
        :maxlength="maxlength"
        :focus="focus"
        @click="onClick"
        @input="onInput"
        :password-icon="false"
        @focus="onFocus"
		    @blur="handleBlur"
      ></u-input>
      <u-input
        v-else
        v-model="selectValue"
        :type="type"
        :focus="focus"
        :placeholder="placeholder"
        :placeholderStyle="placeholderStyle"
        :custom-style="customStyle"
        :height="height"
        :select-open="show"
        :clearable="false"
        :disabled="disabled"
        :maxlength="maxlength"
        @focus="onFocus"
		    @blur="handleBlur"
        @click="show = type == 'select'"
        @input="onInput"
      ></u-input>
    </view>
    <u-select
      v-if="type == 'select'"
      v-model="show"
      mode="single-column"
      :list="list"
      :default-value="[index]"
      :safe-area-inset-bottom="true"
      @confirm="confirm"
    ></u-select>

    <view class="right-box" v-show="rightText != ''">
      <BaseIcon
        v-if="rightText == 'arrow'"
        name="arrow-right"
        color="#999"
        size="40"
      />
      <text class="txt" v-else>{{ rightText }}</text>
    </view>
    <view class="right-box" v-if="type == 'password'" @click="eyes">
      <BaseIcon :name="newName||''" color="#999" size="50" />
    </view>
  </view>
  <view v-else class="inputs">
    <input class="inp"
      v-model="newValue"
      :type="type"
      :placeholder="placeholder"
      :disabled="disabled"
      @click="onClick"
      @input="onInput"
      :focus="focus"
      @focus="onFocus"
		  @blur="handleBlur"
      :style="{ width: width + '%' }"
    />
  </view>
</template>

<script>
import BaseIcon from './BaseIcon.vue'
export default {
  components: { BaseIcon },
  name: 'BaseInput',
  props: {
    value: { type: [String, Number], default: '' }, //input双向绑定值
    type: { type: String, default: 'text' }, //input类型
    placeholder: { type: String, default: '请输入内容' },
    height: { type: [String, Number], default: '80' }, //input高度
    //左侧插入图标
    leftImg: {
      //左侧插入图片对象
      type: [Object, String],
      default() {
        return {
          show: false,
          style: 'width:40rpx;height:44rpx',
          src: '',
        }
      },
    },
    list: {
      //selse的list数组
      type: Array,
      default: function () {
        return [] // 返回一个空数组作为默认值
      },
    },
    index: { type: [Number, String], default: 0 }, //selsect默认选中项
    selectValue: { type: [String, Object], default: '' }, //select选中的value值
    rightText: { type: String, default: '' }, //右侧插入文本 传arrow则为右箭头
    bgColor: { type: String, default: '#f2f2f2' },
    disabled: { type: Boolean, default: false },
    maxlength: { type: [String, Number], default: 140 },
    def: {
      type: Boolean,
      default: true,
    },
    width:{
      type: [String, Number], default: 90
    },
    focus:{
      type: Boolean, default: false
    }

  },
  computed: {
    newValue: {
      // getter
      get: function () {
        return this.value
      },
      // setter
      set: function (val) {
        if (this.type != 'select') this.$emit('input', val)
      },
    },
    newName() {
      if (this.eyeShow) {
        return 'eye-fill'
      } else {
        return 'eye-off'
      }
    },
  },
  watch: {
    leftImg: {
      handler: function () {
        if (this.leftImg) {
          // 检查 this.leftImg 是否存在
          this.customStyle = {
            color: '#333',
            paddingLeft: this.leftImg.show ? '80rpx' : '20rpx',
          }
        } else {
          this.customStyle = {
            color: '#333',
            paddingLeft: '80rpx',
          }
        }
      },
      deep: true,
      immediate: true,
    },
  },
  data() {
    return {
      placeholderStyle: 'color: #999;',
      show: false,
      customStyle: { color: '#333', paddingLeft: `20rpx` },
      eyeShow: false,
      types: 'password',
    }
  },
  methods: {
    confirm(e) {
      this.$emit('confirm', e)
    },
    onClick() {
      this.$emit('onClick')
    },
    onInput() {
      this.$emit('onInput')
    },
    eyes() {
      this.eyeShow = !this.eyeShow
      if (this.eyeShow) {
        this.types = 'text'
      } else {
        this.types = 'password'
      }
    },
    handleBlur(){
      this.$emit('handleBlur')
    },
    onFocus(){
      this.$emit('onFocus')
    }

  },
}
</script>

<style lang="scss" scoped>
.input {
  background-color: $pageBgColor;
  position: relative;
  padding-right: 30rpx;
  .img-box,
  .right-box {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);

    z-index: 998;
  }
  .img-box {
    left: 16rpx;
  }
  .right-box {
    right: 16rpx;
    .txt {
      color: #333;
      font-weight: bold;
    }
  }
}
.inputs {
  min-height: 80rpx;
  background-color: $pageBgColor;
  position: relative;
  padding-right: 30rpx;
  padding-left: 20rpx;
  display: flex;
  align-items: center;
  .inp {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  .img-box,
  .right-box {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);

    z-index: 998;
  }
  .img-box {
    left: 16rpx;
  }
  .right-box {
    right: 16rpx;
    .txt {
      color: #333;
      font-weight: bold;
    }
  }
}
</style>
