/* 存储 list */
export const AddValueInObject = (arr, item) => {
  // 判断数组长度
  let arrList=arr||[]
  let index = arrList.indexOf(item)
  if (index !== -1) {
    arrList.splice(index, 1) // 删除后面的重复元素
    arrList.unshift(item) // 将元素挪到数组前面
  } else {
    // 如果元素不存在，则直接添加到数组末尾
    arrList.unshift(item)
  }
  if (arrList.length + 1 > 6) {
    // 如果数组长度大于5，删除最后一个元素并去重
    arrList.pop() // 删除最后一个元素
  }
  // 返回处理后的数组
  return arrList
}
export const deleteValueInObject = (array, item) => {
  return array.filter((i) => i !== item)
}
