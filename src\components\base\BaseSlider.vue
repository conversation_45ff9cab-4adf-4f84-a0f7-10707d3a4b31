<template>
  <view class="base-slider">
    <u-slider 
    v-model="newValue" 
    :blockSize="blockSize" 
    :min="min" 
    :max="max"   
    :step="step" 
    :activeColor="activeColor" 
    :inactiveColor="inactiveColor" 
    :blockColor="blockColor"
    :showValue="showValue" 
    :blockStyle="blockStyle" 
    @inputing="inputing"
    @changing="changing"
    @input="input"
    @change="change">
    </u-slider>
  </view>
</template>

<script>
export default {
  name: "BaseSlider",
  props: {
    value: { type: [Number,String], default: 0 },
    blockSize: { type: [Number,String], default: 18}, // 12 - 28
    min: { type: [Number,String], default: 1 }, // 可选的最小值(0-100之间)
    max: { type: [Number,String], default: 100 }, // 可选的最大值(0-100之间)
    step: { type: [Number,String], default: 1 }, 
    activeColor: { type: String , default: "#2979ff" },
    inactiveColor: { type: String , default: "#c0c4cc" },
    blockColor: { type: String , default: "#ffffff" },
    showValue: { type: <PERSON><PERSON>an, default: false }, 
    blockStyle: {
      type: Object ,
      default: function () {
                return {};
            }
    },
  },
  computed: {
    newValue: {
      // getter
      get: function () {
        return this.value;
      },
      // setter
      set: function (val) {
        this.$emit("input", val);
      },
    },
  },
  methods: {
    inputing(res, name) {
      //非自动上次调用此方法
      if (!this.auto) {
        let uploadUrl = res.map((item) => item.url);
        this.$emit("change", uploadUrl);
      }
    },
    changing(data, i, lists) {
      if (data.code != 1) {
        uni.showToast({
          title: data.msg || "上传失败~",
          icon: "none",
          mask: true,
          duration: 2000,
        });
        this.$refs.upload.remove(i);
      }
    },
    //自动上传调用此方法
    input(list) {
      if (this.auto || list.length > 0) {
        let newList = list.map((item) => item.response.data);
        this.$emit("onUpload", newList, this.index);
      }
    },
    change(i, lists, name) {
      this.onUpload(lists);
    },
  },
};
</script>

<style lang="scss" scoped>
.base-slider {
}
</style>