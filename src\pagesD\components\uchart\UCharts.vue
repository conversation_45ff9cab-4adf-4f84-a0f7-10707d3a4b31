<template>
  <view class="charts-box">
    <canvas :canvas-id="isId" :id="isId" class="charts" @touchstart="touchstart" @touchmove="touchmove"
      @touchend="touchend" />
  </view>
</template>

<script>
import uCharts from '@qiun/ucharts'
var uChartsInstance = {}
export default {
  props: {
    isId: {
      type: String,
      default: 'userche',
    },
    lineData: {
      type: Array,
      require: true,
      default: function () {
        return []
      },
    },
    chartsName: { type: String, default: '我的销售额' },
    data2: {
      type: Array,
      default: function () {
        return []
      },
    },
    titleLIst: {
      type: Array,
      default: function () {
        return []
      },
    },
    num: {
      type: [String, Number],
      default: 3,
    }
  },
  watch: {
    lineData: {
      handler: function (val) {
        this.getServerData()
      },
    },
  },
  data() {
    return {
      cWidth: 700,
      cHeight: 500,
      /* #ifdef  H5 */
      cWidth: 400,
      cHeight: 400,
      /* #endif */
    }
  },
  onReady() {
    //这里的 750 对应 css .charts 的 width
    this.cWidth = uni.upx2px(700)
    this.cHeight = uni.upx2px(500)
    this.getServerData()
    //这里的 500 对应 css .charts 的 height


  },
  /* #ifdef H5 */
  mounted() {
    //这里的 750 对应 css .charts 的 width
    this.cWidth = uni.upx2px(700)
    this.cHeight = uni.upx2px(500)
    this.getServerData()
  },
  /* #endif */
  methods: {
    getServerData() {
      //模拟从服务器获取数据时的延时
      setTimeout(() => {
        //模拟服务器返回数据，如果数据格式和标准格式不同，需自行按下面的格式拼接
        let res = {
          categories: [],
          series: [{ name: this.chartsName, data: [] }],
        }

        if (this.data2.length > 0) {
          res.series = [
            { name: this.titleLIst[0], data: [] },
            { name: this.titleLIst[1], data: [] },
          ]
          this.data2?.forEach((el) => {
            res.series[1]?.data.push(el.amount)
          })
        }
        this.lineData?.forEach((el) => {
          if (el.time_point.split('-').length > 2) {
            res.categories.push(el.time_point)
          } else {
            let arr = el.time_point.split('')
            let str = ''
            for (let i = 0; i < arr.length; i++) {
              if (i < 4) {
                str = str + arr[i]
              }
            }
            str = str + '...'
            res.categories.push(str)
          }
          res.series[0]?.data.push(el.amount)
        })
        this.drawCharts(this.isId, res)
      }, 500)

    },
    drawCharts(id, data) {
      const ctx = uni.createCanvasContext(id, this)
      uChartsInstance[id] = new uCharts({
        type: 'column',
        context: ctx,
        width: this.cWidth,
        height: this.cHeight,
        categories: data.categories,
        series: data.series,
        animation: true,
        fontSize: 8,
        background: '#FFFFFF',
        color: [
          '#1890FF',
          '#91CB74',
          '#FAC858',
          '#EE6666',
          '#73C0DE',
          '#3CA272',
          '#FC8452',
          '#9A60B4',
          '#ea7ccc',
        ],
        padding: [15, 15, 0, 5],
        touchMoveLimit: 24,
        enableScroll: true,
        legend: {},
        xAxis: {
          disableGrid: true,
          scrollShow: true,
          itemCount: this.num,
        },
        yAxis: {
          data: [
            {
              min: 0,
            },
          ],
        },
        extra: {
          column: {
            type: 'group',
            width: 30,
            activeBgColor: '#000000',
            activeBgOpacity: 0.08,
          },
        },
      })

    },
    touchstart(e) {
      uChartsInstance[e.target.id].scrollStart(e)
    },
    touchmove(e) {
      uChartsInstance[e.target.id].scroll(e)
    },
    touchend(e) {
      uChartsInstance[e.target.id].scrollEnd(e)
      uChartsInstance[e.target.id].touchLegend(e)
      uChartsInstance[e.target.id].showToolTip(e)
    },

  },
}
</script>

<style scoped>
.charts {
  
  width: 700rpx;
  height: 500rpx;

}
</style>
