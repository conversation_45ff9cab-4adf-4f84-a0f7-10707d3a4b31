<template>
  <u-checkbox
    v-model="newChecked"
    shape="circle"
    :label-disabled="isLabel"
    @change="change"
  >
    {{ title }}
  </u-checkbox>
</template>

<script>
export default {
  name: "BaseCheck",
  props: {
    checked: {
      type: Boolean, //是否选中
      default: false,
    },
    title: {
      //object主要是为了应对空值
      type: String | Object, //
      default: "",
    },
    isLabel: {
      type: Boolean | String,
      default: false,
    },
  },
  computed: {
    newChecked: {
      // getter
      get: function () {
        return this.checked;
      },
      // setter
      set: function (val) {
        this.$emit("update:checked", val);
      },
    },
  },
  methods: {
    change(e) {
      this.$emit("changeCheck");
    },
  },
};
</script>

<style lang="scss" scoped>
</style>