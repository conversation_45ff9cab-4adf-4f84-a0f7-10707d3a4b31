<template>
  <view>
    <BaseNavbar title="设备领取" />
    <view class="bindHotelCard">
      <view class="deviceCode">
        <view>设备编号</view>
        <view>{{ device_sn ? device_sn : "" }}</view>
      </view>
      <view class="hotelName">
        <view>领取状态</view>
        <view>{{ isActive ? "已领取" : "等待领取" }}</view>
      </view>
    </view>
    <view class="bind-btn">
      <BaseButton @onClick="activeMachine">领 取</BaseButton>
    </view>
    <view class="tips" v-if="tipsList">
      <view class="tips-title">温馨提示</view>
      <view class="tips-box" >
        {{ tipsList }}
      </view>
    </view>
  </view>
</template>

<script>
import BaseButton from "../../components/base/BaseButton.vue";
import BaseNavbar from "@/components/base/BaseNavbar.vue";
export default {
  components: { BaseNavbar, BaseButton },
  data() {
    return {
      fromData: "", //来源
      isFromIndexScan: false, //首页扫码来的
      mid: "",
      isActive: false,
      device_sn: "",
      tipsList:''
    };
  },
  methods: {
    async getActive() {
      let data = {
        mid: this.mid,
      };
      let rtn = await this.$u.api.isActive(data);
      this.isActive = rtn?.is_active;
      this.device_sn = rtn?.device_sn;

      this.tipsList=rtn?.tip
      // this.tipsList='该设备用|wfaoewfwe'
    },
    async activeMachine() {
      try {
        //领取设备
        let data = {
          device_sn: this.device_sn,
        };

        await this.$u.api.activeMachine(data)
        this.isShowSuccess("领取成功", 0, this.getActive());
      }catch(error){
        console.log('错误信息', error)
      }
    
    },
  },
  onLoad(opt) {
    if (opt?.from) {
      this.fromData = opt.from;
      this.isFromIndexScan = true;
    }
    this.mid = opt?.mid;
    this.getActive();
  },
};
</script>

<style lang="scss" scoped>
.tips {
  padding: 30rpx;
  font-size: 40rpx;

  &-box {
    line-height: 1.8;
  }
}
.tips-title {
  margin-bottom: 20rpx;
  color: red;
}
.bindHotelCard {
  margin-top: 20rpx;
  height: 200rpx;
  width: 100%;
  background: white;
  font-size: 26rpx;
  color: $textBlack;

  .deviceCode,
  .hotelName {
    height: 100rpx;
    display: flex;
    flex-direction: row;
    align-content: center;
    align-items: center;
    margin-left: 35rpx;
    border-bottom: 1rpx solid #e5e5e5;
  }

  .deviceCode view:first-child,
  .hotelName view:first-child {
    margin-right: 30rpx;
  }
}

.bind-btn {
  padding: 0 50rpx;
  margin-top: 100rpx;
}
</style>