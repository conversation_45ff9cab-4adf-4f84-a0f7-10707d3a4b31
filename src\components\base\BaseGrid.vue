<template>
  <u-grid :border="border" :col="col" :align="align">
    <u-grid-item class="u-grid-item-box" v-for="(item, i) in gridList" :key="i" @click="onClick(item.href)">
      <image :style="{ width, height: width }" :src="item.icon" />
      <view :style="{ marginTop }" class="name">{{ item.name }}</view>
    </u-grid-item>
  </u-grid>
</template>

<script>
export default {
  name: "BaseGrid",
  props: {
    border: {
      type: Boolean,
      default: false,
    },
    col: {
      type: Number,
      default: 4,
    },
    gridList: {
      type: Array,
      default: function () {
                return [];
            }
    },
    width: {
      type: String,
    },
    marginTop: {
      type: String,
      default: "20rpx",
    },
  },
  data() {
    return {
      align: "left",
    };
  },
  methods: {
    onClick(url) {
      this.$emit('onClick',url)
      // uni.navigateTo({ url });
    },
  },
  options: { styleIsolation: "shared" }, //组件必须加,才能修改内部样式
};
</script>

<style lang="scss" scoped>
.name {
  font-size: $font-size-base;
  color: $textBlack;
}

.u-grid-item-box {
  ::v-deep .u-grid-item {
    margin-top: 50rpx;
  }

  ::v-deep .u-grid-item-box {
    padding: 0 !important;
  }
}
</style>