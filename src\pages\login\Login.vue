<template>
  <view>
    <BaseNavbar title="登录" :isLeft="false" :isShowBack="false" />
    <image class="topImg" src="@/static/img/login-top.png" />
    <view class="logoBox flexRowAllCenter">
      <image class="logoImg" :src="logoImg" />
    </view>
    <view class="form">
      <view class="input_box">
        <BaseInput v-model="userName" @handleBlur="handleBlur" @onFocus="onFocus" placeholder="请输入账号"
          :leftImg="userLeftImg" />
        <view class="input_chunk" v-if="vLoginList.userName && isInputShow" @click="inputChange">
          <view>
            {{ vLoginList.userName }}
          </view>
        </view>
      </view>
      <BaseInput class="pasword" v-model="passWord" placeholder="请输入密码" :leftImg="passLeftImg" type="password" />
      <BaseInput :selectValue="vSelectSysObj.label" :index="vSelectSysObj.value" placeholder="请选择系统" type="select"
        :list="sysList||[]" @confirm="confirm" :leftImg="systemLeftImg" />
    </view>
    <view class="btnDefault">
      <BaseButton type="primary" @onClick="login">登 录</BaseButton>
    </view>
    <!-- 隐私政策 -->
    <view class="privacyAgree flexRowBetween">
      <view class="privacyRadio">
        <!-- <BaseCheck :checked.sync="checked" />
        <view class="agreement" @click="checked = !checked">
          <text>我已同意</text>
          <text class="agreement_box" @click.stop="gotoWebView(vSiteConfig.site_info.user_policy_url)">
            隐私政策
          </text>
          和
          <text class="agreement_box" @click.stop="gotoWebView(vSiteConfig.site_info.user_agreement_url)">用户协议</text>
        </view> -->
      </view>
      <view class="forgetPass" @click="goForget">忘记密码？</view>
    </view>
    <view class="copyright flexRowAllCenter" :style="{ bottom: vIphoneXBottomHeight + 20 + 'rpx' }">
      <text>Copyright@{{ vSiteConfig.site_info.site_name }}</text>
    </view>
  </view>
</template>

<script>
import BaseButton from '@/components/base/BaseButton.vue'
import BaseInput from '@/components/base/BaseInput.vue'
import BaseNavbar from "@/components/base/BaseNavbar.vue";
import { mapMutations } from 'vuex'
export default {
  components: { BaseButton, BaseInput, BaseNavbar },
  data() {
    return {
      inputHeight: 80,
      placeholderStyle: 'color: #999;',
      show: false,
      longon: true,
      userName: '',
      passWord: '',
      checked: true,
      userLeftImg: {
        show: true,
        style: 'width:40rpx;height:44rpx',
        src: require('@/static/img/icon/userName-icon.png'),
      },
      passLeftImg: {
        show: true,
        style: 'width:38rpx;height:46rpx',
        src: require('@/static/img/icon/passWord-icon.png'),
      },
      systemLeftImg: {
        show: true,
        style: 'width:46rpx;height:42rpx',
        src: require('@/static/img/icon/system-icon.png'),
      },
      isInputShow: false
    }
  },
  computed: {
    logoImg() {
      return this.vSiteConfig?.site_info?.site_logo || ''
    },
  },
  methods: {
    ...mapMutations(['setList', 'addItem', 'updateItem', 'deleteItem']),
    confirm(e) {
      this.$u.vuex('vSelectSysObj', e[0])
    },
    navigator(b = false) {
      uni.switchTab({
        url: '/pages/index/index',
        success: function (e) {
          let page = getCurrentPages().pop()
          if (!page) return;
          if (b) {
            uni.$emit('login', { from: 'login' });
          }
        },
      })

    },

    async login() {
      try {
        if (!this.checked) {
          this.$u.toast('请阅读并同意隐私协议~');
          return;
        }
        const { userName, passWord } = this;
        if (!userName || !passWord) {
          this.$u.toast('请填写用户名或密码~');
          return;
        }
        const data = {
          username: userName,
          password: passWord,
          device_type: 'wxapp',
        };
        const res = await this.$u.api.login(data);
        const rtnData = { ...res, userName, passWord };
        const loginName = { userName, passWord };
        this.$u.vuex('vLoginList', loginName);
        this.$u.vuex('vUserInfo', rtnData);
        this.$u.vuex('vToken', rtnData.token);
        this.addItem(rtnData);
        this.navigator(true);
      } catch (err) {
        console.log('错误信息', err);
        // this.$u.toast('登录失败，请重试~');
      }
    },
    goForget() {
      uni.navigateTo({ url: '/pages/login/Forget' })
    },

    gotoWebView(url) {
      uni.navigateTo({
        url: '/pagesC/webView/WebView?url=' + encodeURIComponent(url),
      })
    },
    onFocus() {
      this.isInputShow = true
    },
    handleBlur() {
      setTimeout(() => {
        this.isInputShow = false
      }, 100)
    },
    inputChange() {
      this.userName = this.vLoginList.userName
      this.isInputShow = false
    }
  },
  onLoad(opt) {
    // 从参数中获取来源
    const from = opt?.from || '';

    // 根据不同的来源执行不同的逻辑
    switch (from) {
      case 'outLogin':
        this.userName = this.vUserInfo.userName;
        this.passWord = this.vUserInfo.passWord;
        this.checked = true;
        this.$u.vuex('vUserInfo', {});
        this.$u.vuex('vToken', '');
        break;
      case 'user':
        this.userName = opt?.userName || '';
        this.passWord = opt?.passWord || '';
        break;
      case 'userAdd':
        this.userName = '';
        this.passWord = '';
        this.checked = true;
        this.$u.vuex('vUserInfo', {});
        this.$u.vuex('vToken', '');
        break;
      case 'userUpdata':
        this.userName = this.vUserInfo.userName;
        this.passWord = this.vUserInfo.passWord;
        this.checked = true;
        this.$u.vuex('vToken', '');
        this.login();
        break;
      default:
        break;
    }

    // 如果有 vToken，则执行 navigator 函数
    if (this.vToken) {
      this.navigator();
    }

    /* #ifdef MP-WEIXIN */
    wx.showShareMenu({
      withShareTicket: true,
      menus: ['shareAppMessage', 'shareTimeline'],
    });
    /* #endif */
  },
  onReady() {
    this.$forceUpdate()
  },
}
</script>

<style lang="scss" scoped>
.pasword ::v-deep .u-icon__icon {
  font-size: 65rpx !important;
}

.topImg {
  width: 100%;
  height: 340rpx;
}

.logoBox {
  width: 188rpx;
  height: 188rpx;
  margin: 0 auto;
  background-color: white;
  transform: translateY(-50%);
  box-shadow: 0px 4px 8px 0px rgba(0, 0, 0, 0.1);
  border-radius: 10px;
  padding: 20rpx;

  .logoImg {
    width: 100%;
    height: 100%;
  }
}

.input_box {
  position: relative;

  // border: 2rpx solid red;
  .input_chunk {
    position: absolute;
    top: 90rpx;
    z-index: 9999;
    width: 100%;
    left: 0;
    border: 2rpx solid rgb(229, 229, 229);
    line-height: 80rpx;
    background-color: white;
    padding: 0 20rpx;
  }
}

.form {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding: 0 30rpx;
  box-sizing: border-box;
  height: 340rpx;
  margin-bottom: 80rpx;
  //::v-deep .u-input {
  //   background: #f2f2f2;
  //   border-radius: 8rpx;
  //   padding: 0 28rpx !important;
  //   .u-input__input {
  //   }
  // }

}

.btnDefault {
  margin: 80rpx 0 32rpx;
  padding: 0 30rpx;
}

.privacyAgree {
  box-sizing: border-box;
  padding: 0 30rpx;

  .privacyRadio {
    display: flex;
    align-items: flex-end;

    ::v-deep .u-checkbox__label {
      margin-right: 0;
    }

    .agreement {
      color: $textBlack;
      font-size: $font-size-base;

      &_box {
        color: $themeColor;
        margin: 0 4rpx;
      }
    }
  }

  .forgetPass {
    color: $themeColor;
    font-size: $font-size-base;
  }
}

.copyright {
  width: 100%;
  position: fixed;
  bottom: 20rpx;
  color: $textGray;
  font-size: $font-size-xsmall;
}
</style>
