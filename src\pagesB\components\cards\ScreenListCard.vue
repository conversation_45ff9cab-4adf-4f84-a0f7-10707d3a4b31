<template>
  <view class="card" @click="goDetails">
    <view class="card_info">
      <view class="content">
        <view class="content-box">
          <view>屏幕编号：</view>
          <view>{{ info.screen_sn }}</view>
        </view>
      </view>
      <view class="content">
        <view class="content-box">
          <view>设备编号：</view>
          <view>{{ info.device_sn }}</view>
        </view>
      </view>

      <view class="content">
        <view class="content-box">
          <view>{{ vPointName }}名称：</view>
          <view class="textMaxTwoLine">{{ info.hotelName || "" }}</view>
        </view>
      </view>
      <view class="content">
        <view class="content-box">
          <view>广告区：</view>
          <view>A区</view>
        </view>
        <view class="online">
          <block v-if="info.um_status === 'online'">
            <image class="online_icon" src="@/pagesB/static/img/icon/device_online_icon.png" />
            <view class="theme-color">在线</view>
          </block>
          <block v-else>
            <image class="online_icon" src="@/pagesB/static/img/icon/device_unline_icon.png" />
            <view class="text-warn">离线</view>
          </block>
        </view>
      </view>
      <view class="content">
        <view class="content-box">
          <view>拥有者：</view>
          <view>曹操查</view>
        </view>
      </view>
      <view class="content">
        <view class="bind">
          <block v-if="info.device_sn">
            <image class="bind_icon" src="@/pagesB/static/img/icon/isBind_icon.png" />
            <view class="theme-color">已绑定</view>
          </block>
          <block v-else>
            <image class="bind_icon" src="@/pagesB/static/img/icon/noBind_icon.png" />
            <view class="text-warn">未绑定</view>
          </block>
        </view>
        <!-- <view class="theme-color" @click.stop="goDetails">查看详情</view> -->
      </view>
    </view>
    <view class="card-bottom">
      <view class="btn">
        <BaseButton type="default" shape="circle" @onClick="isShowModalBind = true">绑定设备</BaseButton>
      </view>
      <view class="btn">
        <BaseButton type="default" shape="circle" @onClick="uploadAd">上传广告</BaseButton>
      </view>
      <view class="btn">
        <BaseButton type="default" shape="circle" @onClick="isShowModal = true">
          清空广告
        </BaseButton>
      </view>
      <view class="btn">
        <BaseButton type="default" shape="circle" @onClick="upScanCode">
          推送二维码
        </BaseButton>
      </view>
      <!--占位块-->
      <view class="btn-block" v-for="item in 2" :key="item"> </view>
    </view>
    <!--提示是否清空广告-->
    <BaseModal :show.sync="isShowModal" @confirm="emptyAd" title="温馨提示" content="您该设备的广告将会清空，此操作不可恢复，是否继续清空？" />

    <!--绑定设备弹框-->
    <BaseModal :show.sync="isShowModalBind" @confirm="confirmBind" title="请选择绑定的设备">
      <view slot="default">
        <BaseInput @onClick="goBindDevice" :disabled="true" v-model="bindDeviceSn" placeholder="请选择绑定的设备" />
      </view>
    </BaseModal>
  </view>
</template>

<script>
import BaseButton from "@/components/base/BaseButton.vue";
import BaseModal from "@/components/base/BaseModal.vue";
import BaseInput from "@/components/base/BaseInput.vue";
export default {
  name: "ScreenListCard",
  props: {
    info: { type: Object, default: {} },
    bindDeviceSn: { type: String, default: "" },
  },
  data() {
    return { isShowModal: false, isShowModalBind: false };
  },
  methods: {
    goBindDevice() {
      uni.navigateTo({
        url: "/pagesB/device/DeviceList?from=screen_bind_device",
      });
    },
    goDetails() {
      // uni.navigateTo({ url: "/pagesB/screen/ScreenDetails" });
    },
    //推送二维码
    upScanCode() {
      let data = {
        screen_sn: this.info.screen_sn,
        device_sn: this.info.device_sn,
      };
      this.$u.api.updateQrWx(data).then((res) => {
        this.isShowSuccess("推送二维码成功");
      })
        .catch((err => {
          console.log('错误信息', err)
        }))
    },
    //清空广告
    emptyAd() {
      let data = {
        screen_sn: this.info.screen_sn,
        place: "All",
        mid: this.info.id,
      };
      this.$u.api.delPlace(data).then((res) => {
        this.isShowSuccess("广告清空成功");
      })
        .catch((err => {
          console.log('错误信息', err)
        }))
    },
    //去上传广告页面
    uploadAd() {
      uni.navigateTo({
        url: `/pagesB/screen/ScreenUploadAd?mid=${this.info.id}&device_sn=${this.info.device_sn}&screen_sn=${this.info.screen_sn}`,
      });
    },
    //确认绑定
    confirmBind() {
      this.$emit("confirmBind", this.info.screen_sn);
    },
  },
  components: { BaseButton, BaseModal, BaseInput },
};
</script>

<style lang="scss" scoped>
.textMaxTwoLine {
  overflow-wrap: normal;
}

.card {
  &_info {
    padding: 20rpx;

    .content {
      display: flex;
      justify-content: space-between;
      line-height: 1.8;

      &-box {
        display: flex;

        >view {
          font-size: $font-size-base;
          color: $textDarkGray;

          &:first-child {
            flex-shrink: 0;
            width: 130rpx;
            text-align: right;
          }

          &:last-child {
            color: $textBlack;
          }
        }
      }
    }
  }

  &-bottom {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    border-top: 2rpx solid $dividerColor;
    padding: 20rpx;

    .btn {
      width: 30%;

      &:nth-child(n + 4) {
        margin-top: 20rpx;
      }
    }

    .btn-block {
      width: 30%;
    }
  }
}
</style>