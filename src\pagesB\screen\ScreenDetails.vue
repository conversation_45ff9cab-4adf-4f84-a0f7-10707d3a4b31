<template>
  <view class="screen_details">
    <BaseNavbar :title="title" />
    <view class="screen_top">
      <image class="screen_top_img" src="" />
    </view>
    <view class="screen_info">
      <view class="name"
        >点位名称点位名称点位名称点位名称点位名称点位名称 点位名称</view
      >
      <view>
        <view class="bind">
          <block v-if="Math.random() > 0.5">
            <image
              class="bind_icon"
              src="@/pagesB/static/img/icon/isBind_icon.png"
            />
            <view class="theme-color">已绑定</view>
          </block>
          <block v-else>
            <image
              class="bind_icon"
              src="@/pagesB/static/img/icon/noBind_icon.png"
            />
            <view class="text-warn">未绑定</view>
          </block>
        </view>
      </view>
      <view>
        <view>设备编号</view>
        <view>0000000141616</view>
      </view>
      <view>
        <view>屏幕状态</view>
        <view>在线</view>
      </view>
      <view>
        <view>广告区</view>
        <view>B区</view>
      </view>
      <view>
        <view>拥有者</view>
        <view>曹操查</view>
      </view>
      <view>
        <view>开屏时间</view>
        <view>2021-10-18 09：00：00</view>
      </view>
      <view>
        <view>闭屏时间</view>
        <view>2021-10-18 09：00：00</view>
      </view>
      <view>
        <view>详细位置</view>
        <view>湖北省武汉市洪山区那那那</view>
      </view>
    </view>
    <view class="btn">
      <view><BaseButton type="primary">导航</BaseButton></view>
      <view
        ><BaseButton type="default" @onClick="goEdit">编辑</BaseButton></view
      >
    </view>
  </view>
</template>

<script>
import BaseNavbar from "@/components/base/BaseNavbar.vue";
import BaseButton from "@/components/base/BaseButton.vue";

export default {
  components: { BaseNavbar, BaseButton },
  data() {
    return {
      title: "屏幕详情",
    };
  },
  methods: {
    goEdit() {
      uni.navigateTo({ url: "/pagesB/screen/ScreenAdd?from=edit" });
    },
  },
};
</script>

<style lang="scss" scoped>
.screen_details {
  padding: 30rpx;
  .screen_top {
    height: 240rpx;
    border-radius: $cardRadius;
    &_img {
      width: 100%;
      height: 100%;
      background-color: pink;
      border-radius: $cardRadius;
    }
  }
  .screen_info {
    font-size: $font-size-base;
    color: $textDarkGray;
    .name {
      color: $textBlack;
      font-size: $font-size-middle;
      font-weight: bold;
    }
    > view {
      display: flex;
      justify-content: space-between;
      margin-top: 30rpx;
      > view {
        &:last-child {
          color: $textBlack;
        }
      }
    }
  }
  .btn {
    margin-top: 100rpx;
    > view {
      margin-bottom: 40rpx;
    }
  }
}
</style>