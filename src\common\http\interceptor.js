const install = (Vue, vm) => {
    Vue.prototype.$u.http.setConfig({
        baseUrl: '', // 请求的本域名 //到api.js进行赋值修改
        loadingText: '拼命加载中~', // 请求loading中的文字提示
        // 设置为json，返回后会对数据进行一次JSON.parse()
        dataType: 'json',
        showLoading: true, // 是否显示请求中的loading
        loadingTime: 200, // 在此时间内，请求还没回来的话，就显示加载中动画，单位ms
        originalData: false, // 是否在拦截器中返回服务端的原始数据
        loadingMask: true, // 展示loading的时候，是否给一个透明的蒙层，防止触摸穿透
        // 配置请求头信息
        header: {
            'content-type': 'application/json;charset=UTF-8'
        },
        timeout: 10000, // 超时时间
    });

    // 请求拦截，配置Token等参数
    Vue.prototype.$u.http.interceptor.request = (config) => {
        let token = vm.vUserInfo.token
        if (token) {
            config.header = {
                "XX-Token": token,
                'XX-Device-Type': 'wxapp',
                'XX-Api-Version': '1.0.0',
                'XX-Wxapp-AppId': vm.vAppId,
                'XX-App-ID': vm.vAppId,
            }
            if (config.url.includes("/waapi/public/login") || config.url.includes("/api/machine_renew/Prepay")) {
                config.header["XX-App-Type"] = 'wx'

            }

        }
        return config;
        // 如果return一个false值，则会取消本次请求
        // if(config.url == '/user/rest') return false; // 取消某次请求
    }

    // 响应拦截，判断状态码是否通过
    Vue.prototype.$u.http.interceptor.response = (res) => {

        if (res.code == 1) {
            return res.data;
        } else {
            uni.showToast({
                title: res.msg || "请求失败~",
                icon: 'none',
                duration: 3000
            })
            if (res.code == 10001) {
                uni.reLaunch({
                    url: '/pages/login/Login?from=outLogin',
                });
            }
            return false;
        }
    }
}

export default {
    install
}