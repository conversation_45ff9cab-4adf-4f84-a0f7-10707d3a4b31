<template>
  <view>
    <BaseNavbar :title="title" />
    <view v-if="fromData == 'changePassword'" class="content">
      <view>
        <view>输入原密码</view>
        <view
          ><BaseInput placeholder="请输入原密码" v-model="oldPassword"
        /></view>
      </view>
      <view>
        <view>输入新密码</view>
        <view
          ><BaseInput
            type="password"
            placeholder="输入新密码"
            v-model="newPassword"
        /></view>
      </view>
      <view>
        <view>请再次输入新密码</view>
        <view>
          <BaseInput
            type="password"
            placeholder="请再次输入新密码"
            v-model="newPasswordAgain"
          />
        </view>
      </view>
    </view>
    <view v-if="fromData == 'changePhone'" class="content">
      <view>
        <view>绑定手机号</view>
        <view class="auth">
          <BaseInput
            placeholder="请输入手机号"
            v-model="authPhone"
          />
          <!-- <button
            v-if="!authPhone"
            class="phone-btn"
            open-type="getPhoneNumber"
            @getphonenumber="getPhoneNumber"
          ></button> -->
        </view>
      </view>
    </view>
    <view class="btn">
      <BaseButton type="primary" @onClick="confirmChange">{{
        title
      }}</BaseButton>
    </view>
  </view>
</template>

<script>
import BaseButton from "@/components/base/BaseButton.vue";
import BaseInput from "@/components/base/BaseInput.vue";
import BaseNavbar from "@/components/base/BaseNavbar.vue";
export default {
  components: { BaseNavbar, BaseInput, BaseButton },
  data() {
    return {
      title: "修改密码",
      fromData: "",
      oldPassword: "",
      newPassword: "",
      newPasswordAgain: "",
      oldPhone: "",
      code: "",
      newPhone: "",
      authPhone: "", //授权手机号
    };
  },
  methods: {
    confirmChange() {
      if (this.fromData == "changePassword") {
        let data = {
          old_password: this.oldPassword,
          password: this.newPassword,
          re_password: this.newPasswordAgain,
        };
        this.$u.api.modifyPwd(data).then((res) => {
          let rtnData = this.vUserInfo;
          rtnData.passWord = this.newPassword;
          this.$u.vuex("vUserInfo", rtnData);
          this.isShowSuccess("修改成功", 1);
        });
      } else if (this.fromData == "changePhone") {
        if (this.vUserInfo?.user?.mobile)
          return this.isShowErr("您已经绑定过手机号了~");
        if (!this.authPhone) return this.isShowErr("请先获取授权手机号~");
        let data = { mobile: this.authPhone };
        this.$u.api.saveUserMobile(data).then((res) => {
          let newUserInfo = this.vUserInfo;
          newUserInfo.user.mobile = this.authPhone;
          this.$u.vuex("vUserInfo", newUserInfo);
          this.isShowSuccess("绑定成功", 1);
        });
      }
    },
    // getPhoneNumber(e) {
    //   uni.showLoading({
    //     title: "请等待~",
    //     mask: false,
    //   });
    //   const { code, encryptedData, iv } = e.detail;
    //   console.log("🚀 ~ code", code);

    //   if (e.detail.errMsg == "getPhoneNumber:ok") {
    //     this.login(code, encryptedData, iv);
    //   } else {
    //     this.isShowErr("获取手机号授权失败，请重新获取~");
    //   }
    // },
    login(code, encryptedData, iv) {
      uni.hideLoading();
      let data = {
        code,
      };
      this.$u.api.getPhoneNumber(data).then((res) => {
        if (res) {
          this.authPhone = res;
          this.isShowSuccess("授权手机号成功");
        } else {
          this.isShowErr("获取手机号为空,请重试~");
        }
      });
    },
  },
  onLoad(opt) {
    if (opt.from) {
      this.fromData = opt.from;
      if (this.fromData == "changePassword") {
        this.title = "修改密码";
      } else if (this.fromData == "changePhone") {
        this.title = "绑定手机号";
        this.authPhone = this.vUserInfo?.user?.mobile || "";
      }
    }
  },
};
</script>

<style lang="scss" scoped>
.content {
  padding: 30rpx;
  > view {
    margin-bottom: 50rpx;
    > view:first-child {
      color: $textBlack;
      font-size: $font-size-middle;
      font-weight: bold;
      margin-bottom: 20rpx;
    }
    .code {
      width: 100%;
      margin-right: 30rpx;
    }
    .auth {
      position: relative;
      .phone-btn {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba($color: #fff, $alpha: 0.1);
        z-index: 9999;
      }
    }
  }
}
.btn {
  padding: 0 30rpx;
}
</style>