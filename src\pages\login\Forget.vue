<template>
  <view class="content">
    
    <BaseNavbar title="找回密码" />
    <view class="titleBox flexColumnAllCenter">
      <view class="title"
        >请联系{{ vSiteConfig.site_info.site_name }}官方客服</view
      >
      <view class="tel" @click="callPhone">{{ vTel }}</view>
    </view>

    <view class="qrCode flexColumnAllCenter">
      <image class="qrCodeImg" :src="vSiteConfig.site_info.wechat_qrcode" />
      <view class="follow">关注公众号，获取更多资讯</view>
    </view>
  </view>
</template>

<script>
import BaseNavbar from "@/components/base/BaseNavbar.vue";
export default {
  components: { BaseNavbar },
  methods: {
    callPhone() {
      uni.makePhoneCall({
        phoneNumber: this.vTel,
        success: (result) => {},
        fail: (error) => {},
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.content {
  height: 100vh;
  display: flex;
  flex-direction: column;
  align-content: center;
  .titleBox {
    color: $textBlack;
    font-size: $font-size-xxxxlarge;
    font-weight: 700;
    margin-bottom: 164rpx;
    padding-top: 240rpx;
    .title {
      margin-bottom: 55rpx;
    }
    .tel {
      color: $themeColor;
    }
  }
  .qrCode {
    .qrCodeImg {
      width: 200rpx;
      height: 200rpx;
    }
    .follow {
      font-size: $font-size-small;
      color: $textBlack;
      margin-top: 20rpx;
    }
  }
}
</style>