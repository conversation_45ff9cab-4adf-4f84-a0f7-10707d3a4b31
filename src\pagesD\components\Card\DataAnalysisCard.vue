<template>
    <view class='card'>
        <view class="content">
            <block v-if="cardType === 'user'">
                <view class="content-box">
                    <view class="title">{{ vPointName }}：</view>
                    <view class="txt">{{ info.hotelName || '暂无' }}</view>
                </view>
                <view class="content-box" v-if="false">
                    <view class="title">设备数量：</view>
                    <view class="txt">{{ info.nums }}</view>
                </view>
                <view class="content-box" v-if="false">
                    <view class="title">{{vPointName}}地址：</view>
                    <view class="txt">{{ info.address || '暂无' }}</view>
                </view>
            </block>
            <block v-else-if="cardType === 'hotel'">
                <view class="content-box">
                    <view class="title">设备编号：</view>
                    <view class="txt">{{ info.device_sn || '暂无' }}</view>
                </view>
                <view class="content-box" v-if="false">
                    <view class="title">桌号：</view>
                    <view class="txt">{{ info.room_num }}</view>
                </view>
            </block>

            <view class="content-box profit">
                <view class="profit-box">
                    <view class="title">{{ cardType === 'user' ? vPointName : '设备' }}销售额：</view>
                    <view class="txt">{{ info.all_amount }} 元</view>
                </view>
                <view class="profit-details" @click="goDetails">明细</view>
            </view>
        </view>
    </view>
</template>
<script>
export default {
    props: {
        info: { type: Object, default: {} },
        cardType: { type: String, default: 'user' },

    },
    data() {
        return {

        };
    },

    methods: {
        goDetails() {
            let param = '';
            if (this.cardType == 'user') {

                param = `?cardType=hotel&hotel_id=${this.info.hotel_id}`
            } else if (this.cardType === 'hotel') {
                param = `?cardType=device&device_sn=${this.info.device_sn}`
            }
            uni.navigateTo({ url: `/pagesB/dataAnalysis/DataAnalysis${param}` })
        }
    },
    onLoad() {

    },
}
</script>


<style scoped  lang='scss'>
.card {
    padding: 20rpx;
}

.content {
    &-box {
        display: flex;
        font-size: $font-size-middle;

        .title {
            color: $textDarkGray;
            flex-shrink: 0;
        }

        .txt {
            color: $textBlack;
        }
    }

    .profit {
        justify-content: space-between;

        &-box {
            display: flex;
        }

        &-details {
            color: $themeComColor;
            font-weight: 700;
        }
    }
}
</style>