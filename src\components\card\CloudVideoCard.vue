<template>
  <view v-if="info.url">
    <view class="content flexRowBetween" @click="goDetails">
      <image class="img" :src="info.thumbnail || ''" />
      <view class="info flexColumnBetween">
        <view class="top textMaxTwoLine">{{ info.title }}</view>
        <view class="bottom flexRowBetween">
          <!-- <view class="type" v-if="info.post_keywords">{{
            info.post_keywords
          }}</view> -->
          <!-- <view v-else></view> -->
          <!-- <view class="not_yet" v-else>暂未开播</view> -->
          <view class="time">{{ getTime }}</view>
        </view>
      </view>
    </view>
    <u-line color="#e5e5e5" length="100vw" margin="30rpx 0 0" />
  </view>
</template>

<script>
import { timestampToTime } from "@/common/tools/utils.js";
import { navigateToReplenishList } from '@/wxutil/navgate.js'
export default {
  name: "CloudVideoCard",
  props: {
    info: {
      type: Object,
      default() {
        return {};
      },
    },
  },
  computed: {
    getTime() {
      return timestampToTime(this.info.time * 1000);
    },
  },
  methods: {
    async goDetails() {
      try {
        await navigateToReplenishList("/pagesC/webView/WebView?url=" + encodeURIComponent(this.info.url));
        // 导航成功后的代码
       
      } catch (err) {
        // 导航失败后的代码
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.content {
  height: 170rpx;
  margin-top: 30rpx;
}
.img {
  width: 260rpx;
  height: 100%;
  border-radius: $imgRadius;
  background-color: #f2f2f2;
}
.info {
  width: 400rpx;
  height: 100%;
  .top {
    color: $textBlack;
    font-size: $font-size-middle;
    font-weight: bold;
  }
  .bottom {
    color: $textDarkGray;
    font-size: $font-size-small;
    .type {
      padding: 8rpx 16rpx;
      color: $textWhite;
      background: linear-gradient(268deg, #206bc5 0%, #0eade2 99%);
      border-radius: 4px;
    }
    .not_yet {
      height: 50rpx;
      width: 130rpx;
      display: flex;
      justify-content: center;
      align-items: center;
      background: #ff8e49;
      border-radius: 4px;
      color: $textWhite;
    }
  }
}
</style>