<template>
  <view>
    <BaseNavbar title="优惠券管理" />
    <view class="top">
      <view class="hotel-img">
        <image class="img" :src="hotelInfo.img" />
        <view class="camera">
          <BaseUpload
            ref="BaseUpload"
            :maxCount="1"
            :customBtn="true"
            :auto="true"
            @onUpload="onUpload"
          >
            <BaseIcon slot="addBtn" name="camera" color="#0039f3" />
          </BaseUpload>
        </view>
      </view>
      <view class="coupon-info">
        <view class="info-item">
          <view class="info-item-num">
            {{ hotelInfo.write_off_num || 0 }}张
          </view>
          <view class="info-item-title">已核销</view>
        </view>
        <view class="info-item">
          <view class="info-item-num">{{ hotelInfo.total_num || 0 }}张</view>
          <view class="info-item-title">已领取</view>
        </view>
        <view class="info-item">
          <view class="info-item-num">
            {{ hotelInfo.no_receive_num || 0 }}张
          </view>
          <view class="info-item-title">待领取</view>
        </view>
      </view>
    </view>
    <view class="content">
      <view class="apply">
        <view class="apply-title">应用中心</view>
        <view class="apply-list">
          <view
            class="apply-list-item"
            v-for="item in setList"
            :key="item.name"
            @click="goPage(item.url)"
          >
            <image class="apply-list-item-img" :src="item.src" />
            <view class="apply-list-item-text">{{ item.name }}</view>
          </view>
        </view>
      </view>
      <view class="wifi">
        <view class="wifi-title">
          <BaseIcon name="wifi" size="32" color="#0039f3" />
          <view class="wifi-title-text">店铺WiFi</view>
        </view>
        <view class="wifi-line"></view>
        <view class="wifi-card">
          <view class="wifi-card-info">
            <view class="wifi-card-info-box">
              <view>{{ hotelInfo.hotelName }}</view>
              <view>wifi名称：{{ hotelInfo.wifi_name || '暂未设置' }}</view>
              <view>昨日有效连接0次</view>
              <view>昨日有效连接0次</view>
            </view>
            <image class="wifi-card-info-img" :src="hotelInfo.img_wifi" />
          </view>
          <view class="wifi-card-btn">
            <view class="btn-default" hover-class="btn-active">打印</view>
            <view
              class="btn-default"
              hover-class="btn-active"
              @click="
                goPage(
                  `/pagesB/coupon/CouponWifiShare?hotelName=${
                    hotelInfo.hotelName || ''
                  }`,
                )
              "
            >
              分享
            </view>
            <view
              class="btn-primary"
              hover-class="btn-active"
              @click="
                goPage(
                  `/pagesB/coupon/CouponWifi?id=${hotelInfo.id}&wifi_name=${
                    hotelInfo.wifi_name || ''
                  }&wifi_psd=${hotelInfo.wifi_psd || ''}&hotelName=${
                    hotelInfo.hotelName || ''
                  }`,
                )
              "
            >
              修改
            </view>
          </view>
        </view>
      </view>
    </view>
    <SafeBlock height="50" />
  </view>
</template>
<script>
import BaseButton from '../../components/base/BaseButton.vue'
import BaseIcon from '../../components/base/BaseIcon.vue'
import BaseNavbar from '@/components/base/BaseNavbar.vue'
import BaseUpload from '../../components/base/BaseUpload.vue'
import SafeBlock from '../../components/common/SafeBlock.vue'

export default {
  components: { BaseNavbar, BaseIcon, BaseUpload, SafeBlock, BaseButton },
  data() {
    return {
      titleStyle: {
        color: '#333',
        fontSize: '28rpx',
        fontWeight: 'bold',
      },

      setList: [
        {
          name: '优惠券',
          url: '/pagesB/coupon/CouponList',
          src: '/pagesB/static/img/coupon/hotel_coupon.png',
        },
        {
          name: '充值',
          url: '/pagesB/coupon/CouponSetMeal',
          src: '/pagesB/static/img/coupon/hotel_charge.png',
        },
        {
          name: '领取记录',
          url: '/pagesB/coupon/CouponRecord',
          src: '/pagesB/static/img/coupon/hotel_history.png',
        },
      ],
      hotelInfo: {},
    }
  },

  methods: {
    goPage(url) {
      uni.navigateTo({ url })
    },
    onUpload(item) {
      if (item[0]) {
        this.$refs.BaseUpload.remove(0)
        this.editHotelHandle({
          img: item[0],
        })
      }
    },
    async getCouponHotelManageInfoHandle() {
      this.hotelInfo = await getCouponHotelManageInfogetCouponHotelManageInfo()
      this.$u.vuex('vMiniCode', this.hotelInfo?.img_wifi)
    },
    async editHotelHandle(item) {
      let data = {
        id: this.hotelInfo.id,
        ...item,
      }
      await this.$u.api.editHotel(data)
      this.isShowSuccess('修改成功', 0, this.getCouponHotelManageInfoHandle())
    },
  },
  onLoad() {
    this.getCouponHotelManageInfoHandle()
  },
  onShow() {
    /*  #ifndef H5 */
    let pages = getCurrentPages()
    let currPage = pages[pages.length - 1] // 当前页
    if (currPage.data.isDoRefresh == true) {
      // 是否刷新
      currPage.data.isDoRefresh = false
      this.getCouponHotelManageInfoHandle()
    }
    /*#endif */
    /*  #ifdef H5 */
    if (this.vCurrPage.isDoRefresh == true) {
      // 是否刷新
      this.vCurrPage.isDoRefresh = false
      this.getCouponHotelManageInfoHandle()
    }
    /*#endif */
  },
}
</script>

<style lang="scss">
page {
  background-color: $pageBgColor;
}
</style>
<style scoped lang="scss">
.top {
  padding: 42rpx 36rpx 0;
  background-color: #fff;
  .hotel-img {
    position: relative;
    height: 300rpx;
    margin-bottom: 42rpx;
    .img {
      width: 100%;
      height: 100%;
      background-color: pink;
      border-radius: 10rpx;
    }
    .camera {
      display: flex;
      justify-content: center;
      align-items: center;
      position: absolute;
      right: 12rpx;
      bottom: 12rpx;
      width: 68rpx;
      height: 68rpx;
      border-radius: 50%;
      background-color: rgba($color: #fff, $alpha: 0.5);
    }
  }
  .coupon-info {
    display: flex;
    justify-content: space-around;
    align-items: center;
    padding: 28rpx 0;
    .info-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      color: #333;

      &-num {
        font-size: 30rpx;
        font-weight: 700;
      }
      &-title {
        font-size: 22rpx;
        margin-top: 24rpx;
      }
    }
  }
}
.content {
  padding: 0 36rpx;
  .apply {
    margin-top: 34rpx;
    &-title {
      color: #333;
      font-size: 32rpx;
      margin-bottom: 20rpx;
      font-weight: 700;
    }
    &-list {
      display: flex;
      justify-content: space-around;
      align-items: center;
      border-radius: 20rpx;
      padding: 30rpx 0;
      background-color: #fff;
      &-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        &-img {
          width: 80rpx;
          height: 80rpx;
        }
        &-text {
          color: #333;
          font-size: 20rpx;
          margin-top: 10rpx;
        }
      }
    }
  }
  .wifi {
    margin-top: 56rpx;
    &-title {
      display: flex;
      align-items: flex-end;
      color: #333;
      font-size: 32rpx;
      font-weight: 700;
      &-text {
        margin-left: 10rpx;
      }
    }
    &-line {
      height: 2rpx;
      background-color: #bdbdbd;
      margin: 20rpx 0;
    }
    &-card {
      padding: 46rpx 26rpx 26rpx 46rpx;
      border-radius: 20rpx;
      background-color: #fff;
      &-info {
        display: flex;
        justify-content: space-between;
        align-items: flex-end;
        &-img {
          flex-shrink: 0;
          width: 162rpx;
          height: 162rpx;
          border-radius: 50%;
        }
        &-box {
          color: #5a5a5a;
          font-size: 28rpx;
          :nth-child(1) {
            font-weight: 700;
          }
          :nth-child(3) {
            margin: 48rpx 0 20rpx;
          }
        }
      }
      &-btn {
        display: flex;
        justify-content: space-around;
        margin-top: 50rpx;
        > view {
          padding: 20rpx 60rpx;
          font-size: 32rpx;
          border: 2rpx solid #0039f3;
        }
        .btn-default {
          color: #0039f3;
        }
        .btn-primary {
          color: #fff;
          background-color: #0039f3;
        }
        .btn-active {
          opacity: 0.8;
        }
      }
    }
  }
}
</style>
