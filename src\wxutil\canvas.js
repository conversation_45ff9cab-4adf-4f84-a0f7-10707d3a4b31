/**
 * @description: 选择手机图片
 * @param {type} album	从相册选图	
 * @param {type} camera	使用相机	
 * @return {type} 
 */
export function chooseImage() {
    return new Promise((resolve, reject) => {
        wx.chooseImage({
            count: 1,
            sizeType: ['original', 'compressed'],
            sourceType: ['album', 'camera'],
            success(res) {
                // // tempFilePath可以作为img标签的src属性显示图片
                const tempFilePaths = res.tempFilePaths[0]
                resolve(tempFilePaths)
            }
        })
    })
}
/**
 * @description: 在新页面中全屏预览图片。预览的过程中用户可以进行保存图片、发送给朋友等操作。
 * @param {type} current	前显示图片的http链接	
 * @param {type} urls	需要预览的图片http链接列表	
 * @return {type} 
 */
export function chooseImg(current, urls) {
    wx.previewImage({
        current: current, // 当前显示图片的http链接
        urls: urls // 需要预览的图片http链接列表
    })
}
/**
* @description: 绘制正方形（可以定义圆角），并且有图片地址的话填充图片
* @param {CanvasContext} ctx canvas上下文
elementOpt = {
    top:0,//位置Y
    left: 0,//位置X
    width: 0,//宽度
    height: 0,//高度
    radius: 0,//圆角
    rotate: 0,//旋转
    path: '',//图片地址
    color: '#333',//背景颜色
    shape:true,//是否是圆
}
*/

export function drawSquarePic(ctx, elementOpt) {
    let { left = 0, top = 0, width = 0, height = 0, radius = 0, rotate = 0, path = '', color = '#fff', shape } = elementOpt;
    let x = left, y = top, w = width, h = height, r = radius, url = path
    ctx.save()
    ctx.beginPath()
    if (!shape) {
        // 方式一
        // ctx.rect(x, y, width, height);
        // 方式二
        if (r > 0) {
            ctx.setFillStyle('transparent');
            //判断传入的圆角,圆角不能大于图形最大范围
            let maxR = w > h ? h / 2 : w / 2
            r = r > maxR ? maxR : r
        }
        //左上角
        ctx.arc(x + r, y + r, r, Math.PI, Math.PI * 1.5)
        ctx.moveTo(x + r, y)
        ctx.lineTo(x + w - r, y)
        ctx.lineTo(x + w, y + r)
        //右上角
        ctx.arc(x + w - r, y + r, r, Math.PI * 1.5, Math.PI * 2)
        ctx.lineTo(x + w, y + h - r)
        ctx.lineTo(x + w - r, y + h)
        //右下角
        ctx.arc(x + w - r, y + h - r, r, 0, Math.PI * 0.5)
        ctx.lineTo(x + r, y + h)
        ctx.lineTo(x, y + h - r)
        //左下角
        ctx.arc(x + r, y + h - r, r, Math.PI * 0.5, Math.PI)
        ctx.lineTo(x, y + r)
        ctx.lineTo(x + r, y)
        ctx.fill()
    } else {
        //先画个圆   前两个参数确定了圆心 （x,y） 坐标  第三个参数是圆的半径  四参数是绘图方向  默认是false，即顺时针
        let _width = width / 2 + x;
        let _height = height / 2 + y;
        let _r = width / 2;
        ctx.arc(_width, _height, _r, 0, Math.PI * 2);
    }
    // 剪切，剪切之后的绘画绘制剪切区域内进行，需要save与restore 这个很重要 不然没办法保存
    ctx.clip()
    // 设置旋转中心
    let offsetX = x + Number(width) / 2;
    let offsetY = y + Number(height) / 2;
    ctx.translate(offsetX, offsetY);
    let degrees = rotate ? Number(rotate) % 360 : 0;
    ctx.rotate((degrees * Math.PI) / 180);
    // 绘制图片
    return new Promise((resolve, reject) => {
        if (url) {
            console.log("🚀 ~ url", url)

            wx.getImageInfo({
                src: url,
                success(res) {
                    console.log("🚀 ~ res", res)

                    ctx.drawImage(res.path, x - offsetX, y - offsetY, w, h)
                    ctx.closePath()
                    ctx.restore()//恢复之前被切割的canvas，否则切割之外的就没办法用
                    ctx.draw(true)
                    resolve()
                },
                fail(res) {
                    console.log("fail -> res", res)
                    uni.showToast({
                        title: "图片下载异常",
                        duration: 2000,
                        icon: "none"
                    })
                }
            })
        } else {
            ctx.draw(true)
            resolve()
        }
    })
}

/**
 * @description: 获取设备信息
 * @param {type} 
 * @return {type} 
 * @author: hch
 */
export function getSystem() {
    let system = wx.getSystemInfoSync()
    let scale = system.windowWidth / 375 //按照苹果留 375*667比例 其他型号手机等比例缩放 显示
    return { w: system.windowWidth, h: system.windowHeight, scale: scale }
}

/**
 * 
 * @param {*} ctx ctx实例
 * @param {*} textOptions 文本对象
 * @returns 
textOptions = {
    id: 3,//id
    type: "text",//类型
    top: 160,//位置Y
    left: 105,//位置X
    width: 200,//宽度
    height: 50,//高度
    text: "向你推荐了一件好物，请查收",//文本
    size: 28,//字体大小
    color: "#000",//字体颜色
    active: false,//是否选中
    textAlign: "left",//对齐方式 可选值 'top'、'bottom'、'middle'、'normal'
    fontWeight: "normal",//字体加粗 
} */
export function drawTextReturnH(ctx, textOptions) {
    let { text, left, top, width, size, color = '#333', textAlign, fontWeight = "400", textBaseline = 'top' } = textOptions
    ctx.setTextBaseline(textBaseline);//设置文字基线
    ctx.setFillStyle(color); //设置字体颜色  
    // ctx.setFontSize(fontSize);
    ctx.font = fontWeight + ' ' + size + 'px sans-serif' //设置字体大小，注意：百度小程序 用ctx.setFontSize设置字体大小后，计算字体宽度会无效
    let arrText = text.split('');
    if (textAlign) {
        ctx.setTextAlign(textAlign);//设置文本的水平对齐方式  ctx.setTextAlign这个可以兼容百度小程序 ，注意：ctx.textAlign百度小程序有问题
        switch (textAlign) {
            case 'center':
                left = left + width / 2
                break;

            case 'right':
                left = left + width
                break;

            default:// 左对齐
                break;
        }
    }
    let drawArr = [];
    let texts = "";
    while (arrText.length) {
        let word = arrText.shift();
        texts += word;
        let textWidth = ctx.measureText(texts).width;
        if (textWidth > width) {
            // 因为超出宽度 所以要截取掉最后一个字符
            texts = texts.substr(0, texts.length - 1);
            drawArr.push(texts);
            texts = "";
            // 最后一个字还给arrText
            arrText.unshift(word);
        } else if (!arrText.length) {
            drawArr.push(texts);
        }
    }


    console.log("🚀 ~ drawArr", drawArr)
    drawArr?.forEach((item, i) => {
        top += size * i
        console.log("🚀 ~ top", top)
        ctx.fillText(item, left, top);
        //本次绘制是否接着上一次绘制。即 reserve 参数为 false，则在本次调用绘制之前 native 层会先清空画布再继续绘制；若 reserve 参数为 true，则保留当前画布上的内容，本次调用 drawCanvas 绘制的内容覆盖在上面，默认 false。
        ctx.draw(true)
    })
    return top
}


/**
 *@description 生成指定长度的随机字符
 * @param length 字符长度
 */
export function generateRandomStr(length = 10) {
    const str = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ'
    let result = ''
    for (let i = length; i > 0; --i) {
        result += str[Math.floor(Math.random() * str.length)]
    }
    return result
}



