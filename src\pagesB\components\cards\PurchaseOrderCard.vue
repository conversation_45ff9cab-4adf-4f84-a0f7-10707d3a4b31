<template>
  <view class="card">
    <view class="content">
      <view class="content-box">
        <view>采 购 人：</view>
        <view>{{ info.user_login }}</view>
      </view>
      <view class="content-box">
        <view>采购{{ vPointName }}：</view>
        <view>{{ info.hotelName }}</view>
      </view>
      <view class="content-box">
        <view>采购商品：</view>
        <view>
          <view v-for="(item, index) in info.goods_names" :key="index"
            >{{ item.goods_name }}-{{ item.num }}</view
          >
        </view>
      </view>
      <view class="content-box">
        <view>商品总价：</view>
        <view>{{ info.zongjia.toFixed(2) }}</view>
      </view>
      <view class="content-box">
        <view>商品运费：</view>
        <view>{{ info.yunfei }}</view>
      </view>

      <view class="content-box">
        <view>收货姓名：</view>
        <view>{{ info.receiver }}</view>
      </view>
      <view class="content-box">
        <view>收货号码：</view>
        <view>{{ info.receiverPhone }}</view>
      </view>
      <view class="content-box">
        <view>详细地址：</view>
        <view>{{ info.address }}</view>
      </view>

      <view class="content-box">
        <view>订单状态：</view>
        <view class="order-status">{{ statusData }}</view>
      </view>
      <view class="content-box">
        <view>订单时间：</view>
        <view>{{
          $u.timeFormat(info.time * 1000, "yyyy-mm-dd hh:MM:ss")
        }}</view>
      </view>
      <block v-if="info.status == 3">
        <view class="content-box">
          <view>快递名称：</view>
          <view>{{ info.expressName }}</view>
        </view>
        <view class="content-box">
          <view>快递单号：</view>
          <view>{{ info.expressNumber }}</view>
        </view>
      </block>
    </view>
    <view class="btn" v-if="isManage && info.status != 4">
      <view class="btn-box">
        <BaseButton
          width="200"
          type="default"
          shape="circle"
          @onClick="confirm"
        >
          {{ btnTitle }}
        </BaseButton>
      </view>
    </view>
    <BaseModal
      :show.sync="isShowModal"
      @confirm="confirmEliverGood"
      title="请填写快递信息"
    >
      <view slot="default">
        <view>
          <BaseInput v-model="expressName" placeholder="请输入快递名称" />
        </view>
        <view class="express-number">
          <BaseInput v-model="expressNumber" placeholder="请输入快递单号" />
        </view>
      </view>
    </BaseModal>
  </view>
</template>

<script>
import BaseButton from "@/components/base/BaseButton.vue";
import BaseInput from "@/components/base/BaseInput.vue";
import BaseModal from "@/components/base/BaseModal.vue";
export default {
  components: { BaseButton, BaseModal, BaseInput },
  name: "PurchaseOrderCard",
  props: {
    info: { type: Object, default: {} },
    isManage: { type: Boolean, default: false },
  },
  computed: {
    statusData() {
      switch (this.info.status) {
        case 0:
          this.examineStatus = 1;
          this.btnTitle = "审核通过";
          return "待审核";
        case 1:
          this.examineStatus = 2;
          this.btnTitle = "线下付款";
          return "待付款";
        case 2:
          this.examineStatus = 3;
          this.btnTitle = "发货";
          return "待发货";
        case 3:
          this.examineStatus = 4;
          this.btnTitle = "补货";
          return "发货完成";
        case 4:
          return "补货完成";
        default:
          this.btnTitle = "异常";
          return "异常";
      }
    },
  },
  data() {
    return {
      btnTitle: "审核通过",
      examineStatus: 0,
      expressNumber: "", //快递编号
      expressName: "", //快递名称
      isShowModal: false,
    };
  },
  methods: {
    async updatePurchaseOrder() {
      try{
        let data = { id: this.info.id, status: this.examineStatus };
      if (this.examineStatus == 3) {
        data["expressNumber"] = this.expressNumber;
        data["expressName"] = this.expressName;
      }
      await this.$u.api.updatePurchaseOrder(data)
      this.$emit("confirm", this.btnTitle);
      }catch(error){
        console.log('错误信息',error)
      }
    
    },
    confirm() {
      if (this.examineStatus == 3) {
        this.isShowModal = true;
      } else {
        this.updatePurchaseOrder();
      }
    },
    confirmEliverGood() {
      if (!this.expressNumber || !this.expressName) {
        this.isShowErr("请填写正确得快递信息~");
      } else {
        this.updatePurchaseOrder();
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.card {
  position: relative;
  padding: 20rpx 30rpx;
}
.content {
  &-box {
    display: flex;
    font-size: $font-size-base;
    line-height: 1.8;
    .order-status {
      color: $themeComColor !important;
    }
    > view {
      &:first-child {
        text-align: right;
        width: 140rpx;
        color: $textDarkGray;
        white-space: nowrap;
        flex-shrink: 0;
      }
      &:last-child {
        color: $textBlack;
      }
    }
  }
}
.btn {
  position: absolute;
  right: 10rpx;
  bottom: 20rpx;
  display: flex;
  justify-content: flex-end;
  &-box {
  }
}
.express-number {
  margin-top: 20rpx;
}
</style>