<template>
  <view>
    <BaseNavbar :title="title" />
    <BaseTabs :current="curTabIndex" :list="tabsList" :is-showBar="false" @change="changeTabs" />
    <view class="content">
      <view>
        <view>订单编号</view>
        <view>
          <BaseInput placeholder="请输入订单编号" />
        </view>
      </view>
      <block v-if="curTabsIndex == 0">
        <view>
          <view>退款金额</view>
          <view>
            <BaseInput placeholder="请输入金额" rightText="元" />
          </view>
        </view>
        <view>
          <view>退款原因</view>
          <view>
            <BaseInput placeholder="请输入退款原因" />
          </view>
        </view>
      </block>
      <block v-if="curTabsIndex == 1">
        <view>
          <view>结束时间</view>
          <view>
            <TimeSelect v-model="endTime" placeholder="请选择结束时间" />
          </view>
        </view>

        <view>
          <view>结束原因</view>
          <view>
            <BaseInput placeholder="请输入结束原因" />
          </view>
        </view>
      </block>
      <view class="btn">
        <BaseButton type="primary">确定</BaseButton>
      </view>
    </view>
  </view>
</template>

<script>
import BaseNavbar from "@/components/base/BaseNavbar.vue";
import BaseTabs from "@/components/base/BaseTabs.vue";
import BaseInput from "@/components/base/BaseInput.vue";
import BaseButton from "@/components/base/BaseButton.vue";
import TimeSelect from "@/components/common/TimeSelect.vue";
export default {
  components: { BaseNavbar, BaseTabs, BaseInput, BaseButton, TimeSelect },
  data() {
    return {
      title: "订单维护申请",
      tabsList: [
        {
          name: "订单退款",
          status: 0,
        },
        {
          name: "结束订单",
          status: 1,
        },
      ],
      curTabsIndex: 0,
      endTime: "",
    };
  },
  methods: {
    changeTabs(e) {
      this.curTabsIndex = e;
    },
  },
};
</script>

<style lang="scss" scoped>
.content {
  padding: 30rpx;
  > view {
    margin-bottom: 50rpx;
    > view {
      &:first-child {
        color: $textBlack;
        font-weight: bold;
        font-size: $font-size-middle;
        margin-bottom: 20rpx;
      }
    }
  }
  .btn {
    margin-top: 100rpx;
  }
}
</style>