<template>
  <view>
    <BaseNavbar :title="title" />
    <view class="list">
      <view class="list_item flexRowBetween" v-for="(item, i) in setList" :key="i" @click="goPage(item.url)">
        <view>{{ item.name }}</view>
        <BaseIcon name="arrow-right" size="30" />
      </view>
    </view>
    <view class="out-login">
      <BaseButton type="primary" @onClick="outChange">切换账号</BaseButton>
      <BaseButton type="eixt" @onClick="outLogin">退出登录</BaseButton>
    </view>
  </view>
</template>

<script>
import BaseNavbar from "@/components/base/BaseNavbar.vue";
import BaseIcon from "@/components/base/BaseIcon.vue";
import BaseButton from "@/components/base/BaseButton.vue";
export default {
  components: { BaseNavbar, BaseIcon, BaseButton },
  data() {
    return {
      title: "设置",
      setList: [
        { name: "修改密码", url: "/pagesC/set/ChangeInfo?from=changePassword" },
        { name: "绑定手机号", url: "/pagesC/set/ChangeInfo?from=changePhone" },
      ],
    };
  },
  methods: {
    goPage(url) {
      uni.navigateTo({ url });
    },
    outLogin() {
      this.$u.api.logout().then((res) => {
        uni.reLaunch({ url: "/pages/login/Login?from=outLogin" });
      });
    },
    outChange() {
      uni.navigateTo({ url: "/pagesD/user/UserChange" });

    },
  },
};
</script>

<style lang="scss">
page {
  background-color: $pageBgColor;
}
</style>

<style lang="scss" scoped>
.list {
  &_item {
    height: 88rpx;
    padding: 0 30rpx;
    background-color: $uni-bg-color;
    margin-top: 10rpx;
  }
}

.out-login {
  padding: 0 30rpx;
  margin-top: 100rpx;

  :nth-child(1) {
    margin-bottom: 15rpx;
  }
}
</style>