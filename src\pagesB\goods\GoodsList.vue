<template>
  <view>
    <BaseNavbar :title="title" />

    <BaseSearch placeholder="请输入商品名称" @search="search" listType="goodsList" />
    <!-- <BaseList listType="goodsList" @searchChange="searchChange" /> -->
    <ComList :loading-type="loadingType" :bottom="isShowChecked ? 128 : 0">
      <GoodsListCard v-for="(item, index) in listData" :key="index" :isShowCheck="isShowChecked" :info="item"
        @selectItem="selectItem(item)" />
    </ComList>
    <FixedAddIcon v-if="isFromHome" @onAdd="goGoodsAdd" @onImport="goGoodsImport" :isShowImport="true" />
    <view v-if="isFromParent" class="fixed-import flexRowBetween"
      :style="{ paddingBottom: 20 + vIphoneXBottomHeight + 'rpx' }">
      <BaseCheck :checked.sync="allChecked" :title="'全选(' + selectNum + ')'" @changeCheck="changeCheckAll" />
      <BaseButton width="330" type="primary" @onClick="importGoods">导 入</BaseButton>
    </view>
  </view>
</template>

<script>
import BaseNavbar from "@/components/base/BaseNavbar.vue";
import BaseSearch from "@/components/base/BaseSearch.vue";
import ComList from "@/components/list/ComList.vue";
import myPull from "@/mixins/myPull.js";
import GoodsListCard from "../components/cards/GoodsListCard.vue";
import BaseCheck from "@/components/base/BaseCheck.vue";
import BaseButton from "@/components/base/BaseButton.vue";
import FixedAddIcon from "@/components/common/FixedAddIcon.vue";
// import BaseList from '@/components/base/BaseList.vue'
import { AddValueInObject } from '@/wxutil/list'
export default {
  components: {
    BaseNavbar,
    BaseSearch,
    ComList,
    GoodsListCard,
    BaseCheck,
    BaseButton,
    FixedAddIcon,
    // BaseList
  },
  data() {
    return {
      title: "我的商品",
      fromData: "",
      allChecked: false,
      isShowChecked: false,
      goodsName: "",
      isParentGoods: false,
      isFromPlace: false, //点位来的
      isFromParent: false, //上级商品来的
      isFromHome: false, //首页来的
      isFromDeviceSelect: false, //绑定选择商品来的
      selectInfoList: [],
      selectNum: 0,
      hotel_id: "",
      hotelName: '', //搜索
    };
  },
  methods: {
    async getList(page, done) {
      try {
        let data = {
          goods_name: this.goodsName,
          page,
        }
        let res = null
        if (this.isParentGoods) {
          res = await this.$u.api.getMyParentProducts(data);
        } else if (this.isFromPlace) {
          res = await this.$u.api.getHotelProducts({
            ...data,
            hotel_id: this.hotel_id,
          });
        } else {
          res = await this.$u.api.getMyProducts(data);
        }
        let dataList = res.data;
        if (res.length > 0 && this.isParentGoods) {
          //添加check属性
          res.forEach((item) => (item.isCheck = false));
        }
        done(dataList);
      } catch (error) {
        console.log(error)
      }

    },
    goGoodsAdd() {
      uni.navigateTo({ url: "/pagesB/goods/GoodsAdd" });
    },
    goGoodsImport() {
      uni.navigateTo({ url: `/pagesB/goods/GoodsList?from=goodsImport` });
    },
    searchChange(val) {
      this.hotelName = val
      this.search(val)
    },
    search(val) {
      this.goodsName = val;
      let list = AddValueInObject(this.vServerList.goodsList, val)
      this.$u.vuex(`vServerList.goodsList`, list)
      this.refresh();
    },
    selectItem(item) {
      if (typeof item.isCheck == "undefined") {
        this.$set(item, "isCheck", true);
      } else {
        item.isCheck = !item.isCheck;
      }
      //这里是选择商品后直接返回上局数据到上一页
      if (this.isFromDeviceSelect) {
        let { goods_name, market_price, original_img, goods_id,second_price } = item;
        let goodsInfo = {
          goods_name,
          good_price: market_price,
          original_img,
          default_stock: 1,
          goods_id,
          second_price,
        };
        /*  #ifndef H5 */
        let pages = getCurrentPages();
        let currPage = pages[pages.length - 1]; //当前页面
        let prevPage = pages[pages.length - 2]; //上一个页面
        //直接调用上一个页面的setData()方法，把数据存到上一个页面中去
        prevPage.setData({
          goodsInfo,
          isGetGoodsInfo: true,
        });
        /*#endif */
        /*  #ifdef H5 */
        this.vCurrPage.goodsInfo = goodsInfo
        this.vCurrPage.isGetGoodsInfo = true
        /*#endif */
        uni.navigateBack();
        return;
      }

      //检查选中数量
      this.selectInfoList = this.listData.filter((item) => item.isCheck);
      if (this.selectInfoList.length == this.listData.length) {
        this.allChecked = true;
      } else {
        this.allChecked = false;
      }
      this.selectNum = this.selectInfoList.length;
    },
    changeCheckAll() {
      this.allChecked = !this.allChecked;
      this.listData.forEach((item) => (item.isCheck = this.allChecked));
      if (this.allChecked) {
        this.selectNum = this.listData.length;
        this.selectInfoList = this.listData;
      } else {
        this.selectNum = 0;
        this.selectInfoList = [];
      }
    },
    async importGoods() {
      try{
        if (this.selectNum <= 0) {
        return;
      }
      let addGoodsId = this.selectInfoList.map((item) => item.goods_id);
      let data = {
        goods_ids: addGoodsId,
      };
      await this.$u.api.addParentProducts(data)
      this.isShowSuccess("导入成功", 1, () => { }, true);
      }catch(error){
        this.isShowSuccess("导入失败", 1, () => { }, true);
      }
      
    },
  },
  onLoad(opt) {
    this.fromData = opt?.from;
    if (this.fromData == "goodsImport") {
      //导入上级商品
      this.title = "上级商品";
      this.isShowChecked = true;
      this.isParentGoods = true;
      this.isFromParent = true;
    } else if (this.fromData == "place") {
      //点位来的
      this.hotel_id = opt.id;
      this.title = this.vPointName + "商品";
      this.isFromPlace = true;
    } else if (this.fromData == "deviceSelectGoods") {
      this.title = "我的商品";
      this.isFromDeviceSelect = true;
      this.isShowChecked = true;
    } else {
      this.isFromHome = true;
    }
    this.refresh();
  },
  onShow(e) {
    /*  #ifndef H5 */
    let pages = getCurrentPages();
    let currPage = pages[pages.length - 1]; // 当前页
    if (currPage.data.isDoRefresh == true) {
      // 是否刷新
      currPage.data.isDoRefresh = false;
      this.goodsName = "";
      this.refresh();
    }
    /*#endif */
    /*  #ifdef H5 */
    if (this.vCurrPage.isDoRefresh == true) {
      // 是否刷新
      this.vCurrPage.isDoRefresh = false;
      this.goodsName = "";
      this.refresh();
    }
    /*#endif */
  },
  mixins: [myPull()],
};
</script>
<style lang="scss">
page {
  background-color: $pageBgColor;
}
</style>
<style lang="scss" scoped>
.fixed-import {
  position: fixed;
  left: 0;
  bottom: 0;
  width: 100%;
  padding: 20rpx 30rpx;
  box-sizing: border-box;
  background-color: $uni-bg-color;
  z-index: 999999;
}
</style>
