<template>
  <view>
    <view class="sticker">
      <BaseNavbar :title="title" />
      <BaseTabs
        :list="tabsList"
        :isShowBar="false"
        @change="tabChange"
        :isScroll="true"
        :current="curTabIndex"
      />
    </view>

    <ComList :loading-type="loadingType">
      <WithDrawalRecordCard
        v-for="item in listData"
        :key="item.id"
        :info="item"
      />
    </ComList>
  </view>
</template>

<script>
import BaseNavbar from "@/components/base/BaseNavbar.vue";
import BaseTabs from "@/components/base/BaseTabs.vue";
import ComList from "@/components/list/ComList.vue";
import WithDrawalRecordCard from "../components/cards/WithDrawalRecordCard.vue";
import myPull from "@/mixins/myPull.js";
export default {
  components: { BaseNavbar, BaseTabs, ComList, WithDrawalRecordCard },
  data() {
    return {
      title: "提现记录",
      tabsList: [
        {
          name: "全部",
          status: "",
        },
        {
          name: "审核中",
          status: 0,
        },
        {
          name: "审核通过",
          status: 1,
        },
        {
          name: "提现成功",
          status: 2,
        },
        {
          name: "提现失败",
          status: 3,
        },
        {
          name: "提现撤销",
          status: 5,
        },
        {
          name: "提现已退款",
          status: 10,
        },
      
        {
          name: "已驳回",
          status: -1,
        },
      ],
      curTabIndex: 0,
    };
  },
  methods: {
    tabChange(e) {
      this.curTabIndex = e;
      this.refresh();
    },
    getList(page, done) {
      let status = this.tabsList[this.curTabIndex].status;
      status = status
      let data = {
        page,
        limit: 15,
        status,
      };
      this.$u.api.getCashWithdrawList(data).then((res) => {
        done(res.data);
      });
    },
  },

  onLoad(opt) {
    this.refresh();
  },
  mixins: [myPull()],
};
</script>
<style lang="scss">
page {
  background-color: $pageBgColor;
}
</style>
<style lang="scss" scoped>
</style>