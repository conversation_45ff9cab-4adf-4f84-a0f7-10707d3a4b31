<template>
  <view>
    <BaseNavbar title="广告审核" />                       
    <view class="sticker">
      <BaseTabs :current="curTabIndex"  :list="tabList" @change="tabChange" :isShowBar="false" />
    </view>

    <ComList :loading-type="loadingType">
      <AdvertsAuditListCard
        v-for="item in listData"
        :key="item.id"
        :info="item"
        @confirm="confirmChange"
      />
    </ComList>
  </view>
</template>

<script>
import ComList from "../../components/list/ComList.vue";
import myPull from "@/mixins/myPull.js";
import AdvertsAuditListCard from "../components/cards/AdvertsAuditListCard.vue";
import BaseNavbar from "../../components/base/BaseNavbar.vue";
import BaseTabs from "../../components/base/BaseTabs.vue";
export default {
  components: { ComList, AdvertsAuditListCard, BaseNavbar, BaseTabs },
  data() {
    return {
      tabList: [
        {
          name: "全部",
          status: "",
        },
        {
          name: "待审核",
          status: 0,
        },
        {
          name: "已审核",
          status: 1,
        },
      ],
      curTabIndex: 0,
    };
  },
  methods: {
    tabChange(e) {
      this.curTabIndex = e;
      this.refresh();
    },
    async getList(page, done) {
      try{
        let data = {
        page,
        limit: 10,
        status: this.tabList[this.curTabIndex].status,
        device_sn: "",
      };
      let res=await this.$u.api.getMemberApplyList(data)
      done(res.data);
      }catch(error){
        console.log('错误信息',error)
      }
     
    },
    confirmChange(msg) {
      this.isShowSuccess(msg + "成功", 0, this.refresh());
    },
  },
  onLoad(opt) {
    this.refresh();
  },
  mixins: [myPull()],
};
</script>

<style lang="scss" scoped></style>
