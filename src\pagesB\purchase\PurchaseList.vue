<template>
  <view>
    <view class="sticker">
      <BaseNavbar title="集采商城" />
      <BaseTabs :list="tabList" :isShowBar="false" @change="tabChange" />
      <BaseTabs
        v-if="curTabIndex == 1"
        :list="tabOrderList"
        :isShowBar="false"
        @change="orderTabChange"
        :current="curTabIndex"
      />
    </view>

    <ComList :loading-type="loadingType" :bottom="curTabIndex == 0 ? 128 : 0">
      <block v-if="curTabIndex == 0">
        <PurchaseGoodsCard
          v-for="(item, index) in listData"
          :key="index"
          :info="item"
          :index="index"
          @onCheck="selectItem(item, index)"
          @checkValue="checkValue"
          :current="curTabIndex"
        />
      </block>
      <block v-else>
        <PurchaseOrderCard
          v-for="item in listData"
          :key="item.id"
          :info="item"
        />
      </block>
    </ComList>
    <view
      v-if="curTabIndex == 0"
      class="fixed-btn flexRowBetween"
      :style="{ paddingBottom: 20 + vIphoneXBottomHeight + 'rpx' }"
    >
      <BaseCheck
        :checked.sync="isAllCheck"
        :title="'全选(' + selectNum + ')'"
        @changeCheck="changeCheckAll"
      />
      <view class="total">
        <view class="total-box">
          <view>合计：</view>
          <view class="total-box-mark">￥</view>
          <view class="total-box-price">{{ goodsTotalPrice.toFixed(2) }}</view>
        </view>
        <view class="total-num"> 共计{{ goodsTotalNum }}件 </view>
      </view>
      <BaseButton width="200" type="primary" @onClick="confirmOrder"
        >提交订单</BaseButton
      >
    </view>
  </view>
</template>

<script>
import BaseNavbar from "@/components/base/BaseNavbar.vue";
import BaseTabs from "../../components/base/BaseTabs.vue";
import ComList from "@/components/list/ComList.vue";
import PurchaseGoodsCard from "../components/cards/PurchaseGoodsCard.vue";
import PurchaseOrderCard from "../components/cards/PurchaseOrderCard.vue";
import myPull from "@/mixins/myPull.js";
import BaseButton from "../../components/base/BaseButton.vue";
import BaseCheck from "../../components/base/BaseCheck.vue";
export default {
  components: {
    BaseNavbar,
    BaseTabs,
    ComList,
    PurchaseGoodsCard,
    PurchaseOrderCard,
    BaseButton,
    BaseCheck,
  },
  data() {
    return {
      tabList: [
        {
          name: "集采商城",
          status: 0,
        },
        {
          name: "集采订单",
          status: 1,
        },
      ],
      tabOrderList: [
        {
          name: "全部",
          status: "",
        },
        {
          name: "待审核",
          status: 0,
        },
        {
          name: "待付款",
          status: 1,
        },
        {
          name: "待发货",
          status: 2,
        },
        {
          name: "发货完成",
          status: 3,
        },
      ],
      curTabIndex: 0,
      curOrderTabIndex: 0,
      isAllCheck: false, //是否全部选中
      selectNum: 0,
      selectInfoList: [], //选中的list
      goodsTotalPrice: 0, //选中商品总价格
      goodsTotalNum: 0, //总数量
    };
  },
  methods: {
    tabChange(e) {
      this.curTabIndex = e;
      this.refresh();
    },
    orderTabChange(e) {
      this.curOrderTabIndex = e;
      this.refresh();
    },
    getList(page, done) {
      if (this.curTabIndex == 0) {
        let data = {
          page,
          limit: 10,
        };
        this.$u.api.getMyParentProducts(data).then((res) => {
          let newData = res.data.map((item) => {
            if (typeof item.isAmount == "undefined") item.isAmount = 1;
            if (typeof item.isCheck == "undefined") item.isCheck = false;
            return item;
          });
          done(newData);
        });
      } else {
        let data = {
          status: this.tabOrderList[this.curOrderTabIndex].status,
          page,
          limit: 10,
        };
        this.$u.api.getMyPurchaseList(data).then((res) => {
          done(res.data);
        });
      }
    },
    //选中状态改变
    selectItem(item, index) {
      if (typeof item.isCheck == "undefined") {
        this.$set(item, "isCheck", true);
      } else {
        item.isCheck = !item.isCheck;
      }
      this.selectInfoList = this.listData.filter((item) => item.isCheck) || [];
      this.isAllCheck = this.selectInfoList.length == this.listData.length;
      this.selectNum = this.selectInfoList.length;
      this.countGoodPrice();
    },
    //编辑框数量改变
    checkValue(item) {
      this.listData[item.index].isAmount = item.value;
      this.listData[item.index].isCheck && this.countGoodPrice();
    },
    //全选
    changeCheckAll() {
      this.isAllCheck = !this.isAllCheck;
      this.listData.forEach((item) => (item.isCheck = this.isAllCheck));
      this.selectInfoList = this.listData;
      this.countGoodPrice();
    },
    //计算价格
    countGoodPrice() {
      let totalPrice = 0,
        totalNum = 0,
        selectList = [];
      this.listData.forEach((item) => {
        if (item.isCheck) {
          totalPrice += item.shop_price * item.isAmount;
          totalNum += item.isAmount;
          selectList.push(item);
        }
      });
      this.goodsTotalPrice = totalPrice;
      this.goodsTotalNum = totalNum;
      this.selectInfoList = selectList;
    },
    //确认订单
    confirmOrder() {
      if (this.goodsTotalNum > 0) {
        let setData = {
          list: this.selectInfoList,
          totalPrice: this.goodsTotalPrice,
          totalNum: this.goodsTotalNum,
        };
        uni.setStorageSync("select_purchase_goods", setData);
        uni.navigateTo({ url: "/pagesB/purchase/PurchaseConfirmOrder" });
      }
    },
  },
  onLoad(opt) {
    this.refresh();
  },
  onShow(e) {
   /*  #ifndef H5 */
    let pages = getCurrentPages();
    let currPage = pages[pages.length - 1]; // 当前页
    if (currPage.data.isDoRefresh == true) {
      // 是否刷新
      currPage.data.isDoRefresh = false;
      this.refresh();
    }
    if (currPage.data.item && this.isShowPopup) {
      let checkHotel = currPage.data.item;

      this.selectHotel = {
        hotelName: checkHotel.hotelName,
        dianwei: checkHotel.id,
      };
    }
    /*#endif */
    /*  #ifdef H5 */
    if (this.vCurrPage.isDoRefresh == true) {
      // 是否刷新
      this.vCurrPage.isDoRefresh = false;
      this.refresh();
    }
    if (this.vCurrPage.item && this.isShowPopup) {
      let checkHotel = this.vCurrPage.item;

      this.selectHotel = {
        hotelName: checkHotel.hotelName,
        dianwei: checkHotel.id,
      };
    }
    /*#endif */
    
    
  },
  mixins: [myPull()],
};
</script>
<style lang="scss">
page {
  background-color: $pageBgColor;
}
</style>
<style lang="scss" scoped>
.fixed-btn {
  position: fixed;
  left: 0;
  bottom: 0;
  width: 100%;
  padding: 20rpx 30rpx;
  box-sizing: border-box;
  background-color: $uni-bg-color;
  z-index: 999999;
  .total {
    &-box {
      display: flex;
      align-items: flex-end;
      color: $textBlack;
      font-size: $font-size-base;
      &-mark {
        color: red;
        font-size: $font-size-xsmall;
      }
      &-price {
        color: red;
        font-size: $font-size-middle;
      }
    }
    &-num {
      color: $textDarkGray;
      font-size: $font-size-small;
    }
  }
}
</style>