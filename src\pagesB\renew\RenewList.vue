<template>
  <view>
    <BaseNavbar :title="title" />

    <!-- <BaseSearch placeholder="请输入设备编号/上级账号" /> -->

    <ComList :loading-type="loadingType" :bottom="120">
      <RenewListCard v-for="item in data" :key="item.id" :info="item" @onCheck="selectItem(item)" />
    </ComList>
    <FixedAddIcon :bottom="30" @onAdd="goGoodsAdd" />
    <FixedSubButton :info="fixedSubOrderInfo" @changeCheckAll="changeCheckAll" @confirm="confirmOrder" />
  </view>
</template>

<script>
import BaseNavbar from "@/components/base/BaseNavbar.vue";
import BaseSearch from "@/components/base/BaseSearch.vue";
import ComList from "@/components/list/ComList.vue";
import myPull from "@/mixins/myPull.js";
import RenewListCard from "../components/cards/RenewListCard.vue";
import FixedAddIcon from "@/components/common/FixedAddIcon.vue";
import FixedSubButton from "../../components/common/FixedSubButton.vue";
export default {
  components: {
    BaseNavbar,
    BaseSearch,
    ComList,
    RenewListCard,
    FixedAddIcon,
    FixedSubButton,
  },
  data() {
    return {
      title: "续费管理",
      data: [{

        device_sn: '022222222544',
        start_date: '2023-06-18 08:00:00',
        statar: 1,
        money: 25,
        user_login: 'test'
      }, {

        device_sn: '022222222544',
        start_date: '2023-06-18 08:00:00',
        statar: '12255522',
        money: 25,
        user_login: 'test'
      }, {

        device_sn: '022222222544',
        start_date: '2023-06-18 08:00:00',
        statar: '12255522',
        money: 25,
        user_login: 'test'
      }, {

        device_sn: '022222222544',
        start_date: '2023-06-18 08:00:00',
        statar: '12255522',
        money: 25,
        user_login: 'test'
      }]
    };
  },
  computed: {
    fixedSubOrderInfo() {
      let selectList = this.data?.filter((item) => item.isCheck) || [];
      let isAllCheck =
        selectList.length === this.data.length && selectList.length !== 0;
      let selectNum = selectList.length || 0;
      let totalPrice = selectList.reduce(
        (total, nowValue) =>
          (parseFloat(total) * 1000 + parseFloat(nowValue.money) * 1000) / 1000,
        0
      );
      return {
        isAllCheck, //是否全选
        selectNum, //选中数量
        totalPrice: totalPrice, //总价
        totalNum: selectList.length, //总数量
      };
    },
  },
  methods: {
    goGoodsAdd() {
      uni.navigateTo({ url: "/pagesD/Renewal/RenewalAdd" });
    },
    getList(page, done) {
      let data = {
        day: 30,
        page, //页码
      };
      this.$u.api.getUserExpireMachine(data).then((res) => {
        done(res.data.data);
      });
    },
    selectItem(item) {
      if (typeof item.isCheck == "undefined") {
        this.$set(item, "isCheck", true);
      } else {
        item.isCheck = !item.isCheck;
      }
    },
    changeCheckAll(isAllCheck) {
      this.data = this.data.map((item) => {
        if (typeof item.isCheck == "undefined") {
          this.$set(item, "isCheck", isAllCheck);
        } else {
          item.isCheck = isAllCheck;
        }
        return item;
      });
    },
    confirmOrder() {
      let deviceList = [];
      this.data.forEach((item) => {
        if (item.isCheck) deviceList.push(item.device_sn);
      });
      let data = {
        device_sn_str: deviceList.join(","),
        year: 1,
        renew_type: "device", //day=>按照临期时间续费device=>按照设备编号续费
        day: "30",
      };
      this.$u.api.createRenewOrder(data).then((res) => {
        let orderSn = res.data.order_sn;
        uni.setStorageSync(orderSn, {
          deviceList,
          totalNum: this.fixedSubOrderInfo.totalNum,
        });
        uni.navigateTo({
          url:
            "/pagesB/renew/RenewConfirmOrder?order_sn=" +
            orderSn +
            "&orderAmount=" +
            res.data.amount,
        });
      });
    },
  },
  onLoad() {
    this.refresh();
  },
  mixins: [myPull()],
};
</script>


<style lang="scss">
page {
  background-color: $pageBgColor;
}
</style>
<style lang="scss" scoped></style>