<template>
  <view>
    <BaseNavbar title="优惠券领取记录" />
    <BaseTabs
      :list="tabList"
      @change="tabChange"
      height="90"
      :isShowBar="true"
      :current="curTabIndex"
    />
    <ComList :loadingType="loadingType">
      <CouponRecordCard v-for="item in listData" :key="item.id" :info="item" />
    </ComList>
  </view>
</template>
<script>
import BaseNavbar from "@/components/base/BaseNavbar.vue";
import ComList from "@/components/list/ComList.vue";
import CouponRecordCard from "@/pagesB/coupon/components/CouponRecordCard.vue";
import myPull from "@/mixins/myPull";
import BaseTabs from "../../components/base/BaseTabs.vue";
export default {
  components: { BaseNavbar, ComList, CouponRecordCard, BaseTabs },
  mixins: [myPull()],
  data() {
    return {
      tabList: [
        {
          name: "全部",
          status: "",
        },
        {
          name: "未核销",
          status: 0,
        },
        {
          name: "已核销",
          status: 1,
        },
      ],
      curTabIndex: 0,
    };
  },

  methods: {
    tabChange(e) {
      this.curTabIndex = e;
      this.refresh();
    },
    getList(page, done) {
      let data = {
        page,
        limit: 10,
      };
      this.$u.api.getReceiveCouponsHotelList(data).then((res) => {
        done(res);
      })
      .catch((err=>{
        console.log('错误信息',err)
      }))
    },
  },
  onLoad() {
    this.refresh();
  },
};
</script>

<style lang="scss">
page {
  background-color: $pageBgColor;
}
</style>
<style scoped  lang='scss'>
</style>