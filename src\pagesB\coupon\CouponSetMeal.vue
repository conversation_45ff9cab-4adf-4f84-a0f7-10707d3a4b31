<template>
  <view>
    <BaseNavbar :title=title />
    <view class="recharge">
      <view
        class="recharge-item"
        v-for="(item, index) in setMealList"
        :key="item.id"
        :class="{ active: item.active }"
        @click="activeHandle(index)"
      >
        <view class="recharge-item-price">{{ item.money.toFixed(2) }}</view>
        <view class="recharge-item-discount">{{
          item.sys_money.toFixed(2)
        }}</view>
        <view class="recharge-item-describe">按数量扣费</view>
        <view class="recharge-item-num">{{ item.num }}张</view>
      </view>
    </view>

    <view class="btn" :style="{ bottom: vIphoneXBottomHeight + 20 + 'rpx' }">
      <BaseButton shape="circle" @onClick="isShowPopup = true"
        >提交支付</BaseButton
      >
    </view>
    <BasePopup :show.sync="isShowPopup" mode="bottom">
      <PayWay @confirmPay="confirmPay" :orderMoney="orderAmount" />
    </BasePopup>
  </view>
</template>
<script>
import BaseButton from "../../components/base/BaseButton.vue";
import BaseNavbar from "@/components/base/BaseNavbar.vue";
import BasePopup from "../../components/base/BasePopup.vue";
import PayWay from "../../components/common/PayWay.vue";
export default {
  components: { BaseNavbar, BaseButton, PayWay, BasePopup },
  data() {
    return {
      setMealList: [],
      orderAmount: "",
      isShowPopup: false,
      activeItem: {},
      title:`${this.vCargoLanes}充值`
    };
  },

  methods: {
    async getSetMeal() {
      this.setMealList = (await this.$u.api.getHotelCouponPackage()) || [];
      this.activeHandle(0);
    },
    activeHandle(index) {
      this.setMealList?.map((item, i) => {
        item.active = i === index;
        item.active && (this.activeItem = item);

        return item;
      });
    },
    confirmPay(type, pay_type) {
      this.isShowPopup = false;
      let data = {
        pay_type,
        openid: this.vPayOpenid,
        money: this.activeItem?.money, // 支付金额
        num: this.activeItem?.num, //购买数量
      };

      this.$u.api.createCouponHotelPackageAndPrepay(data).then((res) => {
        if (type === 0) {
          let newUserInfo = this.vUserInfo;
          newUserInfo.user.cash =
            (parseFloat(newUserInfo.user.cash) * 1000 -
              parseFloat(this.orderAmount) * 1000) /
            1000;
          this.$u.vuex("vUserInfo", newUserInfo);
          this.isShowSuccess("支付成功~");
        } else if (type === 1) {
          this.requestWxPay(res?.pay_info);
        }
      })
      .catch((err=>{
        console.log('错误信息',err)
      }))
    },
    requestWxPay(payInfo) {
      let that = this;
      //请求微信支付
      uni.requestPayment({
        /* #ifdef MP-WEIXIN */
        provider: "wxpay",
        appId: payInfo.appId,
        timeStamp: payInfo.timeStamp,
        nonceStr: payInfo.nonceStr,
        package: payInfo.package,
        signType: payInfo.signType,
        paySign: payInfo.paySign,
        /* #endif */
        success(res) {
          console.log("微信支付订单成功", res);
          that.isShowSuccess("支付成功~");
        },
        fail(res) {
          console.log("支付失败", res);
          that.isShowErr("支付失败~");
        },
      });
    },
  },
  onLoad() {
    this.getSetMeal();
  },
};
</script>

<style   lang='scss'>
page {
  background-color: $pageBgColor;
}
</style>
<style scoped  lang='scss'>
.recharge {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  padding: 30rpx;
  margin: 26rpx 16rpx;
  border-radius: 20rpx;
  background-color: #fff;
  box-sizing: border-box;
  &-item {
    position: relative;
    flex: 0 0 45%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: center;
    height: 180rpx;
    padding: 32rpx 0 20rpx;
    border-radius: 10rpx;
    color: #fff;
    background-color: #0039f3;
    box-sizing: border-box;
    &-price {
      font-size: 38rpx;
      font-weight: 700;
    }
    &-discount {
      font-size: 28rpx;
      text-decoration: line-through;
    }
    &-describe {
      font-size: 26rpx;
    }
    &-num {
      position: absolute;
      right: 0;
      top: 0;
      padding: 8rpx;
      font-size: 24rpx;
      color: #fff;
      border-radius: 0 0 10rpx 10rpx;
      background-color: #fd746c;
    }
    &:nth-child(n + 3) {
      margin-top: 30rpx;
    }
  }
}
.active {
  background-color: pink;
}
.btn {
  position: fixed;
  bottom: 20rpx;
  left: 0;
  right: 0;
  padding: 0 50rpx;
}
</style>