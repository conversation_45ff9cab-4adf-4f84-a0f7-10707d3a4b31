<template>
  <view>
    <BaseNavbar :title="title" :pageShow="true" :isShowBack="true" />
    <BaseSearch :showScan="true" type-img="place" @onClickIcon="onClickIcon" @search="search" @onClickScan="onClickScan"
      placeholder="请输入设备编号" listType="replenishList" />
    <!-- <BaseList listType="replenishList" @searchChange="searchChange" /> -->
    <view class="tabs">
      <!-- <BaseSroll height="44" :list="tabList" name="label" :current="curTabIndex" @change="tabChange" /> -->
      <BaseTabs height="44" :list="tabList" name="label" :is-scroll="true" :isShowBar="false" :current="curTabIndex"
        @change="tabChange" />
    </view>
    <view class="linbuttom"></view>
    <BaseDropdown :options-list="optionsList" @change="changeDropdown" :num="total" />
    <ComList :loading-type="loadingType" class="height_box">
      <!-- 使用作用域插槽，将遍历后的数据item和index传递出去 -->
      <!-- <view class="height" :key="index">
      </view> -->
      <view v-for="(items, i) in pagesData.length" :key="i" :id="'pages-' + i" class="border">
        <view v-if="i < currentIndex + 2 && i > currentIndex - 2" :style="{ height: pageHeight[i] + 'px' }">
          <ReplenishListCard v-for="(item, index) in  pagesData[i]" :key="index" @updataTime="updataTime(item)"
            :info="item" :status="optionsList[0].value" />
        </view>
        <view v-else :style="{ height: pageHeight[i] + 'px' }" class="border">

        </view>
      </view>

    </ComList>
    <!-- 筛选 -->
    <BasePopup :show.sync="isShowPopup" mode="top" :customStyle="customStyle">
      <DeviceScreener ref="DeviceScreener" @confirm="confirmScreenner"></DeviceScreener>
    </BasePopup>
    <BaseBackTop v-if="scrollTop > systemHeight" listShow @onScroll="onScroll">
    </BaseBackTop>
    <!-- <view class="zhe"></view> -->
  </view>
</template>

<script>
import BaseNavbar from "@/components/base/BaseNavbar.vue";
import BaseSearch from "@/components/base/BaseSearch.vue";
import BaseTabs from "@/components/base/BaseTabs.vue";
import BaseDropdown from "@/components/base/BaseDropdown.vue";
import ComList from "@/components/list/ComList.vue";
import ReplenishListCard from "../components/cards/ReplenishListCard.vue";
import myPull from "@/mixins/myPull";
import BasePopup from "../../components/base/BasePopup.vue";
import { getUrlDynamicData } from "@/common/tools/utils.js";
import DeviceScreener from "../components/screener/DeviceScreener.vue";
import { subtractDaysAndFormat } from "@/wxutil/times";
// import BaseList from '@/components/base/BaseList.vue'
import { AddValueInObject } from '@/wxutil/list'
import BaseBackTop from '@/components/base/BaseBackTop.vue'
export default {
  components: {
    BaseNavbar,
    BaseSearch,
    BaseDropdown,
    ComList,
    ReplenishListCard,
    BaseTabs,
    BasePopup,
    DeviceScreener,
    // BaseList,
    BaseBackTop,
  },
  data() {
    return {
      title: "运维管理",
      tabList: [
        { label: "全部", value: 0, status: 0 },

        {
          label: "缺货",
          value: 1,
          status: 1,
        },
        {
          label: "缺电",
          value: 2,
          status: 2,
        },
        {
          label: "禁用",
          value: 3,
          status: 4,
        },
        // {
        //   label: "使用中",
        //   value: 4,
        //   status: 5,
        // },
        // {
        //   label: "离线",
        //   value: 4,
        //   status: 6,
        // }, 
        {
          label: "3天未成交",
          value: 4,
          status: 3,
        }, {
          label: "7天未成交",
          value: 5,
          status: 7,
        }, {
          label: "30天未成交",
          value: 6,
          status: 30,
        },

      ],
      optionsList: [
        {
          title: "全部",
          options: [
            { label: "全部", value: 0, status: 0 },

            {
              label: "缺货",
              value: 1,
              status: 5,
            },

            {
              label: "缺电",
              value: 2,
              status: 2,
            },
            {
              label: "禁用",
              value: 3,
              status: 4,
            },

            // {
            //   label: "使用中",
            //   value: 4,
            //   status: 5,
            // },
            // {
            //   label: "离线",
            //   value: 4,
            //   status: 6,
            // }, 
            {
              label: "3天未成交",
              value: 4,
              status: 3,
            }, 
            {
              label: "7天未成交",
              value: 5,
              status: 7,
            }, {
              label: "30天未成交",
              value: 6,
              status: 30,
            },

          ],
          value: 0,
        },
      ],
      total: 0,
      isShowPopup: false,
      device_sn: "",
      room_num: null,
      dianwei: 0,
      status: 0,
      isstatus: "",
      owner: "", //上级账号
      curTabIndex: 0,
      items: {},
      lastest_order_time: '',
      hotelName: '', //搜索
      containerHeight: 0,
      oneHeight: 0, // 单个元素的高度,默认200.在mounted中会再次回去单个元素高度
      scrollLIstTop: 0,
      stop: false,
      statusTwo: 0,
      customStyle: {
        top: 110 + this.vStatusBarHeight + this.vNavBarHeight + 'rpx',
      },
      scrollTop: 0,
      currentIndex: 0,          // 当前页数 pageNum
      pageHeight: [],           // 每屏高度存储
      systemHeight: 0,           // 屏幕高度
      scrollControlDisabled: false,//是否只能滑动一屏
      isScrolling: false,

    };
  },
  methods: {
    computedHeight() {
      const query = uni.createSelectorQuery().in(this);
      for (let i = 0; i < this.pagesData.length; i++) {
        query.select('#pages-' + i).boundingClientRect(data => {
          if (data && data.height > 20) {
            this.pageHeight[i] = data.height;
          }
        }).exec();
      }
    },
    //返回顶部
    onScroll() {
      this.scrollControlDisabled = true;
      this.scrollTop = 0
      this.currentIndex = 0
      uni.pageScrollTo({
        scrollTop: 0,
        duration: 100,
        complete: () => {
          this.scrollControlDisabled = false;
        }
      });
    },
    // 需要渲染的数据
    // onPageScroll(e) {
    //   this.scrollTop = e.scrollTop;
    // },
    loading(title, isShowLoading) {
      if (isShowLoading) {
        uni.showLoading({
          title,
          mask: true,
        })
      } else {
        uni.hideLoading()
      }
    },
    /* 更新时间 */
    async updataTime(item) {
      let parames = {
        device_sn: item.device_sn
      }
      let data = await this.$u.api.getAllStatus(parames)
      setTimeout(() => {
        this.loading('更新中', true)
      }, 100)
      setTimeout(async () => {
        try {
          let res = await this.$u.api.getUMDetail(parames)
          this.items = item

          let index = this.listData.findIndex((items) => items.id === this.items.id); // 查找id为2的元素索引
          let pagesIndex = this.pagesData[this.currentIndex].findIndex((items) => items.id === this.items.id); // 查找id为2的元素索引
          if (index !== -1) {
            // 如果找到了
            this.items.report_time = res.report_time
            this.items.light_status = res.light_status
            this.items.tip_music = res.tip_music
            this.items.electricity = res.electricity
            this.items.remain_game_time = res.remain_game_time
            this.items.electricity_status = res.electricity_status
            this.listData.splice(index, 1, this.items);
            this.pagesData[this.currentIndex].splice(pagesIndex, 1, this.items);
            this.loading()
          }
        } catch (error) {
          console.log('错误', error)
        }

      }, 3000)

    },
    tabChange(e, item) {
      this.curTabIndex = e;
      let data = this.optionsList[0]
      data.value = e
      data.title =
        this.optionsList[0].options[e].label;
      let datalist = []
      datalist[0] = data
      this.changeDropdown(datalist)
    },
    async getList(page, done) {
      // this.isScrolling = true
      try {
        let data = {
          device_sn: this.device_sn,
          room_num: this.room_num,
          status: this.status, // 是否在线
          hotel_id: this.dianwei,
          owner: this.owner, // 上级账号
          lastest_order_time: this.lastest_order_time,
          page,
        };

        switch (this.isstatus) {
          case 0:
            if (this.statusTwo == 0) {
              this.status = 0;
              // data['game_doing'] = 0;
            }
            break;
          case 5:
            data['amount'] = 10;
            break;
          case 2:
            data['electricity'] = 0;
            break;
          case 4:
            data['use_status'] = 2;
            break;
          case 6:
            data['status'] = 3;
            break;
          default:
            break;
        }

        let res = await this.$u.api.getBindHotelUMs(data)
        if (res.machine.length > 0) this.total = res.machine[0].Total;
        done(res.data);
        if (this.pagesData.length > this.pageHeight.length) {
          this.$nextTick(() => {
            setTimeout(() => {
              this.computedHeight()
              // this.isScrolling = false;
            }, 200);
          });
        }

      } catch (error) {
        console.error('获取补货列表信息出错', error);
        // this.isScrolling = false
      }
    },
    changeDropdown(item) {
      // console.log('下拉选择', item)
      this.optionsList = item;
      let index = item[0].value;
      this.isstatus = item[0].options[index].status;
      if (index < 4) {
        this.lastest_order_time = ''
      } else {
        // this.isstatus=0
        this.lastest_order_time = subtractDaysAndFormat(item[0].options[index].status)
      }
      this.curTabIndex = item[0].value
      this.refresh();
    },
    lookDetails(device_sn) {
      uni.navigateTo({
        url:
          "/pagesB/device/DeviceGoodsList?from=replenish&device_sn=" +
          device_sn,
      });
    },
    onClickIcon(e) {
      if (e == 0) {
        uni.navigateTo({ url: "/pagesB/mapMode/MapMode?from=replenish" });
      } else if (e == 1) {
        this.isShowPopup = !this.isShowPopup;
      }
    },
    confirmScreenner(e) {
      this.device_sn = e.device_sn;
      this.room_num = e.room_num;
      this.status = e.status;
      this.statusTwo = e.status;
      this.dianwei = e.dianwei;
      this.owner = e.owner;
      this.isShowPopup = false;
      // this.curTabIndex = 0;
      // let data = this.optionsList[0]
      // data.value = 0
      // data.title =
      //   this.optionsList[0].options[0].label;
      // console.log('tab切换', data)
      // let datalist = []
      // datalist[0] = data
      // this.changeDropdown(datalist)
      this.refresh();
    },
    searchChange(val) {
      this.hotelName = val
      this.search(val)
    },
    search(e) {
      this.device_sn = e;
      let list = AddValueInObject(this.vServerList.replenishList, e)
      this.$u.vuex(`vServerList.replenishList`, list)
      this.refresh();
    },
    //点击扫码icon
    onClickScan(result) {
      if (!result) return this.isShowErr("请扫描正确二维码~");
      if (result.includes("device_sn")) {
        this.device_sn = getUrlDynamicData(result, "device_sn");
        this.refresh();
        this.device_sn = "";
      } else {
        this.isShowErr("请扫描正确二维码~");
      }
    },
  },
  onLoad(options) {
    // 计算单个元素的高度
    /*  */
    this.customStyle = {
      top: 110 + this.vStatusBarHeight + this.vNavBarHeight + 'rpx',
    }
    if (options.status) {
      // let item = {
      //   label: "使用中",
      //   status: 5,
      //   value: 4
      // }
      let item = this.optionsList[0].options.find(option => option.status == options.status)
      if (item) {
        this.tabChange(item.value, item)
      }
    } else {

      this.refresh();
    }
    this.systemHeight = uni.getSystemInfoSync().windowHeight; // 获取屏幕高度

  },
  onShow() {
    let isDoRefresh, item;

    // 获取当前页的数据（针对不同平台）
    /*  #ifndef H5 */
    let pages = getCurrentPages();
    let currPage = pages[pages.length - 1]; // 当前页
    isDoRefresh = currPage.data.isDoRefresh;
    item = currPage.data.item;
    currPage.data.isDoRefresh = false;
    /*#endif */

    /*  #ifdef H5 */
    isDoRefresh = this.vCurrPage.isDoRefresh;
    item = this.vCurrPage.item;
    this.vCurrPage.isDoRefresh = false;
    /*#endif */

    // 是否刷新
    if (isDoRefresh) {
      this.goodsName = "";
      this.refresh();
    }

    // 更新设备筛选器中的酒店信息
    if (item && this.isShowPopup) {
      let checkHotel = item;
      this.$refs.DeviceScreener.hotelName = checkHotel.hotelName;
      this.$refs.DeviceScreener.hotel_id = checkHotel.id;
    }
  },
  onPageScroll(event) {
    if (!event.scrollTop || this.scrollControlDisabled) {
      return;
    }
    let pageScrollTop = event.scrollTop;
    this.scrollTop = event.scrollTop;
    // 检查this.pageHeight因为索引问题没有添加补上
    if (this.pagesData.length > this.pageHeight.length) {
      this.currentIndex = this.pagesData.length - 1;
      this.$nextTick(() => {
        this.computedHeight()
      });
    } else {
      let scrollTop = 0;
      for (let i = 0; i < this.pageHeight.length; i++) {
        scrollTop += this.pageHeight[i];
        if (scrollTop > pageScrollTop + this.systemHeight) {
          this.currentIndex = i;
          break;
        }
      }
    }
  },
  mixins: [myPull()],

};
</script>

<style lang="scss">
page {
  background-color: $pageBgColor;
}
</style>
<style lang="scss" scoped>
.linbuttom {
  height: 2rpx;
  background-color: rgba(206, 206, 206, 0.607);
}

.border {
  border: 1rpx solid transparent;
  box-sizing: border-box;
  /* 包含边框和内边距在元素的总宽高内 */
}

.tabs {
  padding-top: 10rpx;
  background-color: #fff;
}

.zhe {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: -1;
}
</style>