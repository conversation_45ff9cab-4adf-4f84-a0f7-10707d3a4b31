<template>
    <view class='card'>
        <view class="content">
            <block v-if="cardType === 'user'">
                <view class="content-box device-code">
                    <view class="title">{{ vPointName }}：</view>
                    <view class="txt">{{ info.hotelName || '暂无' }}</view>
                </view>
                <view class="content-box device-code">
                    <view class="title">设备数量：</view>
                    <view class="txt">{{ info.nums }}台</view>
                </view>
                <view class="content-box device-code" v-if="false">
                    <view class="title">{{ vPointName }}地址：</view>
                    <view class="txt">{{ info.address || '暂无' }}</view>
                </view>
            </block>
            <block v-else-if="cardType === 'hotel'">
                <view class="content-box device-code">
                    <view class="title">设备编号：</view>
                    <view class="txt">{{ info.device_sn || '暂无' }}</view>
                </view>
                <view class="content-box device-code">
                    <view class="title">位置：</view>
                    <view class="txt">{{ info.room_num }}</view>
                </view>
            </block>
            <block v-else-if="cardType === 'manage'">
                <view class="content-box device-code">
                    <view class="title">账号：</view>
                    <view class="txt">{{ info.user_login }}({{ info.roleName }})</view>
                </view>
                <view class="content-box device-code">
                    <view class="title">设备数量：</view>
                    <view class="txt">{{ info.nums }}台</view>
                </view>
                <view class="content-box device-code" v-if="false">
                    <view class="title">{{ vPointName }}地址：</view>
                    <view class="txt">{{ info.address || '暂无' }}</view>
                </view>
            </block>
            <view class="content-box device-code">
                <view class="title">订单数量：</view>
                <view class="txt">{{ info.order_count }}单</view>
            </view>
            <view class="content-box profit ">
                <view class="profit-box device-code">
                    <view class="title">{{ cardType === 'user' ? vPointName : '设备' }}销售额：</view>
                    <view class="txt">{{ info.all_amount }} 元</view>
                </view>
                <view class="profit-details" @click="goDetails">明细</view>
            </view>
        </view>
    </view>
</template>
<script>
export default {
    props: {
        info: { type: Object, default: {} },
        cardType: { type: String, default: 'user' },
        index: { type: [Number, String], default: 0 },
        linkmanDianwei: { type: String, default: '' },

    },
    data() {
        return {
        };
    },

    methods: {
        goDetails() {
            let param = '';
            if (this.cardType == 'user') {

                param = `?cardType=hotel&hotel_id=${this.info.hotel_id}&index=${this.index}&userName=${this.info.hotelName}&linkmanDianwei=${this.linkmanDianwei}`
            } else if (this.cardType === 'hotel') {
                param = `?cardType=device&device_sn=${this.info.device_sn}&index=${this.index}&userName=${this.info.device_sn}&linkmanDianwei=${this.linkmanDianwei}`
            } else if (this.cardType === 'manage') {
                param = `?cardType=user&uid=${this.info.id}&userName=${this.info.user_login}&linkmanDianwei=${this.linkmanDianwei}`
            }
            uni.navigateTo({ url: `/pagesB/dataAnalysis/DataAnalysis${param}` })
        }
    },
}
</script>


<style scoped lang='scss'>
.card {
    padding: 20rpx;
}

.device-code {
    >view:nth-child(1) {
        width: 180rpx;
        text-align: justify;
        text-align-last: justify;
        // border: 1px solid #000;
    }

    >view:nth-child(2) {
        width: 400rpx;

    }
}

.content {
    &-box {
        display: flex;
        font-size: $font-size-middle;

        .title {
            color: $textDarkGray;
            flex-shrink: 0;
        }

        .txt {
            color: $textBlack;
        }
    }

    .profit {
        justify-content: space-between;
        display: flex;

        &-box {
            display: flex;
        }

        &-details {
            color: $themeComColor;
            font-weight: 700;
        }
    }
}
</style>