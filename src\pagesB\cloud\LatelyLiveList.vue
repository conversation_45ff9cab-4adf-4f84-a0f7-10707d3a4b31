<template>
  <view>
    <BaseNavbar :title="title" />
    <ComList class="live_list" :loading-type="loadingType">
      <CloudVideoCard v-for="item in listData" :key="item.id" :info="item" />
    </ComList>
    <BaseBackTop @onPageScroll="onPageScroll" :scrollTop="scrollTop">
    </BaseBackTop>
  </view>
</template>

<script>
import BaseNavbar from "@/components/base/BaseNavbar.vue";
import ComList from "@/components/list/ComList.vue";
import CloudVideoCard from "@/components/card/CloudVideoCard.vue";
import myPull from "@/mixins/myPull.js";
import BaseBackTop from '@/components/base/BaseBackTop.vue'
export default {
  components: { BaseNavbar, ComList, CloudVideoCard,BaseBackTop },
  data() {
    return {
      scrollTop:0,
      title: "最近指导文章",
    };
  },
  methods: {
    onPageScroll(e) {
    
		this.scrollTop = e.scrollTop;
	},
    getList(page, done) {
      let data = {
        recommended: false,
        page,
        limit: 10,
        cat_id: this.tabList?.[this.curTabIndex]?.id,
      };
      this.$u.api.articleList(data).then((res) => {
        done(res);
      })
      .catch((err=>{
        console.log('错误信息',err)
      }))
    },
  },
  onLoad() {
    this.refresh();
  },
  mixins: [myPull()],
};
</script>

<style lang="scss" scoped>
.live_list {
  ::v-deep .list {
    padding-top: 0;
  }
}
</style>