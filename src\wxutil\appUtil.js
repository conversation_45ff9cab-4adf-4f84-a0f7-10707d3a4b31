//这个文件存放App.vue中存放的自定义方法，因为编译到支付宝小程序时，无法获取到App.vue中的自定义方法
const app=getApp()

//======应用程序全局方法======
// 引入js
function requirejs(e) {
    return require("wxutil/" + e + ".js");
}

// 二进制处理方法
function buf2hex(buffer) { // buf转16进制
    return Array.prototype.map.call(new Uint8Array(buffer), x => ('00' + x.toString(16)).slice(-2)).join('')
}

function buf2string(buffer) { // buf转字符串
    var arr = Array.prototype.map.call(new Uint8Array(buffer), x => x)
    var str = ''
    for (var i = 0; i < arr.length; i++) {
        str += String.fromCharCode(arr[i])
    }
    return str
}

function ab2hex(buffer) {   // 字符串转16进制
    let hexArr = Array.prototype.map.call(
        new Uint8Array(buffer),
        function (bit) {
            return ('00' + bit.toString(16)).slice(-2)
        }
    )
    return hexArr;
}

//====== 获取全局变量方法 ======
function biadMakePhoneCall() {//拨打电话,客服电话
    uni.makePhoneCall({
        phoneNumber: app.globalData.servicePhone,
    })
}

function getModel() { //获取手机型号
    return app.globalData.SystemInfo["model"]
}

function getVersion() { //获取微信版本号
    return app.globalData.SystemInfo["version"]
}

function getSystem() { //获取操作系统版本
    return app.globalData.SystemInfo["system"]
}

function getPlatform() { //获取客户端平台
    return app.globalData.SystemInfo["platform"]
}

function getSDKVersion() { //获取客户端基础库版本
    return app.globalData.SystemInfo["SDKVersion"]
}

function versionCompare(ver1, ver2) { //版本比较
    var version1pre = parseFloat(ver1)
    var version2pre = parseFloat(ver2)
    var version1next = parseInt(ver1.replace(version1pre + ".", ""))
    var version2next = parseInt(ver2.replace(version2pre + ".", ""))
    if (version1pre > version2pre)
        return true
    else if (version1pre < version2pre)
        return false
    else {
        if (version1next > version2next)
            return true
        else
            return false
    }
}

function checkSysBluetoothEnable(){ //检测微信是否支持蓝牙功能
    if (getPlatform() == 'android' && versionCompare('6.5.7', getVersion())) {
        return false;
    }else if (getPlatform() == 'ios' && versionCompare('6.5.6', getVersion())) {
        return false;
    }
    return true;
}

//通知上报蓝牙事件
/*function postEvent(device_sn, event_value, electric = 0) { // 通知蓝牙设备链接了
    const api = require('@/wxutil/api.js');
    let data = {
        event:    event_value,
        device_sn: device_sn,
        electric: electric
    }
    api.reportEvent(data);
}*/

//========  其他方法  ======
//电压计算方法
function calcelectric(elec) {
    let elc =  (elec / 100 * 8) - 0.2;
    elc = elc.toFixed(2)
    return elc
}

//记录访问页
function setPage() {
    var pages = getCurrentPages() //获取加载的页面
    var currentPage = pages[pages.length - 1] //获取当前页面的对象
    var url = currentPage.route //当前页面url
    var options = currentPage.data
    //console.log(options)
    app.globalData.currenPage = '/'+url + '?pageCur=' + options.pageCur;
    app.globalData.pageName = options.pageCur;
}

// 更新界面的方法
function updateGoodsStatus(channel){
    var pages = getCurrentPages();
    var curPage = pages[pages.length - 1];//当前页
    curPage.outGoodsSuccess(channel);
}

function showLoading(tip,methodName = '') {
    uni.showLoading({
        title: tip,
        success:(res) => {
            console.log('方法名 ' + methodName + ' showLoading Succcess ', res,new Date());
        },
        fail:(res) => {
            console.log('方法名 ' + methodName + ' showLoading fail ', res,new Date());
        }
    });
}

function hideLoading(methodName = '') {
    uni.hideLoading({
        success:(res) => {
            console.log('方法名 ' + methodName + ' hideLoading Succcess ', res,new Date());
        },
        fail:(res) => {
            console.log('方法名 ' + methodName + ' hideLoading fail ', res,new Date());
        }
    });
}

export default {
    requirejs,
    buf2hex,
    buf2string,
    ab2hex,
    biadMakePhoneCall,
    getModel,
    getVersion,
    getSystem,
    getPlatform,
    getSDKVersion,
    versionCompare,
    checkSysBluetoothEnable,
    //postEvent,
    calcelectric,
    setPage,
    updateGoodsStatus,
    showLoading,
    hideLoading
}