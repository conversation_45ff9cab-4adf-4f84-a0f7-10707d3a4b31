<template>
  <view class="screener">
    <view class="screener_list">
      <view class="screener_list_item">
        <view class="title">{{titleName}}</view>
        <view>
          <BaseInput v-model="placeNmae" :placeholder="placeholderName" />
        </view>
      </view>
      <view class="screener_list_item">
        <view class="title">账号</view>
        <view class="vpointName">
          <view class="vpointName_zhe" @click="selectHotel">
            <view></view>
            <view class="icon">

              <u-icon name="arrow-right" color="#2979ff" size="45"></u-icon>
            </view>
          </view>
          <BaseInput placeholder="请选择账号" :disabled="true" type="number" v-model="linkmanDianwei" />

        </view>
      </view>
    </view>
    <view class="btn">
      <BaseButton :width="330" type="default" @onClick="resetData">重置</BaseButton>
      <BaseButton :width="330" type="primary" @onClick="confirm">确认</BaseButton>
    </view>
  </view>
</template>
  
<script>
import BaseInput from "@/components/base/BaseInput.vue";
import TimeSelect from "@/components/common/TimeSelect.vue";
import BaseButton from "@/components/base/BaseButton.vue";
export default {
  name: "PupSerch", //订单筛选
  components: { BaseInput, TimeSelect, BaseButton },
  props: {
    linkmanDianwei: {
      type: String,
      default: ''
    },
    type: {
      type: String,
      default: 'user'
    },

  },
  data() {
    return {
      placeNmae: "",
    };
  },
  computed: {

    placeholderName() {
      if (this.type == 'user') {
        // return `请输入${this.isExamine ? "通过" : "拒绝"}原因`;
        return `请输入${this.vPointName}名称`
      } else if (this.type == 'hotel') {
        return '请输入设备编号'
      } else if (this.type == 'device') {
        return '请输入订单编号'
      } else if (this.type == 'manage') {
        return '请输入用户名'
      }
      return '请输入提示'
    },
    titleName() {
      if (this.type == 'user') {
        // return `请输入${this.isExamine ? "通过" : "拒绝"}原因`;
        return `${this.vPointName}名称`
      } else if (this.type == 'hotel') {
        return '设备编号'
      } else if (this.type == 'device') {
        return '订单编号'
      } else if (this.type == 'manage') {
        return '用户名'
      }
    },
  },
  methods: {
    selectHotel() {
      this.$emit("selectHotel");
    },
    resetData() {
      this.placeNmae = "";
      this.$emit("resetData");
    },
    confirm() {
      let data = {
        hotelName: this.placeNmae,
      };
      this.$emit("confirm", data);
    },
  },
  mounted() {
    if (this.serchHotelName) {
      this.placeNmae = this.serchHotelName
    }
  }
};
</script>
  
<style lang="scss" scoped>
.title {
  width: 150rpx;
}

.vpointName {
  // border: 1px solid #000;
  position: relative;
  // border: 2rpx solid red;

  .icon {
    width: 50rpx;
    // border: 2rpx solid red;
  }

  .vpointName_zhe {
    width: 100%;
    height: 100%;
    // border: 1px solid #000;
    position: absolute;
    left: 0;
    top: 0;
    z-index: 999;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
}

.screener {
  padding: 50rpx 30rpx 30rpx;

  &_list {
    &_item {
      display: flex;
      align-items: center;

      >view {
        margin-bottom: 30rpx;

        &:first-child {
          margin-right: 20rpx;
          white-space: nowrap;
          font-size: $font-size-base;
          font-family: Source Han Sans CN;
          font-weight: bold;
          color: $textBlack;
        }

        &:last-child {
          width: 100%;
        }
      }

      .time_end {
        opacity: 0;
      }
    }
  }

  .btn {
    display: flex;
    justify-content: space-between;
  }
}
</style>