<template>
  <view class="">
    <BaseNavbar title="券码核销" />
    <view class="card" v-if="coupon_user_id">
      <view class="card-left">
        <image
          class="card-left-img"
          src="/pagesB/static/img/coupon/coupon_bg.png"
        />
        <view class="card-left-main">
          <view class="card-left-box">
            <view class="card-left-box-unit">￥</view>
            <view class="card-left-box-price">{{
              couponDetails.coupon_info.money
            }}</view>
          </view>
          <view class="card-left-title">优惠券</view>
        </view>
      </view>
      <view class="card-right">
        <view class="card-right-item">
          用户昵称：{{ couponDetails.user_info.user_nickname || "未知" }}
        </view>
        <view class="card-right-item">
          {{vPointName}}：{{ couponDetails.hotel_info.hotelName }}
        </view>
        <view class="card-right-item">
          领取时间：{{ $u.timeFormat(receiveTime, "yyyy.mm.dd") }}
        </view>
        <view
          class="card-right-item"
          :class="{ active: couponDetails.user_coupon.is_write_off === 0 }"
        >
          是否已核：{{ writeOff }}
        </view>
      </view>
    </view>
    <view class="describe">
      <view class="title">使用规则</view>
      <view class="tips">
        <view>・优惠券仅限在有效期间内使用，过期作废。</view>
        <view>・优惠券不能兑换现金，不找零，不退换。</view>
        <view>・一张订单只能使用一张优惠券，无法使用两张 或以上的优惠券。</view>
        <view>
          ・使用了优惠券的商品发生退货退款时，退款金额上限为顾客实际支付给本公司的金额，优惠券不可退换。
        </view>
      </view>
    </view>

    <view
      class="btn"
      v-if="couponDetails.user_coupon.is_write_off === 0"
      :style="{ bottom: vIphoneXBottomHeight + 20 + 'rpx' }"
    >
      <BaseButton shape="circle" @onClick="writeHandle"> 立即核销 </BaseButton>
    </view>
  </view>
</template>
<script>
import BaseButton from "../../components/base/BaseButton.vue";
import BaseNavbar from "@/components/base/BaseNavbar.vue";
export default {
  components: { BaseNavbar, BaseButton },
  computed: {
    writeOff() {
      let is_write_off = {
        0: "未核销",
        1: "已核销",
      };
      return (
        is_write_off[this.couponDetails?.user_coupon?.is_write_off] || "未知"
      );
    },
    receiveTime() {
      return this.couponDetails?.user_coupon?.time;
    },
  },
  data() {
    return {
      coupon_user_id: undefined,
      couponDetails: {},
      info: {},
    };
  },
  methods: {
    async getCouponHotelUserDetailHandle() {
      let data = {
        coupon_user_id: this.coupon_user_id,
      };
      this.couponDetails = await this.$u.api.getCouponHotelUserDetail(data);
    },
    async writeHandle() {
      let data = {
        coupon_user_id: this.coupon_user_id,
      };
      await this.$u.api.writeOffCouponHotel(data);
      this.isShowSuccess("核销成功", 0, () => {
        this.getCouponHotelUserDetailHandle();
      });
    },
  },
  onLoad({ id }) {
    this.coupon_user_id = id;
    this.coupon_user_id && this.getCouponHotelUserDetailHandle();
  },
};
</script>
<style lang="scss">
page {
  background-color: $pageBgColor;
}
</style>

<style scoped  lang='scss'>
.card {
  height: 210rpx;
  display: flex;
  justify-content: space-between;
  border-radius: 10rpx;
  margin: 20rpx;
  background-color: #fff;
  &-left {
    flex: 0 0 315rpx;
    position: relative;
    &-img {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      width: 100%;
      height: 100%;
      border-radius: 10rpx 0 0 10rpx;
      z-index: 0;
    }
    &-main {
      position: relative;
      width: 100%;
      height: 100%;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      color: #fff;
      z-index: 1;
    }
    &-box {
      display: flex;
      align-items: flex-end;

      &-unit {
        font-size: 30rpx;
      }
      &-price {
        font-size: 48rpx;
      }
    }
    &-title {
      font-size: 28rpx;
    }
  }
  &-right {
    flex: 1;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    color: #757575;
    padding: 16rpx 16rpx 24rpx 28rpx;
    box-sizing: border-box;
    .active {
      color: red;
    }
  }
}
.describe {
  display: flex;
  justify-content: space-between;
  margin: 20rpx;
  .title {
    color: #333;
    font-size: 32rpx;
    flex-shrink: 0;
  }
  .tips {
    margin-left: 20rpx;
    color: #babfc3;
    font-size: 24rpx;
  }
}
.btn {
  position: fixed;
  bottom: 20rpx;
  left: 0;
  right: 0;
  padding: 0 50rpx;
}
</style>