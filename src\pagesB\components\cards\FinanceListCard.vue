<template>
  <view class="card content">
    <view class="content-top">
      <view class="content-top-title">{{ info.type }}</view>
      <view class="content-top-name">{{ info.user_login }}</view>
      <view class="content-top-money">{{ info.value.toFixed(2) }}</view>
    </view>
    <view class="time">{{ info.time }}</view>
  </view>
</template>

<script>
export default {
  name: 'FinanceListCard',
  props: {
    info: { type: Object, default: {} },
  },
}
</script>

<style lang="scss" scoped>
.content {
  padding: 30rpx 20rpx;
  &-top {
    display: flex;
    // justify-content: space-between;
    margin-bottom: 24rpx;

    &-title {
      font-size: $font-size-middle;
      font-weight: 700;
      color: $textBlack;
    }
    &-name {
      font-size: $font-size-small;
      color: $textDarkGray;
    }
    &-money {
      font-size: $font-size-middle;
      font-weight: 700;
      color: $textWarn;
    }
  }
  .time {
    font-size: $font-size-small;
    color: $textDarkGray;
  }
  .content-top-title {
    white-space: nowrap; /* 防止文本换行 */
    overflow: hidden; /* 隐藏溢出的文本 */
    text-overflow: ellipsis; /* 显示省略号 */
    width: 300rpx;
    margin-right:20rpx;
  }
  .content-top-name{
    width: 200rpx;
    margin-right:20rpx;
    white-space: nowrap; /* 防止文本换行 */
    overflow: hidden; /* 隐藏溢出的文本 */
    text-overflow: ellipsis; /* 显示省略号 */
  }
  .content-top-money{
    text-align: right;
    width: 150rpx; 
    white-space: nowrap; /* 防止文本换行 */
    overflow: hidden; /* 隐藏溢出的文本 */
    text-overflow: ellipsis; /* 显示省略号 */
  }
}
</style>
