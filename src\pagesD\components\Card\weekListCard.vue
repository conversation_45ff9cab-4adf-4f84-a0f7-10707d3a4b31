<template>
    <view class="card" @click="onCheck">
      <view class="card_left" >
        <BaseCheck :checked.sync="info.isCheck" :title="info.name"  />
  
      </view>
    </view>
  </template>
  
  <script>

  import BaseCheck from "@/components/base/BaseCheck.vue";
  
  export default {
    components: { BaseCheck },
    name: "weekListCard",
    props: {
      info: { type: Object, default: {} },
    },
    methods: {
      onCheck() {
        this.$emit("onCheck");
      },
    },
  };
  </script>
  
  <style lang="scss" scoped>
  .card {
    display: flex;
    align-items: center;
    padding: 10rpx;
    &_right {
      flex: 1;
      > view {
        display: flex;
        justify-content: space-between;
        font-size: $font-size-base;
        color: $textGray;
        margin-bottom: 10rpx;
        :last-child {
          margin-bottom: 0;
        }
        .title {
          text-align: right;
          width: 136rpx;
          display: inline-block;
        }
        .themeColor {
          color: $themeComColor;
        }
  
        .textBlack {
          color: $textBlack;
        }
      }
      .remain_money {
        width: 190rpx;
        display: flex;
        justify-content: space-between;
      }
    }
  }
  </style>