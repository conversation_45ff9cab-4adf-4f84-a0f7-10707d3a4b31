<template>
  <view class="RemoteEquipment">
    <BaseNavbar :title="title"></BaseNavbar>
    <view class="remote">
      <!-- 顶部区域 -->

      <!-- 内容区域 -->
      <view class="remote_content">
        <!-- 内容区域顶部 -->
        <!-- <view class="content_top"> </view> -->

        <view class="remote_top">
          <view class="remote_txtt">
            {{ vPointName }}名称：{{ hotelName ? hotelName : '' }}
          </view>
          <view>{{ vPointName }}账号：{{ user_login ? user_login : '' }}</view>
        </view>
        <!-- 内容区域扫码部分 -->
        <view class="content_center">
          <view @click="sweep()">
            <view class="bilud">
              <img src="./../static/img/sweep.png" alt="" />
            </view>
            <view>
              <test class="center_2">扫一扫添加设备</test>
            </view>
          </view>
        </view>
        <!-- 标题 -->
        <view class="flex" v-if="isMove">
          <view class="title1">
            第一步
          </view>
          <view class="title2">
            请选择要迁移的设备
          </view>
        </view>
        <view class="flex2" v-if="isMove">
          <view class="title1">
            当前设备列表
          </view>
          <view class="title2" @click="clearList()">
            清空列表
          </view>
        </view>
        <!-- 内容区域内容咧白哦部分 -->
        <block v-if="!errors">
          <view class="content_main" v-if="dataList.length > 0" :style="{ height: isMove ? '450rpx' : '550rpx' }">
            <view v-for="(item, i) in dataList" :key="i">
              <view class="content_lt">
                {{ numm(i) }}
              </view>
              <view class="content_icon" @click="deletData(item)">
                <u-icon name="close-circle-fill" color="red" size="40"></u-icon>
              </view>
              <view class="comten_mb">
                系统码：{{ item.vCode || '未绑定' }}
              </view>
              <view>设备码：{{ item.device_sn }}</view>
            </view>
          </view>
          <view class="content_sweep" v-else>
            <view>
              请点击扫一扫添加设备
            </view>
          </view>
        </block>
        <block v-else>
          <view class="content_main" :style="{ height: isMove ? '450rpx' : '550rpx' }">
            <view v-for="(item, i) in errorDataList" :key="i">
              <view class="content_lt">
                {{ numm(i) }}
              </view>
              <view class="content_rt" v-if="item.status == 1">
                <image src="./../static/img/error.png"></image>
              </view>
              <view class="comten_mb">
                系统码：
                <text :class="item.status == 1 ? 'redt' : 'greent'">
                  {{ item.vCode || '未绑定' }}
                </text>
              </view>
              <view>
                设备码：
                <text :class="item.status == 1 ? 'redt' : 'greent'">
                  {{ item.device_sn }}
                </text>
              </view>
            </view>
          </view>
        </block>
        <block v-if="isMove">
          <!-- 标题 -->
          <view class="flex">
            <view class="title1">
              第二步
            </view>
            <view class="title2">
              请选择要迁移的{{ vPointName }}
            </view>
          </view>
          <!-- 场地选择 -->
          <view class="flex input">
            <view class="test">
              {{ hotelName ? hotelName : '' }}
            </view>
            <image class="img" src="./../static/img/goto.jpg"></image>
            <view class="test" :class="{ greent: !hotelNewName }" @click="selectplace">
              {{ hotelNewName ? hotelNewName : `选择迁移${vPointName}` }}
            </view>

          </view>
        </block>

        <!-- 内容区域内容咧白哦部分 -->
        <view class="content_btn">
          <view>
            <BaseButton type="eixt" @onClick="clicke()">{{ isMove ? '批量迁移' : '批量绑定' }}</BaseButton>
          </view>
        </view>
      </view>
    </view>

    <BaseProGressList @errorChange="errorChange" :titles="titles" :total="total" />
  </view>
</template>

<script>
import BaseNavbar from '@/components/base/BaseNavbar.vue'
import BaseButton from '@/components/base/BaseButton.vue'
import { getUrlParams, getUrlDynamicData } from '@/common/tools/utils.js'
import BaseProGressList from '@/components/base/BaseProGressList.vue'
/* #ifdef H5 */
import wx from 'weixin-js-sdk'
/* #endif */
export default {
  components: {
    BaseNavbar,
    BaseButton,
    BaseProGressList,
  },
  data() {
    return {
      title: '批量绑定',
      hotelName: '小二',
      user_login: '13588',
      dataList: [],
      systematicCode: '',
      hotel_id: '',
      mid: '',
      room_num: '',
      errorDataList: [],
      errors: false,
      titles: '批量绑定',
      isMove: false,
      list: [],
      hotelNewName: '',
      hotel_new_id: '',
      device_sn: ''
    }
  },
  computed: {
    total() {
      if (this.dataList.length > 0) {
        return this.dataList.length
      }
      return 0
    },
  },
  methods: {
    /* 清空列表 */
    clearList() {
      this.dataList.splice(0)
    },
    /* 修改场地方 */
    selectplace() {
      uni.navigateTo({
        url: `/pagesB/place/SelectPlace?from=home_device_bind_place`,
      })
    },
    /* 防抖 */
    setTime() {
      let timestamp = new Date().valueOf()
      if (timestamp - this.lastOrderTime < 5000) {
        uni.showToast({
          title: `其他指令正在执行请等${5 - ((timestamp - this.lastOrderTime) / 1000).toFixed(0)+1
            }秒后进行其他操作`,
          icon: 'none',
          duration: 1500,
        })
        // this.lastOrderTime = timestamp;
        return false
      } else {
        this.lastOrderTime = timestamp
        return true
      }
    },
    numm(i) {
      let a
      if (i < 9) {
        a = '0' + (i + 1)
      } else {
        a = i + 1
      }
      return a
    },
    /* 点击删除 */
    deletData(items) {
      let i = this.dataList.indexOf(items)
      this.dataList.splice(i, 1)
    },
    /* 获取信息 */
    async getUMDdetail(datas) {
      let data = {
        ...datas
      }
      let rtnData = await this.$u.api.getUMDetail({ ...data })

      if (rtnData) {
        let parames = {
          vscode: rtnData.vCode,
          device_sn: rtnData.device_sn,
          room_num: rtnData.room_num,
          mid: rtnData.id,
        }
        let a = -1
        if (this.dataList.length > 0) {
          a = this.dataList.findIndex(
            (item) => item.device_sn == parames.device_sn,
          )
        }
        if (a == -1) {
          this.dataList.push(parames)
        } else {
          this.isShowErr('已经添加过该设备')
        }

        //售袋机系统，此时需要额外保存地址信息和上传的图片信息
      } else {
        if (this.fromData == 'home') this.isShowErr('设备编号不正确~', 1)
      }
    },
    /*  扫码 */
    sweep() {
      /* #ifndef H5 */
      uni.scanCode({
        success: async ({ result, scanType, charSet, path }) => {
          let systematicCode
          let vscode = ''
          let device_sn = ''
          let mid = ''
          if (result.includes('vscode')) {
            systematicCode = getUrlParams(result, 'vscode')
            vscode = systematicCode
          } else if (result.includes('device_sn')) {
            systematicCode = getUrlDynamicData(result, 'device_sn')
            device_sn = systematicCode
          } else if (result.includes('mid')) {
            systematicCode = getUrlDynamicData(result, 'mid')
            mid = systematicCode
          } else {
            return this.promptError()
          }

          // let data = {
          //     mid:'1',
          //     device_sn:systematicCode
          // }
          // this.dataList.push(data)

          let data = {
            vscode,
            device_sn,
            mid,
          }

          this.getUMDdetail({ ...data })
          console.log('扫描二位码解析的信息', systematicCode)
        },
        fail: (error) => { },
      })
      /* #endif */
      /* #ifdef H5 */
      if (typeof AlipayJSBridge != 'undefined') {
        let that=this
        AlipayJSBridge.call(
          'scan',
          {
            type: 'qr', // 扫描类型  qr 二维码  / bar 条形码
            // actionType: "scanAndRoute",// 如果只是扫码,拿到码中的内容，这项不用设置都可以
          },
          (res) => {
            // alert(JSON.stringify(res));
            if (res.error == 10) {
              // 错误码为10：用户取消操作
              Toast('取消操作')
            } else if (res.error == 11) {
              // 错误码为11：扫码失败
              Toast('网络异常，请重试')
            } else {
              // res.codeContent为扫码返回的结果
              // window.location.replace(res.codeContent)
              let result = decodeURIComponent(res.codeContent)
              let data = {
              }
              if (result.includes('vscode')) {
                alert(1)
                data['vscode'] = getUrlParams(result, 'vscode')
                alert(data.vscode)
              } else if (result.includes('device_sn')) {
                alert(2)
                data['device_sn'] = getUrlDynamicData(result, 'device_sn')
                alert(data.device_sn)
              } else if (result.includes('mid')) {
                alert(2)
                data['mid'] = getUrlDynamicData(result, 'mid')
                alert(data.mid)
              } else {
                return that.promptError()
              }


              that.getUMDdetail({ ...data })
            }
          },
        )
      } else if (typeof WeixinJSBridge != 'undefined') {
        let uri = location.href.split('#')[0]
        // let uri = this.vUrl
        let data = {
          url: uri,
        }
        let that = this

        this.$u.api.getJsSign(data)
          .then((res) => {
            wx.config({
              debug: false, // 开启调试模式,调用的所有api的返回值会在客户端alert出来，若要查看传入的参数，可以在pc端打开，参数信息会通过log打出，仅在pc端时才会打印。
              appId: res.appId, // 必填，公众号的唯一标识
              timestamp: res.timestamp, // 必填，生成签名的时间戳
              nonceStr: res.nonceStr, // 必填，生成签名的随机串
              signature: res.signature, // 必填，签名
              jsApiList: ['scanQRCode'], // 必填，需要使用的JS接口列表, 这里只需要调用扫一扫
            })
            wx.ready(function () {
              wx.scanQRCode({
                needResult: 1, // 默认为0，扫描结果由微信处理，1则直接返回扫描结果，
                scanType: ['qrCode', 'barCode'], // 可以指定扫二维码还是一维码，默认二者都有
                success: (res) => {
                  // 扫码成功，跳转到二维码指定页面（res.resultStr为扫码返回的结果）
                  // window.location.replace(res.resultStr);
                  setTimeout(() => {
                    // window.location.replace(res.resultStr)
                    let result = decodeURIComponent(res.resultStr)
                    if (result.includes('vscode')) {
                      data['vscode'] = getUrlParams(result, 'vscode')
                    } else if (result.includes('device_sn')) {
                      data['device_sn'] = getUrlDynamicData(result, 'device_sn')
                    } else if (result.includes('mid')) {
                      data['mid'] = getUrlDynamicData(result, 'mid')
                    } else {
                      return that.promptError()
                    }
                    that.getUMDdetail({ ...data })
                  }, 20)
                },
              })
            })
          })
          .catch((err) => {
            console.log('错误结果', err)
          })
      } else {
        // uni.navigateTo({
        //   url: `/pagesD/Scan/Scan?from=batch&hotel_id=${this.hotel_id}`,
        // })
        alert('请使用微信打浏览器打开')
      }
      /* #endif */
    },
    //二维码错误提示
    promptError() {
      this.isShowErr('请扫码正确的二维码,二维码信息')
    },
    clicke() {

      if (this.dataList.length < 1) {
        return this.isShowErr('请添加设备再操作')
      } else if (this.errors) {
        return this.isShowErr('您已经进行过该操作,请返回重新操作')
      }
      if (this.isMove && this.hotelNewName == '' || this.hotelNewName == this.hotelName) {
        return this.isShowErr(`要迁移的${this.vPointName}不能为空或与当前场地方相同`)
      }

      this.isShowTwo('', 1)
      if (this.isMove) {
        let data = {
          hotel_id: this.hotel_id,
        }
        let parames = {
          hotel_id: this.hotel_new_id,

        }
        const requestCallback = async (data) => {
          return this.$u.api.unbindHotel(data)
        }
        const requestCallbackTwo = async (data) => {
          return this.$u.api.bindHotel(data)
        }
        uni.$emit('customEvent', this.dataList, data, 3000, requestCallback, false, parames, requestCallbackTwo)
      } else {
        let data = {
          hotel_id: this.hotel_id,
        }
        const requestCallback = async (data) => {
          return this.$u.api.bindHotel(data)
        }
        uni.$emit('customEvent', this.dataList, data, 3000, requestCallback)
      }

      // uni.setStorageSync("bindId_key", data);
    },
    /* 错误返回 */
    errorChange(list) {
      console.log('返回错误列表', list)
      if (list.length > 0) {
        for (let i = 0; i < this.dataList.length; i++) {
          let a = list.findIndex(
            (item) => item.device_sn == this.dataList[i].device_sn,
          )
          let data = {}
          if (a != -1) {
            data = {
              ...this.dataList[i],
              status: 1,
            }
          } else {
            data = {
              ...this.dataList[i],
            }
          }
          this.errorDataList.push(data)
        }
      } else {
        this.errorDataList = this.dataList
      }
      console.log('错误列表修改后信息', this.errorDataList)
      this.errors = true
      this.isShowErr('完成操作')
    },
  },
  onLoad(opt) {
    console.log(opt, '>>>>>>>>>>>>>>>>>')
    if (opt?.from) {
      this.fromData = opt.from
      if (opt.from == 'device') {
        // 设备绑定来的
        this.hotel_id = opt?.hotel_id
        this.hotelName = opt?.hotelName
        this.user_login = opt?.user_login
        // this.mid = opt?.mid;
      } else if (opt.from == 'Scan') {
        if (opt.mid) {
          let mid = opt.mid
          this.hotel_id = opt.hotel_id
          let data = {
            mid,
          }

          this.getUMDdetail({ ...data })
          console.log('扫描二位码解析的信息', data)
        }
      } else if (opt.from == 'move') {
        this.hotel_id = opt?.hotel_id
        this.hotelName = opt?.hotelName
        this.user_login = opt?.user_login
        // this.mid = opt?.mid;
        this.isMove = true
        let data = JSON.parse(decodeURIComponent(opt.list))
        this.dataList = [...data]
      }
    }
  },
  onShow() {
    /*  #ifndef H5 */
    let pages = getCurrentPages()
    let currPage = pages[pages.length - 1] // 当前页
    if (currPage.data.isDoRefresh == true) {
      // 是否刷新
      this.isShowTwo('', 1)
    }
    if (currPage.data.item) {
      // 有值
      // 修改listData中值
      this.checkHotel = currPage.data.item
      this.hotelNewName = this.checkHotel.hotelName
      this.hotel_new_id = this.checkHotel.id
    }
    /*#endif */
    /*  #ifdef H5 */
    if (this.vCurrPage.isDoRefresh == true) {
      // 是否刷新
      this.isShowTwo('', 1)
    }
    this.checkHotel = this.vCurrPage.item
    this.hotelNewName = this.checkHotel.hotelName
    this.hotel_new_id = this.checkHotel.id
    /*#endif */

  },
}
</script>
<style lang="scss">
page {
  background-color: $pageBgColor;
}
</style>
<style lang="scss" scoped>
.RemoteEquipment {
  width: 100%;
  min-height: 100vh;
  box-sizing: border-box;
  overflow: auto;

  #specialEffect {
    transition: all 1.2ms ease-in;
  }

  #specialEffect:hover {
    background: linear-gradient(100deg, #46a1ff 0%, rgb(255, 91, 91) 100%);
  }

  .remote {
    width: 100%;
    height: 100%;
    padding: 10rpx 25rpx 0rpx 25rpx;
    box-sizing: border-box;

    .remote_top {
      width: 85%;
      margin: auto;
      color: #666;
      display: flex;
      margin-top: 20rpx;
      border-radius: 15rpx;
      flex-direction: column;
      text-align: center;
      font-size: 28rpx;
      padding: 15rpx 0;
      // border: 1px solid #000;
      // background-color: #e7e7e7;
    }

    .content_center {
      // border: 1px solid #000;
      display: flex;
      justify-content: center;
      align-items: center;

      >view {
        font-size: 28rpx;
        font-weight: bolder;
        // border: 1px solid #000;
        text-align: center;
      }
    }

    .remote_txtt {
      margin-bottom: 10rpx;
      overflow-wrap: normal;
    }

    .content_sweep {
      margin: 20rpx 0;
      // border: 1px solid #000;
      height: 550rpx;
      padding: 15rpx;
      display: flex;
      text-align: center;
      // flex-wrap: wrap;
      // overflow-y: auto;
      justify-content: center;

      // align-items: center;
      // border: 1px solid #000;
      >view {
        // border: 1px solid #000;
        // width: 500rpx;
        // height: 100rpx;
      }
    }

    .content_main {
      margin: 10rpx 0;
      // border: 1px solid #000;
      padding: 15rpx;
      overflow: scroll;
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
      align-content: flex-start;

      >view {
        height: 80rpx;
        border-radius: 15rpx;
        // flex:0 1 0;
        width: 330rpx;
        margin: 10rpx 0;
        // border: 1px solid #000;
        background-color: $pageBgColor;
        padding: 10rpx 10rpx;
        padding-left: 25rpx;
        font-size: 22rpx;
        position: relative;

        .content_icon {
          position: absolute;
          right: -7px;
          top: -8px;
        }

        .content_lt {
          position: absolute;
          left: 0px;
          top: 0px;
          background-color: red;
          // border: 1px solid #000;
          font-size: 15rpx;
          width: 40rpx;
          height: 40rpx;
          padding: 5rpx 5rpx;
          color: white;

          clip-path: polygon(0 0, 100% 0, 0 100%, 0 0);
        }

        .content_rt {
          position: absolute;
          right: 0px;
          top: 0px;
          // background-color: red;
          // border: 1px solid #000;
          font-size: 13rpx;
          width: 120rpx;
          height: 80rpx;
          padding-left: 15rpx;
          padding-top: 5rpx;
          color: white;

          // clip-path: polygon(100% 0, 100% 0, 100% 100%, 0 0);
          image {
            width: 100%;
            height: 100%;
          }
        }


        .greenb {
          background-color: $themeComColor;
          color: white;
        }

        .redt {
          color: red;
        }

        .redb {
          background-color: red;
          color: white;
        }
      }

      .red {
        color: red;
      }

      .bacred {
        width: 25rpx;
        height: 15rpx;
        background-color: red;
        color: white;
        font-size: 15rpx;
      }

      .comten_mb {
        border-bottom: 2rpx solid rgb(219, 219, 219);
      }
    }

    .content_btn {
      margin-top: 25rpx;
      margin-bottom: 45rpx;
    }

    .remote_content {
      width: 100%;
      min-height: 600rpx;
      max-height: auto;
      background: rgb(255, 255, 255);
      z-index: 1;
      border-radius: 20rpx;
      box-shadow: rgba(#333, 20%);
      overflow: hidden;
      margin-top: 15rpx;
      position: relative;

      img {
        width: 300rpx;
        height: 300rpx;
      }
    }

    .remote_txtt {
      font-size: 40rpx;
      font-weight: bolder;
    }
  }
}

.greent {
  color: $themeComColor;
}

.center_2 {
  padding: 10rpx 20rpx;
  border-radius: 15rpx;
  background-color: #46a1ff;
  color: #fff;
}

.flex2 {
  display: flex;
  justify-content: space-between;
  padding: 0 20rpx;
  margin-top: 15rpx;

  .title2 {
    color: #46a1ff;
  }
}

.flex {
  margin-top: 15rpx;
  display: flex;
  align-items: center;
  padding: 0 20rpx;

  .title1 {
    color: #46a1ff;
    font-size: 40rpx;
    font-weight: bold;
    margin-right: 20rpx;
  }

  .title2 {
    color: #333;
    font-size: 35rpx;
    font-weight: bold;
  }
}

.input {
  // border: 2rpx solid red;
  height: 80rpx;
  justify-content: space-between;
  padding: 0 20rpx;

  .img {
    height: 80%;
    width: 100rpx;
  }

  .test {
    background-color: #f2f2f2;
    padding: 20rpx;
    display: flex;
    align-items: center;
    width: 250rpx;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    color: #333;
    justify-content: center;
    border-radius: 10rpx;
  }

  .greent {
    color: #9a9a9a !important;
  }
}

.default {
  text-align: center;
  font-size: 26rpx;
}
</style>
