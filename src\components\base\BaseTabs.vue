<template>
  <view class="baseTab" :style="{ width }">
    <u-tabs class="defaultStyle"
      :name="name"
      :is-scroll="isScroll"
      :list="list"
      :height="height"
      :current="current"
      :show-bar="isShowBar"
      font-size="28"
      active-color="#0EADE2"
      inactive-color="#333333"
      bg-color="#fff"
      :bold="true"
      @change="change"
      bar-width="80"
    ></u-tabs>
  </view>
</template>

<script>
export default {
  name: "BaseTabs",
  props: {
    width: { type: String, default: "100%" },
    height: { type: [Number,String], default: "60" },
    isScroll: { type: Boolean, default: false },
    name:{type:String,default:'name'},
    list: {
      type: Array,
      default: function () {
                return [];
            },
    },
    isShowBar: { type: Boolean, default: false },
    current:{type:Number,default:0}
  },
  data() {
    return {
      // current: 0,
    };
  },
  methods: {
    change(e) {
      // this.current = e;
      this.$emit("change", e, this.list[e]);
    },
  },
};
</script>

<style lang="scss" scoped>
.baseTab {
  width: 100%;
  flex: 1;
  
}
</style>