<template>
  <view>
    <view class="sticker">
      <BaseNavbar :title="title" />
      <BaseTabs :current="curTabIndex" :list="tabsList" :isShowBar="false" @change="tabChange" />
    </view>

    <ComList :loading-type="loadingType">
      <FinanceAuditCard
        v-for="item in listData"
        :key="item.id"
        :info="item"
        @refresh="refresh"
        @handleCheck="handleCheck"
      />
    </ComList>
  </view>
</template>

<script>
import BaseNavbar from "@/components/base/BaseNavbar.vue";
import BaseTabs from "@/components/base/BaseTabs.vue";
import ComList from "@/components/list/ComList.vue";
import FinanceAuditCard from "../components/cards/FinanceAuditCard.vue";
import myPull from "../../mixins/myPull";
export default {
  components: {
    BaseNavbar,
    BaseTabs,
    ComList,
    FinanceAuditCard,
  },
  mixins: [myPull()],
  data() {
    return {
      title: "财务审核",
      tabsList: [
        {
          name: "审核中",
          status: 0,
        },
        {
          name: "审核通过",
          status: 1,
        },
        {
          name: "付款成功",
          status: 2,
        },
        {
          name: "付款失败",
          status: 3,
        },
        {
          name: "审核失败",
          status: -1,
        },
      ],
      curTabIndex: 0,
    };
  },
  methods: {
    tabChange(e) {
      this.curTabIndex = e;
      this.refresh();
    },
    getList(page, done) {
      let data = {
        page,
        status: this.tabsList[this.curTabIndex].status,
      };
      this.$u.api.getWithDrawalsDealList(data).then((res) => {
        done(res);
      });
    },
  },
  onLoad() {
    this.refresh();
  },
};
</script>
<style lang="scss">
page {
  background-color: $pageBgColor;
}
</style>
<style lang="scss" scoped>
.check-number {
  margin-bottom: 20rpx;
}
</style>