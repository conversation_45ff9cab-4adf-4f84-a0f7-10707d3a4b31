<template>
  <view>
    <BaseNavbar title="积分任务管理" />
    <FixedAddIcon @onAdd="goGoodsAdd" />
    <ComList :loading-type="loadingType">
      <ScoreListCard
        v-for="item in listData"
        :key="item.id"
        :info="item"
        @refresh="refresh"
      />
    </ComList>
  </view>
</template>

<script>
import BaseNavbar from "@/components/base/BaseNavbar.vue";
import FixedAddIcon from "@/components/common/FixedAddIcon.vue";
import ScoreListCard from "../components/cards/ScoreListCard.vue";
import myPull from "@/mixins/myPull.js";
import ComList from "@/components/list/ComList.vue";
export default {
  components: { BaseNavbar, FixedAddIcon, ScoreListCard, ComList },
  methods: {
    goGoodsAdd() {
      uni.navigateTo({ url: "/pagesB/score/ScoreTaskAdd?from=add" });
    },
    getList(page, done) {
      let data = {
        page,
        limit: 10,
      };
      this.$u.api.getMyTask(data).then((res) => {
        this.$u.vuex("vTaskType", res.data.cat);
        done(res.data.list);
      });
    },
  },
  onLoad() {
    this.refresh();
  },
  onShow() {
    /*  #ifndef H5 */
    let pages = getCurrentPages();
    let currPage = pages[pages.length - 1]; // 当前页
    if (currPage.data.isDoRefresh == true) {
      // 是否刷新
      currPage.data.isDoRefresh = false;
      this.searchValue = "";
      this.refresh();
    }
    /*#endif */
    /*  #ifdef H5 */
    if (this.vCurrPage.isDoRefresh == true) {
      // 是否刷新
      this.vCurrPage.isDoRefresh = false;
      this.searchValue = "";
      this.refresh();
    }
    /*#endif */
  },
  mixins: [myPull()],
};
</script>
<style lang="scss">
page {
  background-color: $pageBgColor;
}
</style>
<style lang="scss" scoped>
</style>