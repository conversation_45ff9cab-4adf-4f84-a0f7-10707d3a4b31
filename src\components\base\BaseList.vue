<template>
  <view class="search-box" v-if="vServerList[listType].length > 0">
    <view class="search-title">最近在搜</view>
    <view class="search-list">
      <view
        v-for="(n, i) in vServerList[listType]"
        class="list-item"
        :key="i"
        @click="search(n)"
      >
        <view class="list-txt">{{ n }}</view>
        <view class="dis-top" @click.stop="exit(n)">
          X
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { deleteValueInObject } from '@/wxutil/list'
export default {
  name: 'BaseList',
  props: {
    listType: { type: String, default: 'analysisList' },
  },
  data() {
    return {}
  },

  mounted() {
    if (!this.vServerList[this.listType]) {
      this.$u.vuex(`vServerList.${this.listType}`, [])
    }
  },
  methods: {
    search(val) {
      this.$emit('searchChange', val)
    },
    exit(val) {
      this.$emit('exit', val)
      let item = [...this.vServerList[this.listType]]
      let items = deleteValueInObject(item, val)
      this.$u.vuex(`vServerList.${this.listType}`, items)
    },
  },
}
</script>

<style lang="scss" scoped>
.search-box{
    padding: 10rpx 30rpx;
    background-color: #fff;
    .search-title{
        font-size:20rpx;
        color:#9a9a9a;
        margin-bottom:10rpx;
    }
    border-bottom:2rpx solid #ecebeb;
}
.search-list {
  height: 50rpx;
  display: flex;

 
  .list-item {
    border: 2rpx solid #fab6b6;
    padding: 10rpx;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-right: 30rpx;
    background-color: #fef0f0;
    color: #fb7171;
    .list-txt {
      white-space: nowrap; /* 防止文本换行 */
      overflow: hidden; /* 隐藏超出容器的部分 */
      text-overflow: ellipsis; /* 显示省略号 */
      width: 80rpx;
    }
    .dis-top {
      //   margin-left: 40rpx;
      font-size: 20rpx;
    }
  }
}
</style>
