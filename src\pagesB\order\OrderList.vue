<template>
  <view>
    <view class="top">
      <BaseNavbar :title="title" />

      <BaseSearch typeImg="screen" listType="orderList" placeholder="请输入订单编号/支付单号" @search="search"
        @onClickIcon="onClickIcon" />
      <!-- <BaseList listType="orderList" @searchChange="searchChange" /> -->
    </view>

    <BaseDropdown :options-list="optionsList" :num="orderTotal" @change="changeDropdown"></BaseDropdown>
    <view class="subsection" @click="handleSubsection">
      <u-subsection mode="subsection" :active-color="mainColor" :list="subsectionList" :current="current"
        @change="changeSubsection" />
      <u-calendar v-model="showCalendar" mode="range" :safe-area-inset-bottom="true" btn-type="error"
        :range-color="mainColor" :active-bg-color="mainColor" :change-year="false"
        @change="changeCalendar"></u-calendar>
    </view>

    <ComList :loading-type="loadingType">
      <view v-for="(items, i) in pagesData.length" :key="i" :id="'pages-' + i" class="border">
        <view v-if="i < currentIndex + 2 && i > currentIndex - 2">
          <OrderListCard v-for="(item, index) in  pagesData[i]" :key="index" @retunOrder="retunOrder(item)"
            @onOff="onOff(item)" @start="start" :info="item" />
        </view>
        <view v-else :style="{ height: pageHeight[i] + 'px' }" class="border">

        </view>
      </view>
    </ComList>
    <BasePopup :show.sync="isShowPopup" mode="top" :customStyle="customStyle">
      <OrderScreener @confirm="confirmQuery" />
    </BasePopup>
    <BaseModal :show.sync="isShowDelModalFree" @confirm="confirmDelReturn" title="温馨提示">
      <view class="red">
        确认要退款给该用户吗，退款后将无法追回,
        <br />
        订单号{{ unBindItem.order_sn }},订单时间{{ unBindItem.add_time }}
      </view>
      <view class="title">1.请选择要退款的理由</view>
      <view class="modalTop">
        <BaseRadio :radioIndex.sync="radioIndex" :size="20" :list="reasonList" />
        <BaseInput v-if="radioIndex == 2" v-model="reason" placeholder="请输入其他理由" />
      </view>
      <view class="title">2.请选择要退款方式</view>
      <view>
        <u-radio-group v-model="value" @change="radioGroupChange" class="flexBottom">
          <u-radio @change="radioChange" class="flexBottom_item" v-for="(item, index) in list" :key="index"
            :name="item.name" :disabled="item.disabled">
            {{ item.name }}
          </u-radio>
        </u-radio-group>
      </view>
    </BaseModal>
    <BaseModal :show.sync="isShowStartModal" @confirm="confirmStart">
      <view slot="default">
        <BaseInput v-model="length_time" placeholder="请输入启动时长(分钟)" />
      </view>
    </BaseModal>
    <disableBaseModal :show="isShowDelModalOff" @confirmDelOff="confirmDelOff" @cancel="cancel" :index="index">
    </disableBaseModal>
    <BaseBackTop v-if="scrollTop > systemHeight" listShow @onScroll="onScroll">
    </BaseBackTop>
  </view>
</template>

<script>
import BaseNavbar from '@/components/base/BaseNavbar.vue'
import BaseSearch from '@/components/base/BaseSearch.vue'
import BaseDropdown from '@/components/base/BaseDropdown.vue'
import ComList from '@/components/list/ComList.vue'
import OrderListCard from '@/pagesB/components/cards/OrderListCard.vue'
import myPull from '@/mixins/myPull.js'
import BasePopup from '@/components/base/BasePopup.vue'
import OrderScreener from './components/OrderScreener.vue'
import BaseModal from '@/components/base/BaseModal.vue'
import BaseInput from '@/components/base/BaseInput.vue'
import disableBaseModal from '@/components/disable/disableBaseModal'
import { subtractDaysAndFormat } from '@/wxutil/times'
import uCalendar from '@/components/uni-calendar/u-calendar.vue'
// import BaseList from '@/components/base/BaseList.vue'
import { AddValueInObject } from '@/wxutil/list'
import BaseBackTop from '@/components/base/BaseBackTop.vue'
import BaseRadio from "../../components/base/BaseRadio.vue";
export default {
  components: {
    BaseNavbar,
    BaseSearch,
    BaseDropdown,
    ComList,
    OrderListCard,
    BasePopup,
    OrderScreener,
    BaseModal,
    BaseInput,
    disableBaseModal,
    uCalendar,
    // BaseList,
    BaseBackTop,
    BaseRadio
  },
  data() {
    return {
      title: '订单管理',
      optionsList: [
        {
          title: '全部',
          options: [
            { label: '全部', value: 0, status: 0 },
            {
              label: '订单完成',
              value: 1,
              status: 1,
            },
            {
              label: '订单异常',
              value: 2,
              status: 2,
            },
            {
              label: '订单取消',
              value: 3,
              status: 3,
            },
            {
              label: '已退款',
              value: 4,
              status: 50,
            },
            {
              label: '待付款',
              value: 5,
              status: 4,
            },
          ],
          value: 0,
        },
      ],
      dianwei: '',
      device_sn: '',
      order_sn: '',
      order_status: 0,
      room_num: '',
      start_time: '',
      end_time: '',
      orderTotal: 0, //订单总数
      //popup显示状态
      isShowPopup: false,
      selectItem: {},
      isShowDelModalFree: false,
      list: [
        {
          name: '原路返回',
          disabled: false,
        },
        {
          name: '线下退款',
          disabled: false,
        },
      ],
      value: '原路返回',
      subsectionList: [
        //     {
        //     name: '昨天'
        // },
        {
          name: '今天',
          status: 1,
        },
        {
          name: '昨天',
          status: 2,
        },

        {
          name: '前天',
          status: 3,
        },
        {
          name: '本月',
          status: 4,
        },

        {
          name: '上月',
          status: 5,
        },
        {
          name: '自定义',
          isCustom: true,
        },
      ],
      current: 0,
      showCalendar: false,
      mainColor: '#fa3534',
      unBindItem: {},
      list: [
        {
          name: '原路返回',
          disabled: false,
        },
        {
          name: '线下退款',
          disabled: false,
        },
      ],
      value: '原路返回',

      isShowDelModalOff: false,
      isShowStartModal: false, //启动
      index: 4,
      selectIndex: 0,
      length_time: 1,
      hotelName: '', //搜索
      reasonList: [
      //   {
      //   title: "泡泡液已用完",
      //   name: "0",
      //   disabled: false,
      //   selectIndex: 0,

      // }, 
      // {

      //   title: "时间没到就停止",
      //   name: "1",
      //   disabled: false,
      //   selectIndex: 1,

      // }, 
      // {
      //   title: "没泡泡液",
      //   name: "2",
      //   disabled: false,
      //   selectIndex: 2,


      // }, 
      {

        title: "启动失败",
        name: "0",
        disabled: false,
        selectIndex: 0,

      }, {
        title: "没有电了",
        name: "1",
        disabled: false,
        selectIndex: 1,

      }, {
        title: "其他理由",
        name: "2",
        disabled: false,
        selectIndex: 2,

      }],
      radioIndex: 0,
      reason: '',
      customStyle: {
        top: 110 + this.vStatusBarHeight + this.vNavBarHeight + 'rpx',
      },
      scrollTop: 0,
      currentIndex: 0,          // 当前页数 pageNum
      pageHeight: [],           // 每屏高度存储
      systemHeight: 0,           // 屏幕高度
      scrollControlDisabled: false,//是否只能滑动一屏
    }
  },
  methods: {
    computedHeight() {
      const query = uni.createSelectorQuery().in(this);
      for (let i = 0; i < this.pagesData.length; i++) {
        query.select('#pages-' + i).boundingClientRect(data => {
          if (data && data.height > 20) {
            this.pageHeight[i] = data.height;
          }
        }).exec();
      }
    },
    onScroll() {
      this.scrollControlDisabled = true;
      this.scrollTop = 0
      this.currentIndex = 0
      uni.pageScrollTo({
        scrollTop: 0,
        duration: 100,
        complete: () => {
          this.scrollControlDisabled = false;
        }
      });
    },
    unpdata() {
      this.refresh()
    },
    // onPageScroll(e) {
    //   this.scrollTop = e.scrollTop
    // },
    cancel() {
      this.isShowDelModalOff = false
    },
    /* 禁用设备 */
    onOff(item) {
      if (item.use_status == 1) {
        this.index = 4
      } else if (item.use_status == 2) {
        this.index = 5
      }
      this.isShowDelModalOff = true
      this.device_sn = item.device_sn
      this.unBindItem = item
    },
    /* 禁用设备弹窗 */
    async confirmDelOff(text) {
      this.isShowDelModalOff = false
      let use_status = 0
      if (this.index == 4) {
        use_status = 2
      } else if (this.index == 5) {
        use_status = 1
      }
      let title = ''
      if (use_status == 2) {
        title = text
      }
      let params = {
        device_sn: this.device_sn,
        use_status,
        reason: title,
      }
      try {
        let res = await this.$u.api.setupdateMachineStatus(params)
        this.device_sn = ''
        // this.refresh();
        let index = this.listData.findIndex(
          (items) => items.order_id === this.unBindItem.order_id,
        ) // 查找id为2的元素索引
        if (index !== -1) {
          // 如果找到了
          if (this.unBindItem.use_status == 1) {
            this.unBindItem.use_status = 2
            this.unBindItem.reason = title
          } else if (this.unBindItem.use_status == 2) {
            this.unBindItem.use_status = 1
            this.unBindItem.reason = title
          }
          this.listData.splice(index, 1, this.unBindItem)

        }
      } catch (error) {
        console.log(error)
      }

    },
    start(i, item) {
      // 补货管理才能有启动
      this.selectIndex = i
      this.isShowStartModal = true
      this.unBindItem = item
    },
    confirmStart() {
      // 开启游戏设备
      this.startGameDevice(this.selectIndex + 1, this.length_time)
    },
    async startGameDevice(channel, time) {
      // 启动设备
      // 启动设备

      let data = {
        device_sn: this.unBindItem.device_sn,
        channel: channel, // 货道
        length_time: time,
      }
      try {
        await this.$u.api.startUM(data)
        this.isShowSuccess('操作成功', 1, () => { }, true)
      } catch (error) {
        console.log('启动设备结果参数 3 ：', error)
      }

    },
    handleSubsection() {
      if (this.current === 5 && !this.showCalendar) this.showCalendar = true
    },
    changeCalendar(e) {
      let { startDate, endDate } = e
      this.handleData('', startDate, endDate)
    },
    handleData(date, startDate, endDate) {
      // this.queryDate = date
      this.start_time = startDate
      this.end_time = endDate
      this.refresh()
    },
    changeSubsection(i) {
      this.current = i
      let selectItem = this.subsectionList[i]

      if (selectItem.isCustom) {
        this.showCalendar = true
      } else {
        let date = new Date()
        if (selectItem.status < 4) {
          this.end_time = this.start_time = subtractDaysAndFormat(
            selectItem.status - 1,
          )
        } else if (selectItem.status == 4) {
          this.end_time =
            date.getFullYear() +
            '-' +
            (date.getMonth() + 1) +
            '-' +
            date.getDate()
          this.start_time =
            date.getFullYear() + '-' + (date.getMonth() + 1) + '-' + 1
        } else if (selectItem.status == 5) {
          let currentDate = new Date();
          let lastMonthDate = new Date(currentDate.getFullYear(), currentDate.getMonth(), 0);

          this.start_time = lastMonthDate.getFullYear() + '-' + (lastMonthDate.getMonth() + 1) + '-1';
          this.end_time = lastMonthDate.getFullYear() + '-' + (lastMonthDate.getMonth() + 1) + '-' + lastMonthDate.getDate();
        }
        this.handleData('', this.start_time, this.end_time)
      }
    },
    radioChange(e) {
      // console.log(e);
    },
    // 选中任一radio时，由radio-group触发
    radioGroupChange(e) {
      // console.log(e);
    },
    async confirmDelReturn() {
      let title = ''
      if (this.radioIndex == 2) {
        title = this.reason
      } else {
        title = this.reasonList[this.radioIndex].title
      }
      let data = {
        order_id: this.unBindItem.order_id,
        money: this.unBindItem.order_amount,
        refund_reason: title
        // money:'0.01'
      }
      if (this.value == '线下退款') {
        data['refund_type'] = 1
      } else {
        data['refund_type'] = 0
      }
      let that = this
      try {
        await this.$u.api.refundOrder(data)
        this.isShowSuccess('执行成功')
        setTimeout(() => {
          that.refresht()
        }, 2000)
      } catch (error) {
        console.log('error', error)
      }

    },

    retunOrder(item) {
      this.value = '原路返回'
      this.isShowDelModalFree = true
      this.unBindItem = item
    },

    refresht() {
      setTimeout(() => {
        this.refresh()
      }, 2500)
    },
    searchChange(val) {
      this.hotelName = val
      this.search(val)
    },
    search(val) {
      this.order_sn = val
      let list = AddValueInObject(this.vServerList.orderList, val)
      this.$u.vuex(`vServerList.orderList`, list)
      this.refresh()
    },
    changeDropdown(item) {
      this.optionsList = item

      this.order_status = item[0].options[item[0].value].status
      this.refresh()
    },
    async getList(page, done) {
      try {
        let data = {
          dianwei: this.dianwei,
          device_sn: this.device_sn,
          room_num: this.room_num,
          start_time: this.start_time,
          end_time: this.end_time,
          order_sn: this.order_sn,
          order_status: this.order_status,
          page,
        };
        let res = await this.$u.api.getOrderList(data);
        if (page === 1) {
          this.orderTotal = res.total;
        }
        done(res.data);
        if (this.pagesData.length > this.pageHeight.length) {
          this.$nextTick(() => {
            this.computedHeight()
          });
        }
      } catch (error) {
        // 处理异常
        console.error('获取订单列表失败：', error);
        // 如果需要处理失败情况，可以在这里添加相应的逻辑
      }
    },
    //点击搜索框右边的icon图标
    onClickIcon(e) {
      if (e == 0) {
        this.isShowPopup = !this.isShowPopup
      }
    },
    //筛选确认查询
    confirmQuery(data) {
      this.dianwei = data.dianwei
      this.device_sn = data.device_sn
      this.start_time = data.start_time
      this.end_time = data.end_time
      this.order_sn = data.order_sn
      this.isShowPopup = false
      this.refresh()
    },
    /* 订单维护 */
    card(i) {
      let item = this.listData[i]
      // uni.navigateTo({ url: `/pagesB/order/OrderMaintain?from=''&order_sn=${this.listData[i].order_sn}` });
    },
  },
  onLoad() {
    this.customStyle = {
      top: 110 + this.vStatusBarHeight + this.vNavBarHeight + 'rpx',
    }
    this.end_time = this.start_time = subtractDaysAndFormat(0)

    this.handleData('', this.start_time, this.end_time)
    this.systemHeight = uni.getSystemInfoSync().windowHeight; // 获取屏幕高度
  },
  onShow(e) {
    /*  #ifndef H5 */
    let pages = getCurrentPages()
    let currPage = pages[pages.length - 1] // 当前页
    if (currPage.data.isDoRefresh == true) {
      // 是否刷新
      currPage.data.isDoRefresh = false
      this.refresh()
    }
    /*#endif */
    /*  #ifdef H5 */
    if (this.vCurrPage.isDoRefresh == true) {
      // 是否刷新
      this.vCurrPage.isDoRefresh = false
      this.refresh()
    }
    /*#endif */
  },
  onPageScroll(event) {
    if (!event.scrollTop || this.scrollControlDisabled) {
      return;
    }
    let pageScrollTop = event.scrollTop;
    this.scrollTop = event.scrollTop;

    // 检查是否需要加载更多页面并更新页面高度
    if (this.pagesData.length > this.pageHeight.length) {
      this.currentIndex = this.pagesData.length - 1;
      this.$nextTick(() => {
        this.computedHeight()
      });
    } else {
      let scrollTop = 0;
      for (let i = 0; i < this.pageHeight.length; i++) {
        scrollTop += this.pageHeight[i];
        if (scrollTop > pageScrollTop + this.systemHeight) {
          this.currentIndex = i;
          break;
        }
      }
    }
  },
  mixins: [myPull()],
}
</script>
<style lang="scss">
page {
  background-color: $pageBgColor;
}
</style>
<style lang="scss" scoped>
.border {
  border: 2rpx solid transparent;
  // border: 2rpx solid red;

  box-sizing: border-box;
  /* 包含边框和内边距在元素的总宽高内 */
}

.top {
  position: relative;
  z-index: 999;
}

.subsection {
  background-color: #fff;
  padding: 20rpx;
}

.modalTop {
  // height: 200rpx;
  font-size: 28rpx;
}

.title {
  margin: 15rpx 0;
}

.red {
  text-align: center;
  color: orangered;
  font-size: 20rpx;
}

.flexBottom {
  padding: 3rpx 0;
  width: 100%;
  display: flex;
  justify-content: space-between;
  // border: 1px solid #000;
  padding-bottom: 10rpx;

  ::v-deep .u-radio {
    margin-right: 80rpx;
  }
}
</style>
