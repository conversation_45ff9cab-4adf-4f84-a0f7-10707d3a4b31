<template>
  <view class="card">
    <view class="card_content flexRowBetween" :class="show ? 'border-bottom' : ''">
      <image class="type_icon" :src="icon" />
      <view class="name_time flexColumnBetween">
        <view class="name">{{ info.realname }} {{ accountNum }}</view>
        <view class="time">{{ time }}</view>
      </view>
      <view class="money-box flexColumnBetween">
        <view class="money">{{ info.money }}元</view>
        <view class="status flexRowAllCenter" @click="show = !show">
          <view>{{ statusName }}</view>
          <view class="arrow" :class="show ? 'rotate' : ''">
            <BaseIcon name="arrow-right" color="#0EADE2" size="28" />
          </view>
        </view>
      </view>
    </view>
    <view v-show="show" class="card_info">
      <view class="check-time" v-if="info.check_time && info.status != 0">
        更新时间：
        {{ $u.timeFormat(info.check_time * 1000, "yy-mm-dd hh:MM:ss") }}
      </view>
      <view class="estimated">
        <view>{{ statusContent }}</view>
      </view>
      <!-- <view>已于2021-10-21 10:00:56提现至支付宝账户</view>
      <view>驳回原因：什么什么原因导致提现不成功</view> -->
    </view>
  </view>
</template>

<script>
import BaseIcon from "@/components/base/BaseIcon.vue";
import { timestampToTime } from "@/common/tools/utils.js";
export default {
  components: { BaseIcon },
  name: "WithDrawalRecordCard",
  props: { info: { type: Object, default: {} } },
  computed: {
    time() {
      return timestampToTime(this.info.create_time * 1000);
    },
    accountNum() {
      return this.info.way == 1
        ? this.info.alipay_num
        : this.info.bank_card || this.info.realname;
    },
    icon() {
      return require(this.info.way == 1
        ? "@/pagesB/static/img/icon/zfb_icon.png"
        : this.info.way == 2 ? "@/pagesB/static/img/icon/wx_icon.png" : this.info.way == 4 ? "@/pagesB/static/img/icon/hzf.png" : "@/pagesB/static/img/icon/hzf.png");
    },
    statusName() {
      let status = this.info.status;
      if (status == 0) {
        this.statusContent = "审核中,请耐心等待~";
        return "审核中";
      } else if (status == 1) {
        this.statusContent = this.info.check_remarks + "~";
        return "审核通过";
      } else if (status == 2) {
        this.statusContent = this.info.pay_remarks + "~";
        return "提现成功";
      } else if (status == 3) {
        this.statusContent = this.info.pay_remarks + "~";
        return "提现失败";
      } else if (status == 5) {
        this.statusContent = "提现已撤回~";
        return "提现撤回";
      } else if (status == 10) {
        this.statusContent = "提现已退款~";
        return "提现已退款";
      }else if (status == -1) {
        this.statusContent = "已驳回~";
        return "已驳回";
      } else {
        this.statusContent = "审核中,请耐心等待~";
        return "审核中";
      }
    },
  },
  data() {
    return {
      show: false,
      statusContent: "审核中,请耐心等待~",
    };
  },
};
</script>

<style lang="scss" scoped>
.card {
  .border-bottom {
    border-bottom: 2rpx solid $dividerColor;
  }

  &_content {
    padding: 20rpx;

    .type_icon {
      width: 80rpx;
      height: 80rpx;
    }

    .name_time {
      flex: 1;
      margin-left: 20rpx;
    }

    >view {
      height: 80rpx;

      .name {
        color: $textBlack;
        font-size: $font-size-middle;
        font-weight: bold;
      }

      .time {
        color: $textDarkGray;
        font-size: $font-size-small;
      }

      .money {
        color: #ef0000;
        font-size: $font-size-middle;
      }

      .status {
        color: $themeComColor;
        font-size: $font-size-base;
      }
    }

    .arrow {
      transition: all 0.4s;
      margin-left: 6rpx;
    }

    .rotate {
      transform: rotate(90deg);
    }

    .money-box {
      align-items: flex-end;
    }
  }

  .card_info {
    padding: 30rpx 20rpx;
    color: $textBlack;
    font-size: $font-size-middle;
    text-align: center;

    .estimated {
      display: flex;
      justify-content: center;

      .cancel {
        margin-left: 200rpx;
      }
    }

    .check-time {
      margin-bottom: 20rpx;
    }
  }
}</style>