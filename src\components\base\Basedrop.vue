<template>
    <view class="select">

        <view class="vpointName">

            <u-input v-model="options[tabValue].label" :disabled="true">
            </u-input>
            <view @click="tabShow = true" class="click">
                <u-icon :name="!tabShow ? 'arrow-down' : 'arrow-up'" color="#333f" size="25"></u-icon>
            </view>
        </view>

        <view class="selectShow" v-if="tabShow">
            <view v-for="(item, i) in options" :key="i" @click="changeBtn(i)">{{ item.label }}</view>
        </view>
    </view>
</template>
  
<script>
export default {
    name: "BaseCountTo",
    props: {
        value: {
            type: [Number,String], //数值
            default: 0,
        },
        bold: { type: Boolean, default: true }, //加粗
        color: { type: String, default: "#fff" }, //颜色
        size: { type: [Number,String], default: "30" }, //字体
        decimals: { type: [Number,String], default: 0 }, //显示小数
    },
    data() {
        return {
            startVal: 0, //开始值。默认0
        };
    },
};
</script>
  
<style lang="scss" scoped></style>