<template>
  <view>
    <BaseNavbar :title="title" />
    <view class="content">

      <view>
        <view class="title">选择续费周期(年费金额：{{ '800' }}/年)</view>
        <BaseInput placeholder="请输入续费周期" rightText="年" v-model="goodsName" />
      </view>
     
      <view>
        <view class="title">选择添加方式</view>
        <BaseInput :selectValue="roleValue.label" :index="roleValue.value" placeholder="请选择角色" type="select"
          :list="roleList" @confirm="roleConfirm" />
      </view>
      <view>
        <view class="title">选择绑定{{ roleList[index].label }}</view>
        <view class="vpointName" >
          <view class="vpointName_zhe" @click="selectplace(index)">

          </view>
          <BaseInput :disabled="true" v-model="hotelName" :placeholder='`请选择绑定${roleList[index].label}`' />
          <view class="click" >
            <u-icon name="arrow-right" color="#2979ff" size="38"></u-icon>
          </view>
        </view>

      </view>
      <view class="btn">
        <BaseButton type="primary" @onClick="confirm">确 认</BaseButton>
      </view>
    </view>
  </view>
</template>

<script>
import BaseButton from "@/components/base/BaseButton.vue";
import BaseInput from "@/components/base/BaseInput.vue";
import BaseNavbar from "@/components/base/BaseNavbar.vue";
import BaseUpload from "@/components/base/BaseUpload.vue";
export default {
  components: { BaseNavbar, BaseInput, BaseUpload, BaseButton },
  data() {
    return {
      title: "新增续费",
      uploadList: [],
      goodsName: "", //商品名称
      market_price: "", //市场价
      storage_type: "", //商品类型
      cost_price: "", //进货价
      roleList: [{
        label: '商户id',
        value: 0
      }, {
        label: '场地方id',
        value: 1
      }, {
        label: '设备编号',
        value: 2
      }],
      roleValue: {
        label: '商户id',
        value: 0
      },
      index:0
    };
  },
  methods: {
    /* 跳转传参数 */
    selectplace(i){
      let str = 'vphone'
      if (i==0) {
        str = 'vphone'
      }else if(i==1){
        str = 'vphone'
      }else if(i==2){
        str = 'devi'
      }
      uni.navigateTo({
        url: `/pagesD/Renewal/RenwalSelect?from=${str}`,
      });
    },
    roleConfirm(e) {
      this.roleValue = this.roleList[e[0].value];
      this.index=e[0].value
      // console.log('index变化',this.index)
    },
    onUpload(item) {
      this.uploadList = item;
      // console.log("🚀 ~ uploadList", this.uploadList);
    },
    async confirm() {
      if (this.goodsName == "")
        return uni.showToast({
          title: "请填写商品名称",
          icon: "none",
          mask: true,
        });
      if (this.cost_price == "")
        return uni.showToast({
          title: "请填写进货价",
          icon: "none",
          mask: true,
        });
      if (this.market_price == "")
        return uni.showToast({
          title: "请填写市场价",
          icon: "none",
          mask: true,
        });
      // console.log("🚀 ~  this.uploadList", this.uploadList);
      let original_img = this.uploadList.length > 0 ? this.uploadList[0] : "";

      let data = {
        goods_name: this.goodsName,
        shop_price: this.market_price,
        market_price: this.market_price,
        cost_price: this.cost_price,
        goods_content: "",
        goods_remark: "",
        storage_type: this.storage_type,
        original_img,
      };
      await this.$u.api.addProducts(data)
      this.isShowSuccess("添加成功", 1, () => { }, true);
    },
  },
};
</script>

<style lang="scss" scoped>
.content {
  padding: 30rpx;

  >view {
    margin-bottom: 50rpx;
  }

  .title {
    color: $textBlack;
    font-size: $font-size-middle;
    font-weight: bold;
    margin-bottom: 20rpx;
  }

  .vpointName {
    position: relative;
    .vpointName_zhe{
      width: 100%;
      height: 100%;
      // border: 1px solid #000;
      position: absolute;
      left: 0;
      top: 0;
      z-index: 999;
    }
    .click {
      font-size: 25rpx;
      color: $font-size-middle;
      width: 52rpx;
      text-align: center;
      display: flex;
      align-items: center;
      height: 80rpx;
      // border: 1px solid #000;
      position: absolute;
      right: 0;
      top: 0;
      z-index: 666;
    }
  }
}
</style>