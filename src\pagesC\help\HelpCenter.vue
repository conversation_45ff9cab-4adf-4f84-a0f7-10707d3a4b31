<template>
  <view>
    <BaseNavbar :title="title" />
    <view class="service">
      <view class="serverPhone flexRowAllCenter" @click="onClickServerPhone">
        客服热线
        </view>
      <button class="serverPhone flexRowAllCenter" open-type="contact">
        在线客服
      </button>
    </view>
    <view class="problem">
      <view class="main-title">常见问题</view>
      <BaseCollapse :list="helpList" />
    </view>
  </view>
</template>

<script>
import BaseNavbar from "@/components/base/BaseNavbar.vue";
import BaseCollapse from "@/components/base/BaseCollapse.vue";
export default {
  components: { BaseNavbar, BaseCollapse },
  data() {
    return {
      title: "帮助中心",
      helpList: [
        // {
        //   head: "1、常见问题标题？",
        //   body: "只要我们正确择取一个合适的参照物乃至稍降一格去看待他人，值得赏识的东西便会扑面而来",
        //   open: false,
        //   disabled: false,
        // },
        // {
        //   head: "2、常见问题标题？",
        //   body: "只要我们正确择取一个合适的参照物乃至稍降一格去看待他人，值得赏识的东西便会扑面而来",
        //   open: false,
        //   disabled: false,
        // },
      ],
    };
  },
  methods: {
    onClickServerPhone() {
      uni.makePhoneCall({
        phoneNumber: this.vTel,
        // 成功回调
        success: (res) => {},
        // 失败回调
        fail: (res) => {},
      });
    },
    questionList(){
      this.$u.api.questionList().then(res=>{
        this.helpList=res
      })
    }
  },
  onLoad(){
    this.questionList()
  }
};
</script>

<style lang="scss">
/*当button里面包含图文时，图文之间会有间距，消除间距的方式是首先对button采用flex竖向布局，然后将子元素的margin和padding设置为0*/
button {
  &:after {
    border: none;
  }

  .button[plain] {
    border: none;
  }

  &:first-child {
    margin-top: 0;
  }

  &:active {
    background-color: white;
  }

  background-color: white;
  border-radius: 0;
  padding: 0;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
</style>
<style lang="scss" scoped>
.service {
  display: flex;
  height: 100rpx;
  border-bottom: 2rpx solid $dividerColor;
  .serverPhone {
    flex: 50%;
    color: $textBlack;
    font-size: $font-size-middle;
  }
  button {
    flex: 50%;
    border: 0 solid #fff;
    outline: none;
    background-color: white;
    margin-left: auto;
    margin-right: 0 !important;
  }
}
.problem {
  padding: 30rpx 50rpx;
  .main-title {
    color: $textBlack;
    font-weight: bold;
    font-size: $font-size-xlarge;
    margin-bottom: 30rpx;
  }
}
</style>