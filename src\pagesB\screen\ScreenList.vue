<template>
  <view>
    <BaseNavbar :title="title" />

    <BaseSearch :showScan="true" type-img="place" placeholder="请输入屏幕编号" @search="search" @onClickIcon="onClickIcon"
      @onClickScan="onClickScan" :isShowSerch="false" />

    <BaseDropdown :options-list="optionsList" @change="changeDropdown" :num="screenTotal" />
    <ComList :loading-type="loadingType">
      <ScreenListCard v-for="item in listData" :key="item.id" :info="item"
        :bindDeviceSn="bindDeviceSn || item.device_sn" @confirmBind="confirmBind" />
    </ComList>
    <!-- <FixedAddIcon @onAdd="goAddScreen" /> -->
    <!-- 筛选 -->
    <BasePopup :show.sync="isShowPopup" mode="top" :customStyle="customStyle">
      <DeviceScreener ref="DeviceScreener" :isShowScreenSn="true" @confirm="confirmScreenner"></DeviceScreener>
    </BasePopup>
  </view>
</template>

<script>
import BaseNavbar from "@/components/base/BaseNavbar.vue";
import BaseSearch from "@/components/base/BaseSearch.vue";
import ComList from "@/components/list/ComList.vue";
import ScreenListCard from "../components/cards/ScreenListCard.vue";
import BaseDropdown from "@/components/base/BaseDropdown.vue";
import FixedAddIcon from "@/components/common/FixedAddIcon.vue";
import myPull from "@/mixins/myPull";
import BasePopup from "../../components/base/BasePopup.vue";
import DeviceScreener from "../components/screener/DeviceScreener.vue";
import { getUrlDynamicData } from "../../common/tools/utils";
export default {
  components: {
    BaseNavbar,
    BaseSearch,
    ComList,
    ScreenListCard,
    BaseDropdown,
    FixedAddIcon,
    BasePopup,
    DeviceScreener,
  },
  mixins: [myPull()],
  data() {
    return {
      title: "屏幕管理",
      optionsList: [
        {
          title: "全部",
          options: [
            { label: "全部", value: 0, status: 0 },
            {
              label: "在线",
              value: 1,
              status: 1,
            },
            {
              label: "离线",
              value: 2,
              status: 3,
            },
          ],
          value: 0,
        },
      ],
      bindDeviceSn: "", //选中需要绑定的device_sn
      isShowPopup: false,
      screenObj: {
        //筛选对象
        device_sn: "",
        screen_sn: "",
        room_num: "",
        status: 0,
        dianweiid: 0,
        owner: "",
      },
      screenTotal: 0,
      customStyle: {
        top: 110 + this.vStatusBarHeight + this.vNavBarHeight + 'rpx',
      }
    };
  },
  methods: {
    changeDropdown(item) {
      this.optionsList = item;
      let index = item[0].value;
      this.screenObj.status = item[0].options[index].status;

      this.refresh();
    },
    goAddScreen() {
      uni.navigateTo({ url: "/pagesB/screen/ScreenAdd?from=add" });
    },
    onClickIcon(e) {
      if (e == 0) {
        uni.navigateTo({ url: "/pagesB/mapMode/MapMode?from=screen" });
      } else if (e == 1) {
        this.isShowPopup = !this.isShowPopup;
      }
    },
    getList(page, done) {
      let data = {
        page,
        ...this.screenObj,
      };
      this.$u.api.getScreenList(data).then((res) => {
        done(res.data.data);
        this.screenTotal = res.data?.total || 0;
      });
    },
    confirmBind(screen) {
      let data = {
        device_sn: this.bindDeviceSn,
        screen,
      };
      this.$u.api.bindScreenMachine(data).then((res) => {
        this.isShowSuccess("绑定成功", 0, this.refresh());
      });
      this.bindDeviceSn = "";
    },
    confirmScreenner(e) {
      this.screenObj = {
        device_sn: e.device_sn,
        screen_sn: e.screen_sn,
        room_num: e.room_num,
        status: e.status,
        owner: e.owner,
        dianweiid: e.dianwei || 0,
      };
      this.isShowPopup = false;
      this.refresh();
    },
    search(e) {
      this.screenObj.screen_sn = e;
      this.refresh();
    },
    //点击扫码icon
    onClickScan(result) {
      if (!result) return this.isShowErr("请扫描正确二维码~");
      if (result.includes("device_sn")) {
        this.device_sn = getUrlDynamicData(result, "device_sn");

        this.refresh();
        this.device_sn = "";
      } else {
        this.isShowErr("请扫描正确二维码~");
      }
    },
  },
  onLoad() {
    this.customStyle = {
      top: 110 + this.vStatusBarHeight + this.vNavBarHeight + 'rpx',
    }
    this.refresh();
  },
  onShow() {
    /*  #ifndef H5 */
    let pages = getCurrentPages();
    let currPage = pages[pages.length - 1]; // 当前页
    if (currPage.data.item) {
      this.bindDeviceSn = currPage.data.item.device_sn; //接收选中的设备信息
      if (this.isShowPopup) {
        let checkHotel = currPage.data.item;
        this.$refs.DeviceScreener.hotelName = checkHotel.hotelName;
        this.$refs.DeviceScreener.hotel_id = checkHotel.id;
      }
    }
    if (currPage.data.isDoRefresh == true) {
      // 是否刷新
      currPage.data.isDoRefresh = false;
      this.refresh();
    }
    /*#endif */
    /*  #ifdef H5 */
    if (this.vCurrPage.item) {
      this.bindDeviceSn = this.vCurrPage.item.device_sn; //接收选中的设备信息
      if (this.isShowPopup) {
        let checkHotel = this.vCurrPage.item;
        this.$refs.DeviceScreener.hotelName = checkHotel.hotelName;
        this.$refs.DeviceScreener.hotel_id = checkHotel.id;
      }
    }
    if (this.vCurrPage.isDoRefresh == true) {
      // 是否刷新
      this.vCurrPage.isDoRefresh = false;
      this.refresh();
    }
    /*#endif */
  },
};
</script>
<style lang="scss">
page {
  background-color: $pageBgColor;
}
</style>
<style lang="scss" scoped></style>