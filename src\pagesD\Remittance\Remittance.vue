<template>
  <view class="remitatance">
    <BaseNavbar :title="title" />
    <view class="tab_box">
      <view class="tab_item" :class="i == tabIndex ? 'tab-active' : ''" v-for="(item, i) in tabList" :key="i"
        @click="tab(i)">
        {{ item.name }}
      </view>
    </view>
    <view class="content">
      <view class="place-item" v-if="real_info_type == 0">
        <view class="place-title">
          个人信息
        </view>
        <view>
          <view class="title">
            <view>用户姓名</view>
            <view class="necessary">*</view>
          </view>
          <BaseInput placeholder="请输入您的姓名" :disabled="edit" v-model="user_name" :def="false" />
        </view>
        <view>
          <view class="title">
            <view>手机号</view>
            <view class="necessary">*</view>
          </view>
          <BaseInput :disabled="edit" placeholder="请输入您的手机号" v-model="tel_no" :def="false" />
        </view>
        <view>
          <view class="title">
            <view>身份证号</view>
            <view class="necessary">*</view>
          </view>
          <BaseInput :disabled="edit" placeholder="请输入您的身份证号" v-model="cert_id" :def="false" />
        </view>
        <view>
          <view class="title">
            <view>身份证起始日期</view>
            <view class="necessary">*</view>
          </view>
          <TimeSelect times v-model="cert_begin_date" placeholder="请选择起始日期" :defaultTime="cert_begin_date" />

          <!-- <view class="necessary">*</view> -->
        </view>
        <view>
          <view class="title">
            <view>身份证是否长期有效</view>
          </view>
          <view>
            <BaseRadio @changes="change" :radioIndex.sync="radioIndex" :list="reasonList" />
          </view>

          <!-- <view class="necessary">*</view> -->
        </view>
        <view v-if="cert_validity_type == 0">
          <view class="title">
            <view>身份证终止日期</view>
            <view class="necessary">*</view>
          </view>
          <TimeSelect v-model="cert_end_date" times placeholder="请选择结束时间" :defaultTime="cert_end_date" />

          <!-- <view class="necessary">*</view> -->
        </view>
        <view>
          <view class="title">
            <view>用户邮箱</view>
          </view>
          <BaseInput :disabled="edit" placeholder="请输入您的邮箱号" v-model="email" :def="false" />
        </view>
        <view>
          <view class="title">
            <view>用户地址</view>
          </view>
          <view class="map-box">
            <BaseInput placeholder="请输入详细位置" :disable="edit" v-model="choosedAddressInfo.address" :def="false"
              width="70" />
            <view class="map-box-title" @click.stop="getSelectLocation">
              <BaseIcon name="map" color="#206cc5" />
              <view class="map-box-title-txt">地图搜索</view>
            </view>
          </view>
        </view>
      </view>
      <view class="place-item" v-if="real_info_type == 1">
        <view class="place-title">
          企业信息
        </view>
        <view>
          <view class="title">
            <view>企业名称</view>
            <view class="necessary">*</view>
          </view>
          <BaseInput placeholder="请输入企业名称" :disabled="edit" v-model="reg_name" :def="false" />
        </view>
        <view>
          <view class="title">
            <view>营业执照编号</view>
            <view class="necessary">*</view>
          </view>
          <BaseInput :disabled="edit" placeholder="请输入营业执照编号" v-model="license_code" :def="false" />
        </view>
        <view>
          <view class="title">
            <view>证件照起始日期</view>
            <view class="necessary">*</view>
          </view>
          <TimeSelect times v-model="license_begin_date" placeholder="请选择证件照起始日期" :defaultTime="license_begin_date" />

          <!-- <view class="necessary">*</view> -->
        </view>
        <view>
          <view class="title">
            <view>证件照是否长期有效</view>
          </view>
          <view>
            <BaseRadio @changes="contactChange" :radioIndex.sync="contactIndex" :list="reasonList" />
          </view>

          <!-- <view class="necessary">*</view> -->
        </view>
        <view v-if="license_validity_type == 0">
          <view class="title">
            <view>证件照终止日期</view>
            <view class="necessary">*</view>
          </view>
          <TimeSelect v-model="license_end_date" times placeholder="请选择证件照终止日期" :defaultTime="license_end_date" />
        </view>
        <view>
          <view class="title">
            <view>注册地址所在省市区</view>
            <view class="necessary">*</view>
          </view>
          <AreaSelect @changs="contactChanges" :defaultRegion="contact_defaultRegion" v-model="contact_selectArea"
            placeholder="请选择注册地址所在省市区" />
        </view>
        <view>
          <view class="title">
            <view>详细地址</view>
            <view class="necessary">*</view>
          </view>
          <view class="map-box">
            <BaseInput placeholder="请输入详细注册地址" :disable="edit" v-model="choosedAddressInfoTwo.name" :def="false"
              width="70" />
            <view class="map-box-title" @click.stop="getSelectLocationTwo">
              <BaseIcon name="map" color="#206cc5" />
              <view class="map-box-title-txt">地图搜索</view>
            </view>
          </view>
        </view>
      </view>
      <view class="place-item" v-if="real_info_type == 1">
        <view class="place-title">
          法人信息
        </view>
        <view>
          <view class="title">
            <view>法人姓名</view>
            <view class="necessary">*</view>
          </view>
          <BaseInput placeholder="请输入法人的姓名" :disabled="edit" v-model="legal_name" :def="false" />
        </view>
        <view>
          <view class="title">
            <view>法人身份证号</view>
            <view class="necessary">*</view>
          </view>
          <BaseInput :disabled="edit" placeholder="请输入法人的身份证号" v-model="legal_cert_no" :def="false" />
        </view>
        <view>
          <view class="title">
            <view>法人证件照起始日期</view>
            <view class="necessary">*</view>
          </view>
          <TimeSelect times v-model="legal_cert_begin_date" placeholder="请选择起始日期"
            :defaultTime="legal_cert_begin_date" />

          <!-- <view class="necessary">*</view> -->
        </view>
        <view>
          <view class="title">
            <view>法人证件照是否长期有效</view>
          </view>
          <view>
            <BaseRadio @changes="legalChange" :radioIndex.sync="legalIndex" :list="reasonList" />
          </view>

          <!-- <view class="necessary">*</view> -->
        </view>
        <view v-if="legal_cert_validity_type == 0">
          <view class="title">
            <view>法人证件照终止日期</view>
            <view class="necessary">*</view>
          </view>
          <TimeSelect v-model="legal_cert_end_date" times placeholder="请选择结束时间" :defaultTime="legal_cert_end_date" />
        </view>
      </view>
      <view class="place-item" v-if="real_info_type == 1">
        <view class="place-title">
          联系人信息
        </view>
        <view>
          <view class="title">
            <view>联系人姓名</view>
            <view class="necessary">*</view>
          </view>
          <BaseInput placeholder="请输入联系人姓名" :disabled="edit" v-model="contact_name" :def="false" />
        </view>
        <view>
          <view class="title">
            <view>联系人的手机号号</view>
            <view class="necessary">*</view>
          </view>
          <BaseInput :disabled="edit" placeholder="请输入联系人的手机号" v-model="contact_mobile" :def="false" />
        </view>
      </view>
      <view class="place-item">
        <view class="place-title">
          {{real_info_type == 1?'对公':''}}银行卡信息
        </view>
        <view>
          <view class="title">
            <view>银行卡号</view>
            <view class="necessary">*</view>
          </view>
          <BaseInput placeholder="请输入银行卡号" :disabled="edit" v-model="card_id" @onInput="addSpaces()" :def="false" />
        </view>

        <view>
          <view class="title">
            <view>开户行所在省市区</view>
            <view class="necessary">*</view>
          </view>
          <AreaSelect @changs="changes" :defaultRegion="defaultRegion" v-model="selectArea" placeholder="请选择省市区" />
        </view>
        <view v-if="real_info_type == 1">
          <view class="title">
            <view>开户行名称</view>
            <view class="necessary">*</view>
          </view>
          <view class="vpointName">
            <view class="vpointName_zhe">
            </view>
            <BaseInput placeholder="请选择开户行名称" v-model="comp_branch_name" />
            <view class="click" @click.stop="selectBranch">
              <u-icon name="arrow-right" color="#999" size="45"></u-icon>
            </view>
          </view>
        </view>
        <view v-show="real_info_type == 1">
          <view class="title">
            <view>银行卡所在银行总行编码</view>
            <view class="necessary">*</view>
          </view>
          <view class="vpointName">
            <view class="vpointName_zhe">
            </view>
            <BaseInput placeholder="请选择银行卡所在银行总行编码" type="number" v-model="comp_bank_code" maxlength="8" />
            <view class="click" @click="selectMain">
              <u-icon name="arrow-right" color="#999" size="45"></u-icon>
            </view>
          </view>
        </view>
       

        <!-- <view v-show="real_info_type == 1">
          <view class="title">
            <view class="title">银行卡所在银行总行编码</view>
            <view class="necessary">*</view>
          </view>
          <BaseInput :disabled="edit" placeholder="请输入您的银行编号" v-model="comp_bank_code" :def="false" />
        </view> -->
        <!-- <view v-show="real_info_type == 1">
          <view class="title">
            <view class="title">开户行名称</view>
            <view class="necessary">*</view>
          </view>
          <BaseInput :disabled="edit" placeholder="请输入您的开户行名称" v-model="comp_branch_name" :def="false" />
        </view> -->
        <!-- <view v-show="real_info_type == 1">
          <view class="title">
            <view class="title">开户行编码</view>
          </view>
          <BaseInput :disabled="edit" placeholder="请输入您的开户行编码" v-model="comp_branch_code" :def="false" />
        </view> -->
      </view>

      <!-- <view>
                <view class="title">
                    <view class="title">用户昵称</view>
                </view>
                <BaseInput :disabled="edit" placeholder="请输入您的昵称" v-model="nickname" />
            </view> -->
      <!-- <view>
                <view class="title">
                    <view class="title">性别</view>
                </view>
                <BaseInput :disabled="edit" placeholder="请输入您的性别" v-model="gender" />
            </view> -->

      <view class="btn" v-if="!edit">
        <view>
          <BaseButton type="primary" @onClick="confirmAdd">确 认</BaseButton>
        </view>
      </view>
    </view>
    <BaseModal :show.sync="isModal" :isShowCancel="false" title="温馨提示">
      <view slot="default" class="noun">
        <view>1.个人认证和企业认证只能任选其一</view>
        <view>2.如果已经提交认证则不能再做其他认证</view>
        <view>3.如果需要修改认证类型请联系相关售后人员</view>
      </view>
    </BaseModal>
  </view>
</template>

<script>
import BaseButton from '@/components/base/BaseButton.vue'
import BaseInput from '@/components/base/BaseInput.vue'
import BaseNavbar from '@/components/base/BaseNavbar.vue'
import BaseUpload from '@/components/base/BaseUpload.vue'
import AreaSelect from '@/components/common/AreaSelect.vue'
import TimeSelectHour from '@/components/common/TimeSelectHour.vue'
import BasePopup from '@/components/base/BasePopup.vue'
import TypeSelectPopup from '@/components/common/TypeSelectPopup.vue'
import { locationMixin } from '@/mixins/locationMixin'
import BaseRadio from '../../components/base/BaseRadio.vue'
import TimeSelect from '@/components/common/TimeSelect.vue'
import BaseIcon from '../../components/base/BaseIcon.vue'
import BaseModal from '@/components/base/BaseModal.vue'

export default {
  components: {
    BaseNavbar,
    BaseInput,
    BaseUpload,
    BaseButton,
    AreaSelect,
    TimeSelectHour,
    BasePopup,
    TypeSelectPopup,
    BaseIcon,
    TimeSelect,
    BaseRadio,
    BaseModal,
  },
  mixins: [locationMixin],
  data() {
    return {
      title: '实名认证',
      user_name: '', //用户名称
      tel_no: '', //用户电话
      email: '', //用户邮箱
      location: '', //用户地址
      // gender: "",//用户性别
      // nickname: "",//用户昵称
      cert_id: '', //用户身份证号
      card_id: '', //银行卡号
      cert_begin_date: '', //身份证起始日期
      cert_end_date: '', //身份证结束日期
      cert_validity_type: 0,
      real_info_type: 0, //个人认证/企业认证 0/1
      reg_name: '', //企业名称
      license_code: '', //营业执照编号
      license_validity_type: 0, //营业执照证件有效类型
      license_begin_date: '', //营业执照有效期起始日期
      license_end_date: '', //营业执照有效期结束日期
      reg_prov_id: '', //注册地址省编码
      reg_area_id: '', //注册地址市编码
      reg_district_id: '', //注册地址区编码
      reg_detail: '', //注册详细地址
      legal_name: '', //法人姓名
      legal_cert_type: '00', //法人证件类型
      legal_cert_no: '', // 法人证件号码
      legal_cert_validity_type: 0, //法人证件有效类型
      legal_cert_begin_date: '', //法人证件有效期开始日期
      legal_cert_end_date: '', //法人证件有效期结束日期
      contact_name: '', //联系人姓名
      contact_mobile: '', //联系人手机号
      contact_defaultRegion: [], //注册默认地址
      comp_bank_code: '', //银行编号
      comp_branch_name: '', //支行名称
      comp_branch_code: '', //支行编号
      contact_selectArea: '',
      contactIndex: 0,
      legalIndex: 0,
      reasonList: [
        {
          title: '非长期有效',
          name: '0',
          disabled: false,
          selectIndex: 0,
        },
        {
          title: '长期有效',
          name: '1',
          disabled: false,
          selectIndex: 1,
        },
      ],
      radioIndex: 0,
      selectArea: '',
      defaultRegion: [], //默认区域
      prov_id: '',
      area_id: '',
      edit: false,
      tabList: [
        { name: '个人认证', status: 0 },
        { name: '企业认证', status: 1 },
      ],
      tabIndex: 0,
      Add: false,
      isModal: false, //BaseModal弹窗
      isMain: false,

    }
  },
  computed: {
    // correctImg() {
    //     console.log('图片地址', this.correctDocuments)
    //     return this.correctDocuments
    // },
  },
  methods: {
    // onUpload(item) {
    //     this.uploadList = item;
    // },
    // onUploadLicense(item) {
    //     this.defaultFileListLicense = [];
    //     this.fileListLicense = item;
    //
    // },
    //选择总行
    selectMain() {
      this.isMain = false
      // console.log('bank_code', this.comp_bank_code)
      uni.navigateTo({
        url: `/pagesB/place/SelectPlace?from=remittance`,
      });
    },
    selectBranch() {
      this.isMain = true
      // console.log('bank_code', this.comp_bank_code)
      uni.navigateTo({
        url: `/pagesB/place/SelectPlace?from=branch&bank_code=${this.comp_bank_code}&type=2`,
      });
    },
    addSpaces() {

      let value = this.card_id.replace(/\s/g, ''); // Remove existing spaces
      let formattedValue = '';

      for (let i = 0; i < value.length; i++) {
        formattedValue += value[i];
        if ((i + 1) % 4 === 0 && i !== value.length - 1) {
          formattedValue += ' ';
        }
      }

      this.card_id = formattedValue;
    },
    padWithZeroes(inputString) {
      while (inputString.length < 6) {
        inputString = inputString + '0'
      }
      return inputString
    },

    changes(e) {
      this.prov_id = this.padWithZeroes(e.province.value)
      // console.log('是否包含', e.city.label.includes('直辖县级行政区'))
      if (e.city.label.includes('直辖县级行政区')) {
        this.area_id = this.padWithZeroes(e.area.value)
      } else {

        this.area_id = this.padWithZeroes(e.city.value)
      }
    },
    contactChanges(e) {
      this.reg_prov_id = this.padWithZeroes(e.province.value)
      if (e.city.label.includes('直辖县级行政区')) {
        this.reg_area_id = this.padWithZeroes(e.area.value)
      } else {

        this.reg_area_id = this.padWithZeroes(e.city.value)
      }

      this.reg_district_id = this.padWithZeroes(e.area.value)
    },

    change(e) {

      this.cert_validity_type = e
    },
    contactChange(e) {

      this.license_validity_type = e
    },
    legalChange(e) {

      this.legal_cert_validity_type = e
    },
    text(a) {
      let b = a.split('-')
      let str = ''
      for (let i = 0; i < b.length; i++) {
        str = str + b[i]
      }
      return str
    },
    formatDate(inputDate) {
      // 检查输入日期是否有效
      if (inputDate) {
        if (inputDate.length !== 8) {
          return 'Invalid date format'
        }

        // 将输入日期按照指定格式进行转换
        const formattedDate = `${inputDate.slice(0, 4)}-${inputDate.slice(
          4,
          6,
        )}-${inputDate.slice(6)}`

        return formattedDate
      } else {
        return ''
      }
    },
    async confirmAdd() {
      let data = {}
      if (!this.card_id) return this.isShowErr('请选输入银行卡号~')
      if (!this.selectArea) return this.isShowErr('请选输入银行卡所在省区~')
      if (this.real_info_type == 0) {
        if (!this.user_name) return this.isShowErr(`请选输入姓名~`)
        if (!this.tel_no) return this.isShowErr('请选输入手机号~')
        if (!this.cert_id) return this.isShowErr('请选输入身份证号~')
        if (!this.cert_begin_date)
          return this.isShowErr('请选输入身份证起始日期~')
        if (this.cert_validity_type == 0) {
          if (!this.cert_end_date)
            return this.isShowErr('请选输入身份证结束日期~')
          if (this.cert_end_date <= this.cert_begin_date)
            return this.isShowErr('身份证结束日期需大于身份证开始日期~')
        }

        let cert_date = this.text(this.cert_begin_date)
        let cert_end = this.text(this.cert_end_date)
        data = {
          location: this.location, //具体地址
          user_name: this.user_name, //姓名
          tel_no: this.tel_no, //电话
          email: this.email, //邮箱
          // gender: this.gender,//性别
          // nickname: this.nickname,//用户昵称
          cert_id: this.cert_id,
          card_id: this.card_id,
          bank_acct_type: 2, //银行卡对私
          cert_begin_date: cert_date,
          cert_validity_type: this.cert_validity_type,
          prov_id: this.prov_id,
          area_id: this.area_id,
          prov_city_area: this.selectArea,
          real_info_type: 0, //个人认证
        }
        if (this.cert_validity_type == 0) {
          data['cert_end_date'] = cert_end
        }
      } else if (this.real_info_type == 1) {
        this.reg_detail = this.choosedAddressInfoTwo.name
        if (!this.reg_name) return this.isShowErr(`请选输入企业名称~`)
        if (!this.license_code) return this.isShowErr(`请选输入营业执照编号~`)
        if (!this.license_begin_date)
          return this.isShowErr(`请选输入营业执照有效期起始日期~`)
        if (this.license_validity_type == 0) {
          if (!this.license_end_date)
            return this.isShowErr(`请选输入营业执照有效期结束日期~`)
          if (this.license_end_date <= this.license_begin_date)
            return this.isShowErr('营业执照结束日期需大于营业执照开始日期~')
        }
        if (!this.contact_selectArea)
          return this.isShowErr(`请选输入注册地址省市区编码~`)
        if (!this.reg_detail) return this.isShowErr(`请选输入注册详细地址~`)
        if (!this.legal_name) return this.isShowErr(`请选输入法人姓名~`)
        if (!this.legal_cert_no) return this.isShowErr(`请选输入法人证件号码~`)
        if (!this.legal_cert_begin_date)
          return this.isShowErr(`请选输入法人证件有效期开始日期~`)
        if (this.legal_cert_validity_type == 0) {
          if (!this.legal_cert_end_date)
            return this.isShowErr(`请选输入法人证件有效期结束日期~`)
          if (this.legal_cert_end_date <= this.legal_cert_begin_date)
            return this.isShowErr('法人身份证结束日期需大于法人身份证开始日期~')
        }
        if (!this.contact_name) return this.isShowErr(`请选输入联系人姓名~`)
        if (!this.contact_mobile) return this.isShowErr(`请选输入联系人手机号~`)
        if (!this.comp_bank_code) return this.isShowErr(`请选总行编码~`)
        if (!this.comp_branch_name) return this.isShowErr(`请输入开户行名称~`)

        let license_begin_date = this.text(this.license_begin_date)
        let license_end_date = this.text(this.license_end_date)
        let legal_cert_begin_date = this.text(this.legal_cert_begin_date)
        let legal_cert_end_date = this.text(this.legal_cert_end_date)
        data = {
          real_info_type: 1, //企业认证
          comp_reg_name: this.reg_name, //企业名称
          comp_license_code: this.license_code, //营业执照编号
          comp_license_validity_type: this.license_validity_type, //营业执照证件有效类型
          comp_license_begin_date: license_begin_date, //营业执照有效期起始日期
          comp_reg_prov_id: this.reg_prov_id, //注册地址省编码
          comp_reg_area_id: this.reg_area_id, //注册地址市编码
          comp_reg_district_id: this.reg_district_id, //注册地址区编码
          comp_reg_detail: this.reg_detail, //注册详细地址
          comp_legal_name: this.legal_name, //法人姓名
          comp_legal_cert_no: this.legal_cert_no, // 法人证件号码
          comp_legal_cert_validity_type: this.legal_cert_validity_type, //法人证件有效类型
          comp_legal_cert_begin_date: legal_cert_begin_date, //法人证件有效期开始日期
          comp_contact_name: this.contact_name, //联系人姓名
          comp_contact_mobile: this.contact_mobile, //联系人手机号
          card_id: this.card_id, //银行卡号
          comp_bank_code: this.comp_bank_code, //银行编号
          comp_branch_name: this.comp_branch_name, //支行名称
          comp_branch_code: this.comp_branch_code, //支行编号
          bank_acct_type: 1, //银行卡对公
          prov_id: this.prov_id, //银行所在省
          area_id: this.area_id, //银行所在区
          prov_city_area: this.selectArea, //银行所在省市区
          comp_prov_city_area: this.contact_selectArea, //注册地址所在省市区
        }
        if (this.license_validity_type == 0) {
          data['comp_license_end_date'] = license_end_date
        }
        if (this.legal_cert_validity_type == 0) {
          data['comp_legal_cert_end_date'] = legal_cert_end_date
        }
      }
      // let rtnData = null;
      try{
        await this.$u.api.setauthRealInfo(data)
        this.isShowSuccess(this.title + '成功', 1, () => { }, true)
      }catch(error){
        console.log(error)
        this.isShowErr(err.msg)
      }
     
      // rtnData.then((res) => {
      //     // this.isShowSuccess(this.title + "成功", 1, () => { }, true);
      //     if(res.msg=='认证并且绑卡成功'){
      //         this.isShowSuccess(this.title + "成功", 1, () => { }, true);
      //     }
      //     console.log('返回信息',res)
      // });
    },
    /* tab切换 */
    tab(i) {
      if (this.Add) {
        this.isModal = true
        return
      }
      this.tabIndex = i
      this.real_info_type = i
    },
    async getRealInfo() {
      try{
        let res=await this.$u.api.getRealInfo()
        let data = res
        this.location = data?.location || ''
        this.tel_no = data?.tel_no || ''
        this.email = data?.email || ''
        this.gender = data?.gender || ''
        this.cert_id = data?.cert_id || ''
        this.cert_validity_type = data?.cert_validity_type || 0
        this.user_name = data?.user_name || ''
        this.card_id = data?.card_id || ''
        // this.gender = data?.gender||''
        // this.nickname = data?.nickname||''
        this.prov_id = data?.prov_id || ''
        this.area_id = data?.area_id || ''
        this.real_info_type = data?.real_info_type || 0
        if (this.real_info_type == 1) {
          this.tabIndex = 1
        }
        this.cert_begin_date = this.formatDate(data?.cert_begin_date)
        //   this.real_info_type=1
        if (this.cert_validity_type == 1) {
          this.radioIndex = 1
        } else {
          this.radioIndex = 0
          this.cert_end_date = this.formatDate(data?.cert_end_date)
        }
        this.selectArea = data?.prov_city_area
        // console.log('🚀 ~  this.opt ', this.cert_begin_date)
        this.reg_name = data?.comp_name || '' //企业名称
        this.license_code = data?.comp_license_code || '' //营业执照编号
        this.license_validity_type = data?.comp_license_validity_type || 0 //营业执照证件有效类型
        this.license_begin_date = this.formatDate(data?.comp_license_begin_date)
        if (this.license_validity_type == 1) {
          this.contactIndex = 1
        } else {
          this.contactIndex = 0
          this.license_end_date = this.formatDate(data?.comp_license_end_date)
        }

        this.reg_prov_id = data?.comp_reg_prov_id || '' //注册地址省编码
        this.reg_area_id = data?.comp_reg_area_id || '' //注册地址市编码
        this.reg_district_id = data?.comp_reg_district_id || '' //注册地址区编码
        this.contact_selectArea = data?.comp_prov_city_area
        this.choosedAddressInfoTwo = {
          name: data?.comp_reg_detail,
        }
        // this.reg_detail = data?.comp_reg_detail || '' //注册详细地址
        this.legal_name = data?.user_name || '' //法人姓名
        this.legal_cert_no = data?.cert_id || '' // 法人证件号码
        this.legal_cert_validity_type = data?.cert_validity_type || 0 //法人证件有效类型
        this.legal_cert_begin_date = this.formatDate(data?.cert_begin_date)
        if (this.legal_cert_validity_type == 1) {
          this.legalIndex = 1
        } else {
          this.legalIndex = 0
          this.legal_cert_end_date = this.formatDate(data?.cert_end_date)
        }
        this.contact_name = data?.comp_contact_name || '' //联系人姓名
        this.contact_mobile = data?.comp_contact_mobile || '' //联系人手机号
        this.comp_bank_code = data?.comp_bank_code || '' //银行编号
        this.comp_branch_name = data?.comp_branch_name || '' //支行名称
        this.comp_branch_code = data?.comp_branch_code || '' //支行编号
        // console.log('已经编辑过了', this.user_name || this.reg_name)
        if (this.user_name || this.reg_name) {
          // console.log('已经编辑过了')
          this.isModal = false
          this.Add = true
        }
        this.addSpaces()
      }catch(error){
        console.log('error',error)
      }
   
    }
  },
  onLoad(opt) {
    if (opt.from == 'edit') {
      this.edit = true
      // console.log('编辑进来的')
    }
    this.getRealInfo()

  },
  onShow() {
    /*  #ifndef H5 */
    let pages = getCurrentPages();
    let currPage = pages[pages.length - 1]; // 当前页
    if (currPage.data.item) {
      // 有值
      // 修改listData中值
      let checkBranch = currPage.data.item;
      if (this.isMain) {
        this.comp_branch_name = checkBranch.bank_name && this.comp_branch_name != checkBranch.bank_name ? checkBranch.bank_name : this.comp_branch_name
        this.comp_bank_code = checkBranch.parent_bank_code && this.comp_bank_code != checkBranch.parent_bank_code ? checkBranch.parent_bank_code : this.comp_bank_code

      } else {
        if (checkBranch.bank_code && this.comp_bank_code != checkBranch.bank_code) {
          this.comp_bank_code = checkBranch.bank_code
          this.comp_branch_name = ''
        }
      }

      // console.log("🚀 ~ this.checkHotel", this.comp_bank_code);

      // this.linkmanDianwei = this.checkHotel.user_login;
      // this.dianwei = this.checkHotel.id

    }
    /*#endif */
    /*  #ifdef H5 */
    if (this.vCurrPage.item) {
      let checkBranch = this.vCurrPage.item
      if (this.isMain) {
        this.comp_branch_name = checkBranch.bank_name && this.comp_branch_name != checkBranch.bank_name ? checkBranch.bank_name : this.comp_branch_name
        this.comp_bank_code = checkBranch.parent_bank_code && this.comp_bank_code != checkBranch.parent_bank_code ? checkBranch.parent_bank_code : this.comp_bank_code

      } else {
        if (checkBranch.bank_code && this.comp_bank_code != checkBranch.bank_code) {
          this.comp_bank_code = checkBranch.bank_code
          this.comp_branch_name = ''
        }
      }
    }

    /*#endif */
  },
}
</script>
<style lang="scss">
page {
  background-color: $pageBgColor;
}
</style>
<style lang="scss" scoped>
/* .remitatance {
  margin: 0 30rpx;
} */

.vpointName {
  position: relative;

  .icon {
    width: 50rpx;
    // border: 2rpx solid red;
  }

  .vpointName_zhe {
    width: 100%;
    height: 100%;
    // border: 1px solid #000;
    position: absolute;
    left: 0;
    top: 0;
    z-index: 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
}

.click {
  font-size: 25rpx;
  color: $font-size-middle;
  width: 52rpx;
  text-align: center;
  display: flex;
  align-items: center;
  height: 80rpx;
  // border: 1px solid #000;
  position: absolute;
  right: 0;
  top: 0;
  z-index: 999;
}

.tab_box {
  display: flex;
  justify-content: space-between;
  background-color: #fff;
  padding: 10rpx 30rpx;
  margin-bottom: 20rpx;

  .tab_item {
    flex: 1;
    padding: 10rpx 20rpx;
    text-align: center;
    border: 2rpx solid #0eade2;
    color: #0eade2;
  }

  .tab-active {
    color: #fff;
    background-color: #0eade2;
  }

  .tab_item:first-child {
    border-top-left-radius: 10rpx;
    border-bottom-left-radius: 10rpx;
  }

  .tab_item:last-child {
    border-top-right-radius: 10rpx;
    border-bottom-right-radius: 10rpx;
  }
}

.noun {
  font-size: 25rpx;

  >view {
    margin: 20rpx 0;
  }
}

.content {
  padding: 0 30rpx 30rpx 30rpx;

  .image {
    height: 200rpx;
    width: 500rpx;
    border: 1px solid #000;

    image {
      width: 100%;
      height: 100%;
    }
  }
/* 
  >view {
    margin-bottom: 50rpx;
  } */

  .place-title {
    border-bottom: 2rpx solid #f0f0f0;
    // border-left: 2rpx solid #f0f0f0;
    // border-right: 2rpx solid #f0f0f0;
    // border-top-left-radius: 10rpx;
    // border-top-right-radius: 10rpx;
    // background-color: #fff;
    padding: 20rpx 0;
    font-size: 30rpx;
    // margin:20rpx 0;
    font-weight: bold;
  }

  .place-item {
    padding: 20rpx 15rpx 50rpx 15rpx;
    border-radius: 15rpx;
    border: 2rpx solid #f0f0f0;
    background-color: #fff;
    margin-bottom: 20rpx;
    // border:2rpx solid red;
  }

  .row-flex {
    display: flex;
    align-items: center;

    &-item {
      display: flex;
      align-items: center;

      &-title {
        flex-shrink: 0;
        font-size: $font-size-middle;
      }

      &:last-child {
        margin-left: 10rpx;
      }
    }
  }

  .title {
    // border: 1px solid #000;
    position: relative;
    display: flex;
    align-items: center;
    color: $textBlack;
    font-size: $font-size-middle;
    font-weight: bold;
    margin: 20rpx 0;

    .title_text {
      margin-top: 30rpx;
    }

    .necessary {
      color: red;
      margin-left: 4rpx;
    }
  }

  .btn {
    margin-top: 50rpx;

    .examine {
      margin-top: 40rpx;
    }
  }

  .volume {
    position: relative;

    &-mark {
      position: absolute;
      top: 0;
      right: 0;
      width: 60rpx;
      height: 80rpx;
      text-align: center;
      line-height: 80rpx;
    }
  }

  .time {
    display: flex;

    .end-time {
      margin-left: 20rpx;
    }
  }
}

.time {
  display: flex;

  .end-time {
    margin-left: 20rpx;
  }
}

.map-box ::v-deep .u-input__input {
  font-size: 22rpx !important;
}

.map-box {
  position: relative;
  height: 80rpx;
  color: $themeColor;

  &-title {
    height: 80rpx;
    display: flex;
    align-items: center;
    position: absolute;
    right: 30rpx;
    z-index: 99;
    top: 0;

    &-txt {
      margin-left: 10rpx;
      margin-top: -4rpx;
    }
  }
}
</style>
