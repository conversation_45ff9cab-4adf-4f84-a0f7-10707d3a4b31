<template>
  <view>
    <BaseNavbar :title="title" />
    <view class="bindHotelCard">
      <view class="deviceCode">
        <view>设备编号</view>
        <view>{{ deviceInfo.device_sn ? deviceInfo.device_sn : "" }}</view>
      </view>
      <view class="hotelName" @click="selectHotel">
        <view>{{ vPointName }}名称</view>
        <view class="hotelName_value">{{ hotelName ? hotelName : "-选择" + vPointName + "-" }}</view>
      </view>
      <LocationType @locationStr="locationStr" :str="room_num" :floors="floor" :numbers="number"></LocationType>

    </view>
    <BaseButton class="bind" width="630" @onClick="bindHotel">绑 定</BaseButton>
    <BaseButton class="bind" width="630" @onClick="gotoAddPoint">
      新增{{ vPointName }}并绑定
    </BaseButton>
    <BaseButton class="bind" width="630" @onClick="goToDeviceDetail" v-show="isShowDetail">
      设备详情</BaseButton>
  </view>
</template>

<script>
import BaseNavbar from "@/components/base/BaseNavbar.vue";
import BaseButton from "../../components/base/BaseButton.vue";
import BaseRadio from "../../components/base/BaseRadio.vue";
import LocationType from '../components/selects/LoctionType.vue';

export default {
  components: { BaseNavbar, BaseButton, BaseRadio, LocationType },
  data() {
    return {
      title: "设备绑定",
      device_sn: "", //设备编号
      mid: "", //设备id
      isFromDevice: false, //设备来的
      deviceInfo: {}, // 设备信息
      isShowDetail: false,
      hotelName: "",
      hotel_id: "",
      isFromIndexScan: false, //首页扫码来的
      room_num: "",
      floor: 1,
      number: 1,

    };
  },
  methods: {
    locationStr(str, floor, number) {
      this.floor = floor;
      this.number = number;

      this.room_num = str;
      // console.log('位置信息', str, floor, number)

    },
    async getUMHotel() {
      let data = {
        mid: this.mid,
      };
      let rtnData = await this.$u.api.getUMHotel(data);
      if (rtnData) {
        this.deviceInfo = rtnData[0] ? rtnData[0] : rtnData;
        if(this.deviceInfo.room_num){
          this.room_num = this.deviceInfo.room_num;
        }
     
        this.hotelName = this.deviceInfo.hotelName;
        this.hotel_id = this.deviceInfo.hotel_id;
        if (!this.deviceInfo.hotel_id) {
          // 未绑定点位
          let select_hotel = uni.getStorageSync("select_hotel");
          let input_wc = uni.getStorageSync("input_wc");
          if(input_wc){
            this.floor=input_wc.floor
            this.number=input_wc.number
          }
          if (select_hotel) {
            this.hotelName = select_hotel.hotelName;
            this.hotel_id = select_hotel.hotel_id;
            // console.log('本地有没有存储',select_hotel, input_wc )
          }
        }
      }
    },
    bindHotel() {
      if (!this.floor) {
        return this.isShowErr("请输入楼层");

      } else if (!this.number) {
        return this.isShowErr("请输入坑位编号");
      }
      uni.setStorage({
        key: "input_wc",
        data: {
          floor: this.floor,
          number: this.number,
        },
      });
      // this.$u.vuex("vWC",wc)
      // 绑定点位
      let data = {
        hotel_id: this.hotel_id,
        mid: this.mid,
        room_num: this.room_num,
      };
      uni.setStorageSync("bindId_key", data);
      this.$u.api.bindHotel(data).then((res) => {
        this.isShowSuccess("绑定成功", 1, () => { }, true);
      });
    },
    selectHotel() {
      uni.navigateTo({
        url: `/pagesB/place/SelectPlace?from=home_device_bind_hotel`,
      });
    },
    goToDeviceDetail() {
      uni.navigateTo({
        url:
          "/pagesB/device/DeviceDetail?from=home_device_bind_hotel&mid=" +
          this.mid,
      });
    },
    gotoAddPoint() {
      if (this.deviceInfo.device_sn) {
        uni.navigateTo({
          url: `/pagesB/place/PlaceAdd?from=add_and_bind&device_sn=${this.deviceInfo.device_sn}&mid=${this.mid}`,
        });
      }
    },
  },
  onLoad(opt) {
    if (opt?.from) {
      if (opt.from == "device") {
        // 设备绑定来的
        this.isFromDevice = true;
      } else if (opt.from == "index_scan") {
        // 首页扫码来的
        this.isFromIndexScan = true;
      }
    }
    this.device_sn = opt?.device_sn;
    this.mid = opt?.mid;
    // 是否显示按钮

    if (this.vUserInfo.role_id > 1 && this.vButtonPermissions) {
      this.isShowDetail = true;
    } else {
      this.isShowDetail = false;
    }
    this.getUMHotel();
  },
  onShow(e) {


    /*  #ifndef H5 */

    let pages = getCurrentPages();
    let currPage = pages[pages.length - 1]; // 当前页
    console.log('路由信息', currPage,currPage.data.wc,currPage.data.item)
    // if (currPage.data.floor&& currPage.data.number) {
    //   this.floor = currPage.data.floors || 1
    //   this.number = currPage.data.numbers || 1
    // }
    if (currPage.data.item && currPage.data.item.hotelName) {
      // 有值
      // 修改listData中值
      this.checkHotel = currPage.data.item;
      // console.log('暂存的值', this.checkHotel)
      this.hotelName = this.checkHotel.hotelName;
      this.hotel_id = this.checkHotel.id;
      // 缓存数据
      uni.setStorage({
        key: "select_hotel",
        data: {
          hotelName: this.hotelName,
          hotel_id: this.hotel_id,
        },
      });
    } else {
      console.log('进来了')
      this.hotelName = '';
      this.hotel_id = ''
    }

    if (currPage.data.isDoRefresh == true) {
      // 是否刷新
      currPage.data.isDoRefresh = false;
      this.getUMHotel();
    }
    /*#endif */
    /*  #ifdef H5 */
    if (this.vCurrPage.item) {
      // 有值
      // 修改listData中值
      this.checkHotel = this.vCurrPage.item;
      this.hotelName = this.checkHotel.hotelName;
      this.hotel_id = this.checkHotel.id;
      // 缓存数据
      uni.setStorage({
        key: "select_hotel",
        data: {
          hotelName: this.hotelName,
          hotel_id: this.hotel_id,
        },
      });
    }
    if (this.vCurrPage.isDoRefresh == true) {
      // 是否刷新
      this.vCurrPage.isDoRefresh = false;
      this.getUMHotel();
    }
    /*#endif */
  },
};
</script>

<style lang="scss">
page {
  background-color: $pageBgColor;
}
</style>
<style lang="scss" scoped>
.bindHotelCard {
  margin-top: 20rpx;
  // height: 300rpx;
  width: 100%;
  background: white;
  font-size: 26rpx;
  color: $textBlack;
}

.ridio {
  width: 480rpx;
}

.rideo-chunk {
  border: 1rpx solid #cbcbcb;
  padding: 10rpx;

}

.deviceCode,
.hotelName {
  // height: 100rpx;
  padding: 25rpx 0;
  display: flex;
  flex-direction: row;
  align-content: center;
  align-items: center;
  margin-left: 35rpx;
  border-bottom: 1rpx solid #e5e5e5;
}

.roomNumber {
  // height: 150rpx;
  padding: 25rpx 0;
  display: flex;
  flex-direction: row;
  align-content: center;
  align-items: center;
  margin-left: 35rpx;
  border-bottom: 1rpx solid #e5e5e5;
  border-bottom: 0;
}

.deviceCode view:first-child,
.hotelName view:first-child,
.roomNumber view:first-child {
  margin-right: 30rpx;
}

.hotelName {

  overflow-wrap: normal;

  .hotelName_value {
    width: 550rpx;
  }
}

.bind {
  margin-top: 50rpx;
}

.addBtn {
  margin-top: 50rpx;
}
</style>
