<template>
  <view class="card" @click="doDetails">
    <view class="container">
      <view v-if="isShowCheck">
        <BaseCheck :show.sync="isCheck" />
      </view>
      <view class="container-right">
        <view class="top">
          <view class="top-title">广告图片</view>
          <view
            class="top-img"
            @click.stop="previewImage(info.img_url)"
            v-if="info.file_type == 1"
          >
            <image class="img" :src="info.img_url" />
          </view>
          <view class="top-img" v-else-if="info.file_type == 2">
            <video class="img" :src="info.img_url" />
          </view>
        </view>
        <view class="content">
          <view class="content-box">
            <view class="title">排序</view>
            <view class="txt">
              <text user-select="true">{{ info.sort }}</text>
            </view>
          </view>
          <view class="content-box">
            <view class="title">广告名称</view>
            <view class="txt">
              <text user-select="true">{{ info.ad_desc }}</text>
            </view>
          </view>
          <view class="content-box">
            <view class="title">广告连接</view>
            <view class="txt">
              <text user-select="true">{{ info.img_href }}</text>
            </view>
          </view>

          <view class="content-box">
            <view class="title">播放时间</view>
            <view class="txt">{{ info.time || 0 }}秒</view>
          </view>
          <view class="content-box">
            <view class="title">广告状态</view>
            <view class="txt theme">
              {{ adStatus }}
            </view>
          </view>
          <view class="content-box">
            <view class="title">创建时间</view>
            <view class="txt">
              {{ $u.timeFormat(info.create_time * 1000, 'yy-mm-dd hh:MM:ss') }}
            </view>
          </view>
          <view class="content-box">
            <view class="title">是否删除</view>
            <view class="txt theme">
              {{ is_delete }}
            </view>
          </view>
          <view class="content-box">
            <view class="title">广告类型</view>
            <view class="txt">
              {{ file_type }}
            </view>
          </view>
          <view class="content-box">
            <view class="title">链接类型</view>
            <view class="txt">
              {{ link_type }}
            </view>
          </view>
          <view class="content-box">
            <view class="title">文字内容</view>
            <view class="txt">
              {{ info.content_text || '' }}
            </view>
          </view>
          <view class="content-box">
            <view class="title">是否在小程序显示</view>
            <view class="txt">
              {{ is_show_wechatmini }}
            </view>
          </view>

          <view class="content-box">
            <view class="title">订单来源</view>
            <view class="txt theme">
              {{ source }}
            </view>
          </view>
        </view>
      </view>
    </view>
    <view class="btn">
      <view class="btn-del">
        <BaseButton @onClick="isShowModal = true" type="default" shape="circle">
          删除广告
        </BaseButton>
      </view>
    </view>

    <BaseModal
      :show.sync="isShowModal"
      @confirm="delAd"
      title="温馨提示"
      content="您该条广告记录将会删除，此操作不可恢复，是否继续删除？"
    />
  </view>
</template>

<script>
import BaseButton from '@/components/base/BaseButton.vue'
import BaseModal from '@/components/base/BaseModal.vue'
import BaseCheck from '@/components/base/BaseCheck.vue'
export default {
  name: 'AdvertsManageCard',
  components: { BaseButton, BaseModal, BaseCheck },
  props: {
    info: { type: Object, default: {} },
    isShowCheck: { type: Boolean, default: false },
  },
  computed: {
    file_type() {
      switch (this.info.file_type) {
        case 1:
          return '图片'
        case 2:
          return '视频'
        default:
          return '未知'
      }
    },
    link_type() {
      switch (this.info.link_type) {
        case 0:
          return '图片'
        case 1:
          return '视频'
        case 2:
          return '音频'
        default:
          return '未知'
      }
    },
    is_show_wechatmini() {
      switch (this.info.is_show_wechatmini) {
        case 0:
          return '隐藏'
        case 1:
          return '显示'
        default:
          return '未知'
      }
    },
    source() {
      switch (this.info.source) {
        case 0:
          return '系统'
        case 1:
          return '广告投放'
        default:
          return '未知'
      }
    },
    adStatus() {
      switch (this.info.status) {
        case 0:
          return '禁用'
        case 1:
          return '启用'
        default:
          return '未知'
      }
    },
    is_delete() {
      switch (this.info.is_delete) {
        case 0:
          return '未删除'
        case 1:
          return '已删除'
        default:
          return '未知'
      }
    },
  },
  data() {
    return {
      isShowModal: false,
      isCheck: false,
    }
  },
  methods: {
    //预览图片
    previewImage(urls) {
      uni.previewImage({
        urls: [urls],
      })
    },
    doDetails() {
      if (this.isShowCheck) {
        this.isCheck = true
        /*  #ifndef H5 */
        let pages = getCurrentPages()
        // let currPage = pages[pages.length - 1] //当前页面
        let prevPage = pages[pages.length - 2] //上一个页面
        //直接调用上一个页面的setData()方法，把数据存到上一个页面中去
        prevPage.setData({
          item: this.info,
        })
        /*#endif */
        /*  #ifdef H5 */
        this.vCurrPage.item = this.info
        /*#endif */
        uni.navigateBack({ delta: 1 })
      } else {
        uni.navigateTo({
          url: '/pagesB/advertsManage/AdverstAdd?from=edit&id=' + this.info.id,
        })
      }
    },
    delAd() {
      let data = {
        id: this.info.id,
      }
      this.$u.api
        .deleateAdvert(data)
        .then((res) => {
          this.isShowSuccess('删除成功', 0, () => {
            this.$emit('refresh')
          })
        })
        .catch((err) => {
          console.log('错误信息', err)
        })
    },
  },
}
</script>

<style lang="scss" scoped>
.card {
  padding: 20rpx;
}
.container {
  display: flex;
  align-items: center;
  &-right {
    flex: 1;
  }
  .top {
    &-title {
      color: $textBlack;
      font-size: $font-size-xlarge;
      font-weight: 700;
      margin-bottom: 20rpx;
    }
    &-img {
      width: 100%;
      height: 240rpx;
      .img {
        width: 100%;
        height: 100%;
      }
    }
  }
  .content {
    margin-top: 20rpx;
    &-box {
      display: flex;
      justify-content: space-between;
      line-height: 1.8;
      font-size: $font-size-middle;
      .title {
        flex-shrink: 0;
        color: $textDarkGray;
      }
      .txt {
        text-align: right;
        color: $textBlack;
        margin-left: 20rpx;
      }
      .theme {
        color: $themeComColor;
      }
    }
  }
}
.btn {
  display: flex;
  justify-content: flex-end;
  margin-top: 20rpx;
  &-del {
  }
}
</style>
