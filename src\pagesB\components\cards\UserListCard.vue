<template>
  <view class="card" @click="clickCrad">
    <view class="card_top">
      <view class="text">
        {{ info.role_name }} -
        <text user-select>{{ info.user_login }}</text>
      </view>
      <view>
        <view>
          <text>余额：</text>
          <text @click.stop="many()" class="textBlack many">{{ info.cash ? info.cash : 0 }}元</text>
        </view>
        <view>
          <text>用户分成：</text>
          <text class="textBlack">
            {{ info.per ? info.per * 1 + '%' : '0%' }}
          </text>
        </view>
      </view>
      <view>
        <view>
          <text>状态：</text>
          <text class="textBlack">{{ info.user_status_text || '正常' }}</text>
        </view>
        <view>
          <text>微信号：</text>
          <text class="textBlack">
            <!-- #ifndef H5 -->
            {{ info.wx_user.nickname ? info.wx_user.nickname : '未绑定' }}
            <!-- #endif -->
            <!-- #ifdef H5 -->
            {{ info.user_nickname ? info.user_nickname : '未绑定' }}
            <!-- #endif -->
          </text>
        </view>
      </view>
    </view>
    <view class="card_btn flexRowBetween">
      <view class="card_btn_box" @click.stop="enableUser">
        {{ isEnableStatus ? '启用' : '禁用' }}
      </view>
      <view class="card_btn_box" @click.stop="bindWx">
        {{ isBindWx ? '微信解绑' : '微信绑定' }}
      </view>
      <view class="card_btn_box" @click.stop="setDivide">
        设置分成
      </view>
    </view>
  </view>
</template>

<script>
import BaseButton from '@/components/base/BaseButton.vue'
export default {
  components: { BaseButton },
  name: 'UserListCard',
  props: {
    info: { type: Object, default: {} },
  },
  computed: {
    isEnableStatus() {
      let status = this.info.user_status
      if (status == 0 || status == 2) {
        return true
      }
      return false
    },
    isBindWx() {
      return this.info.openid ? true : false
    },
  },
  methods: {
    setDivide() {
      this.$emit('setDivide')
    },
    enableUser() {
      this.$emit('enableUser')
    },
    bindWx() {
      this.$emit('bindWx')
    },
    clickCrad() {
      this.$emit('clickCrad')
    },
    many(){
      this.$emit('many')
    }
  },
}
</script>

<style lang="scss" scoped>
.card {
  &_top {
    > view {
      margin-bottom: 24rpx;
      display: flex;
      justify-content: space-between;

      &:first-child {
        color: $textBlack;
        font-size: $font-size-middle;
        font-weight: bold;
      }

      .textBlack {
        color: $textBlack;
      }
      .many {
        color: #0EADE2;
        text-decoration: underline;
      }
    }
    .text {
      display: inline-block;
    }
  }
  .card_btn {
    padding: 10rpx;
    flex-wrap: wrap;

    .card_btn_box {
      box-sizing: border-box;
      width: 190rpx;
      display: flex;
      justify-content: center;
      align-items: center;
      // width: 30%;
      border-radius: 50rpx;
      margin: 10rpx;
      padding: 20rpx 0rpx;
      border: 2rpx solid rgb(212, 212, 212);
      // &:nth-child(n + 4) {
      //   margin-top: 20rpx;
      // }
    }
  }
}
</style>
