<template>
  <view class="card">
    <view class="content">
      <view class="content-box">
        <view class="title-top">
          <view class="title-top-name">广告图片</view>
          <view class="title-top-details" @click="goDetails">查看详情</view>
        </view>
        <view class="content-box-img" @click="previewImage(info.url)">
          <image class="img" :src="info.url || vDefaultIcon" />
        </view>
        <view class="content-box-bottom">
          <view class="content-box-bottom-box">
            <view>设备编号</view>
            <view>{{ info.device_sn }}</view>
          </view>
          <view class="content-box-bottom-box">
            <view>订单编号</view>
            <view>{{ info.order_sn }}</view>
          </view>
          <view class="content-box-bottom-box">
            <view>广告类型</view>
            <view>{{ typeName }}</view>
          </view>
          <view class="content-box-bottom-box">
            <view>广告连接</view>
            <view @click.stop="gotoWebView(info.url_link)" class="link">
              <text user-select="true">{{ info.url_link }}</text>
            </view>
          </view>
          <view class="content-box-bottom-box">
            <view>申请用户</view>
            <view>{{ info.user_nickname }}</view>
          </view>
          <view class="content-box-bottom-box">
            <view>广告总价</view>
            <view>{{ info.amount.toFixed(2) }}</view>
          </view>
          <view class="content-box-bottom-box">
            <view>创建时间</view>
            <view>{{
                $u.timeFormat(info.add_time * 1000, "yyyy-mm-dd hh:MM:ss") ||
                $u.timeFormat(info.add_time * 1000, "yyyy-mm-dd hh:MM:ss")
            }}</view>
          </view>
          <view class="content-box-bottom-box" v-if="info.pay_status == 1">
            <view>付款时间</view>
            <view>{{
                $u.timeFormat(info.pay_time * 1000, "yyyy-mm-dd hh:MM:ss") ||
                $u.timeFormat(info.pay_time * 1000, "yyyy-mm-dd hh:MM:ss")
            }}</view>
          </view>
        </view>
      </view>
      <view class="bottom">
        <view class="bottom-status">
          <view>广告状态：</view>
          <view class="status">{{ statusData }}</view>
        </view>
        <view class="bottom-btn" v-if="info.pay_status == 1 && info.status == 0">
          <BaseButton width="200" type="default" shape="circle" @onClick="confirm">
            {{ btnTitle }}
          </BaseButton>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import BaseButton from "@/components/base/BaseButton.vue";
export default {
  components: { BaseButton },
  name: "AdvertsAuditListCard",
  props: { info: { type: Object, default: {} } },
  computed: {
    typeName() {
      if (this.info.type == 2) {
        return "插屏广告";
      } else if (this.info.type == 4) {
        return "屏幕广告";
      }
    },
    statusData() {
      if (this.info.pay_status == 0) {
        return "未付款";
      } else if (this.info.pay_status == 1) {
        switch (this.info.status) {
          case 0:
            this.btnTitle = "审核通过";
            return "待审核";
          case 1:
            this.btnTitle = "审核上线";
            return "已审核";
          case 2:
            return "已上线";
          default:
            return "未知";
        }
      }
    },
    // deviceSnList() {
    //   return JSON.parse(
    //     this.info.device_sn.includes("[") ? this.info.device_sn : "[]"
    //   )?.join("-");
    // },
  },
  data() {
    return {
      btnTitle: "",
    };
  },
  methods: {
    goDetails() {
      uni.navigateTo({
        url: `/pagesB/adverts/AdvertsDetails?order=${this.info.order_sn}`,
      });
    },
    gotoWebView(url) {
      uni.navigateTo({
        url: "/pagesC/webView/WebView?url=" + encodeURIComponent(url),
      });
    },
    confirm() {
      this.changeStatus();
    },
    changeStatus() {
      let data = { id: this.info.id },
        rtn = null;
      if (this.info.status == 0) {
        data["status"] = 1;
        rtn = this.$u.api.adChangeStatusProcessed(data);
      } else if (this.info.status == 1) {
        data["status"] = 2;
        rtn = this.$u.api.adGoOnline(data);
      }
      rtn?.then((res) => {
        this.$emit("confirm", this.btnTitle);
      })
      .catch((err=>{
        console.log('错误信息',err)
      }))
    },
    //预览图片
    previewImage(urls) {
      uni.previewImage({
        urls: [urls],
      });
    },
  },
};
</script>
<style lang="scss">
page {
  background-color: $pageBgColor;
}
</style>
<style lang="scss" scoped>
.card {}

.content {
  padding: 20rpx;

  &-box {

    // display: flex;
    .title-top {
      display: flex;
      justify-content: space-between;
      margin-bottom: 24rpx;

      &-name {
        color: $textBlack;
        font-size: $font-size-xlarge;
        font-weight: 700;
      }

      &-details {
        color: $themeComColor;
        font-size: $font-size-middle;
      }
    }

    &-img {
      width: 100%;
      height: 300rpx;

      // flex-shrink: 0;
      .img {
        width: 100%;
        height: 100%;
      }
    }

    &-bottom {
      margin-top: 30rpx;

      &-box {
        display: flex;
        justify-content: space-between;

        >view {
          line-height: 1.6;

          &:first-child {
            color: $textDarkGray;
            white-space: nowrap;
            flex-shrink: 0;
          }

          &:last-child {
            color: $textBlack;
            margin-left: 150rpx;
          }
        }

        .link {
          color: rgb(41, 121, 255) !important;
          border-bottom: 1px solid rgb(41, 121, 255);
        }
      }
    }
  }
}

.bottom {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 20rpx;

  &-status {
    display: flex;

    .status {
      color: $themeComColor;
    }
  }
}
</style>
