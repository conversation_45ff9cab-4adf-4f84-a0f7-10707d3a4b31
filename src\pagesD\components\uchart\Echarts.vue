<template>
  <view class="card-charts">

    <view class="cahart-box">
      <!-- #ifndef H5 -->
      <canvas :canvas-id="isId" :id="isId" class="charts" @touchend="tap" />
      <!-- #endif -->
      <!-- #ifdef H5 -->
      <view class="total">
        <view class="tilte1">{{ title1 }}</view>
        <view class="tilte2">{{ title2 }}单</view>
      </view>
      <canvas :canvas-id="isId" :id="isId" class="charts" @touchend="tap" />
      <!-- #endif -->
      <view class="buttom">
        <view v-for="(item, i) in data" :key="i" class="buttom-item">
          <view :style="{ backgroundColor: color[i] }" class="cirea"></view>
          <view>
            <view class="green">{{ item.name }}</view>
            <view class="green-bold">
              {{ listNum ? ((item.value / listNum) * 100).toFixed(2) : '100' }}%
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>
<script>
//文档
// https://www.ucharts.cn/v2/#/
/* #ifdef H5 */
import * as echarts from 'echarts';
/* #endif */
/* #ifndef H5 */
import uCharts from '@qiun/ucharts'
/* #endif */
var uChartsInstance = {}
var myChart = {}
export default {
  name: "UCharts",
  props: {
    isId: {
      type: String,
      default: 'userche',
    },
    dataList: {
      type: Array,
      default: function () {
        return [];
      },
    },
    title1: {
      type: String,
      default: '收益数量'
    },
    title2: {
      type: [String, Number],
      default: '0'
    }
  },
  watch: {
    lineData: {
      handler: function (val) {
        this.getServerData()
      },
    },
  },
  data() {
    return {
      cWidth: 700,
      /* #ifndef H5 */
      cHeight: 500,
      /* #endif */
      /* #ifdef H5 */
      cHeight: 280,
      /* #endif */
      id: 'wESQjyHSJjWTvdwmeCcObWtgnvMkVSVg',
      data: [],
      color: [
        '#1890FF',
        '#91CB74',
        '#FAC858',
        '#EE6666',
        '#73C0DE',
        '#3CA272',
        '#FC8452',
        '#9A60B4',
        '#ea7ccc',
      ],
      index: 0,
      title: '',
      subtitle: 0,
      listNum: 0,
      //       dataList:[{order_amount: "7.98元",
      // g_total_num: 13},{order_amount: "6.98元",
      // g_total_num: 10},{order_amount: "7.98元",
      // g_total_num: 9},{order_amount: "15.98元",
      // g_total_num: 8},{order_amount: "20.98元",
      // g_total_num: 7},{order_amount: "2.98元",
      // g_total_num: 6},{order_amount: "15.98元",
      // g_total_num: 5},{order_amount: "18.98元",
      // g_total_num: 1},]
    };
  },
  /* #ifndef H5 */
  onReady() {
    this.cWidth = uni.upx2px(700)
    //这里的 500 对应 css .charts 的 height
    this.cHeight = uni.upx2px(500)
    this.getServerData()
  },
  /* #endif */

  methods: {
    getServerData() {
      //模拟从服务器获取数据时的延时
      setTimeout(() => {
        //模拟服务器返回数据，如果数据格式和标准格式不同，需自行按下面的格式拼接
        let num = 0
        if (this.dataList.length > 0) {
          this.dataList.map((item, i) => {
            if (i < 7) {
              this.data.push({
                name: item.order_amount + '元',
                value: item.g_total_num,
              })
            } else {
              num = num + item.g_total_num
            }
            this.listNum = this.listNum + item.g_total_num
          })
          if (this.dataList.length > 7) {
            this.data.push({
              name: '其他',
              value: num,
            })
          }
        } else {
          this.data.push({
            name: 0 + '元',
            value: 0,
          })
        }
        let res = {
          series: [
            {
              data: this.data,
            },
          ],
        }
        this.drawCharts(this.isId, res)
      }, 500)
    },
    /* #ifdef H5 */
    drawCharts(id, data) {
      const canvas = document.getElementById(id);
      myChart = echarts.init(canvas);
      const option = {
        tooltip: {
          show: false,
          triggerOn: 'click',
          trigger: 'item',
          formatter(param) {
            // correct the percentage
            return param.name + param.value + '单';
          }
        },
        legend: {
          show: false,
        },
        color: this.color,
        series: [
          {
            name: this.title1,
            type: 'pie',
            radius: ['40%', '55%'],
            avoidLabelOverlap: false,
            label: {
              fontSize: 9,
              show: true,
              formatter(param) {
                // correct the percentage
                return param.name + param.value + '单';
              }
            },
            emphasis: {
              label: {
                show: true,
                fontSize: 10,
                fontWeight: 'bold'
              }
            },
            title: {
              name: this.title1,
              fontSize: 15,
              color: '#666666',
            },

            data: data.series[0].data
          }
        ]
      };
      option && myChart.setOption(option);
      myChart.on('click', (params) => {
        const dataIndex = params.dataIndex;
        // 根据索引值进行其他操作
        // ...
      });
    },
    /* #endif */
    /* #ifndef H5 */
    drawCharts(id, data) {
      const ctx = uni.createCanvasContext(id, this)
      uChartsInstance[id] = new uCharts({
        type: 'ring',
        context: ctx,
        width: this.cWidth,
        height: this.cHeight,
        series: data.series,
        animation: true,
        rotate: false,
        rotateLock: false,
        background: '#FFFFFF',
        title: {
          name: this.title1,
          fontSize: 15,
          color: '#666666',
        },
        subtitle: {
          name: this.title2 + '单',
          fontSize: 25,
          color: '#7cb5ec',
        },
        color: this.color,
        padding: [5, 5, 5, 5],
        dataLabel: true,
        enableScroll: false,
        legend: {
          show: false,
        },
        extra: {
          ring: {
            ringWidth: 20,
            activeOpacity: 1,
            activeRadius: 10,
            offsetAngle: 0,
            labelWidth: 15,
            borderWidth: 3,
            activeRadius: 5,
          },
        },
      })
    },
    handleClick(params) {
      // 获取点击的元素数据索引
      const dataIndex = params.dataIndex;
      // 输出索引值
      // 根据索引值进行其他操作
      // ...
    },
    /* #endif */
    tap(e) {
      // uChartsInstance[id]
      //   uChartsInstance[e.target.id].touchLegend(e)
      /* #ifndef H5 */
      uChartsInstance[e.target.id].showToolTip(e, {
        formatter: (item, category, index, opts) => {
          return item.name + ':' + item.data + '单'
        },
      })
      /* #endif */
      /* #ifdef H5 */
      myChart.dispatchAction({
        type: 'downplay',
        dataIndex: 0
      });
      // 输出数据索引
      const test2 = myChart.convertToPixel()
      // 输出索引值
      // myChart.dispatchAction({
      //   type: 'highlight',
      //   dataIndex: 0
      // });
      /* #endif */
    },
  },
  /* #ifdef H5 */
  mounted() {
    this.cWidth = uni.upx2px(700)
    //这里的 500 对应 css .charts 的 height
    this.cHeight = uni.upx2px(500)
    this.getServerData()
  },
  /* #endif */

};
</script>


<style scoped  lang='scss'>
.cahart-box {

  position: relative;

  .cahart-tile-box {
    width: 700rpx;
    height: 500rpx;
    position: absolute;
    left: 0;
    top: 0;
    // z-index: -1;
  }
}

.total {
  width: 700rpx;
  height: 500rpx;
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;

  .title1 {
    font-size: 15;
    color: '#666666';
  }

  .title2 {
    color: #7cb5ec;
    font-size: 25rpx,
  }
}

.cahart-title {
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  margin: auto;
  width: 250rpx;
  height: 150rpx;
  display: flex;
  justify-content: center;
  align-items: center;

  //   border: 2rpx solid red;
  z-index: 9999;
}

.subtitle {
  text-align: center;
  font-size: 20rpx;
  color: #999;
}

.title {
  text-align: center;
}

.buttom {
  display: flex;
  flex-wrap: wrap;

  .cirea {
    border-radius: 50%;
    width: 30rpx;
    height: 30rpx;
    margin-right: 10rpx;
  }

  .buttom-item {
    width: 150rpx;
    height: 100rpx;
    margin: 10rpx;
    display: flex;
    align-items: center;
    // border: 2rpx solid red;
  }

  .green {
    color: #999;
  }

  .green-bold {
    font-weight: bold;
  }
}

.charts {
  width: 700rpx;
  height: 500rpx;
}
</style>