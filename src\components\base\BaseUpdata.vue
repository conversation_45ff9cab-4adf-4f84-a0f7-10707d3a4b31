<template>
    <view class="updata_box">
        <view :style="{ fontSize: `${size}rpx`, color: `${color}` }">
            {{ updataTime }}
        </view>
        <view @click.stop="onClick" class="updata_icon">
            <u-icon :name="icon" :size="iosSize?iosSize:size" :color="color"></u-icon>
        </view>
    </view>
</template>
  
<script>
import { timestampToTime } from "@/common/tools/utils.js";


export default {

    name: "BaseUpdata",
    props: {
        title: {
            type: String,
            default: '设备'
        },
        icon: {
            type: String, //按钮类型primary default
            default: "reload",
        },
        iosSize: {
            type: String, //按钮类型primary default
            default: "",
        },
        size: {
            type: String, //按钮类型primary default
            default: "20",
        },
        color: {
            type: String, //按钮类型primary default
            default: "#999",
        },
        refresh: {
            type: Boolean,
            default: false
        },
        time: {
            type: '',
            default: ''
        }

    },
    data() {
        return {
            upTime: ''
        };
    },
    computed: {
        updataTime() {
            if (this.time) {
                return this.title + '更新时间:' + this.time
            } else {
                return this.title + '更新时间:' + this.upTime
            }
        },
    },
    created() {
        this.newTime()
    },
    methods: {
        onClick() {
            this.$emit('onClick');
        },
        updateChildData() {
            this.newTime()

        },
        /* 获取当前时间 */
        newTime() {
            let a = new Date()
            this.upTime = timestampToTime(a)
        },
        /* 更新时间 */
        undataTimes() {
            this.newTime()
        },
    },
    watch: {
        refresh(newValue) {
            // 在这里执行子组件的重新渲染操作
            this.newTime()
        }
    }

};
</script>
  
<style lang="scss" scoped>
.updata_box {
    width: 100%;
    // height: 200rpx;
    // border: 1px solid #000;
    display: flex;
    justify-content: center;
    align-items: center;

    .updata_icon {
        margin-left: 5rpx;
        display: flex;
        justify-content: center;
        align-items: center;
        // border: 1px solid #000;
    }
}
</style>