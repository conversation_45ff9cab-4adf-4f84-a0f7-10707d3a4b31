<template>
  <view>
    <BaseNavbar :title="title + '套餐'" />
    <view class="content">

      <view class="content-box">
        <view class="content-box-title">充值金额：</view>
        <BaseInput placeholder="请输入充值金额" v-model="recharge_money" rightText="元" />
      </view>
      <view class="content-box">
        <view class="content-box-title">赠送金额</view>
        <BaseInput placeholder="请输入赠送金额" v-model="gift_money" rightText="元" />
      </view>
      <!-- 保存按钮 -->
      <view class="footer">
        <view class="footer__buttom">
          <BaseButton @onClick="save">{{ from == 'edit' ? '保存' : '添加' }}</BaseButton>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import BaseButton from "../../components/base/BaseButton.vue";
import BaseInput from "../../components/base/BaseInput.vue";
import BaseNavbar from "@/components/base/BaseNavbar.vue";
export default {
  components: { BaseNavbar, BaseInput, BaseButton },
  data() {
    return {
      recharge_money: '',
      gift_money: '',
      title: '添加套餐',
      id: '',
      from: ''
    };
  },
  methods: {
    async save() {
      try {
        let data = {
          type: "1", //活动类型 1：充值活动
          use_range: "1", //使用范围 1:所有设备
          recharge_money: this.recharge_money,
          gift_money: this.gift_money
        }
        if (this.from == 'edit') {
          data[id] = this.id
          await this.$u.api.editPackage(data)
          this.isShowSuccess('保存成功', 1, () => { }, true)
           
        } else {
          await this.$u.api.addPackage(data)
          this.isShowSuccess('添加成功', 1, () => { }, true)
        }
      }catch(error){
        console.log(error)
      }
     

    },
  },
  onLoad(opt) {
    // this.device_sn = opt?.device_sn || "";
    this.from = opt.from
    if (this.from == 'edit') {
      this.title = '编辑'
      let data = JSON.parse(decodeURIComponent(opt.item))
      this.id = data.id
      this.recharge_money = data.recharge_money
      this.gift_money = data.gift_money
      console.log('编辑', data)
    } else {
      this.title = '添加'
    }
    /* #ifdef H5 */
    uni.setNavigationBarTitle({
      title: this.title + '套餐'
    });
    /* #endif */
    // this.getAdPrice();
  },
};
</script>

<style lang="scss" scoped>
.content {
  padding: 20rpx 30rpx;

  &-box {
    margin-bottom: 20rpx;

    &-title {
      display: flex;
      align-items: center;
      color: $textBlack;
      font-size: $font-size-middle;
      font-weight: bold;
      margin-bottom: 20rpx;
    }
  }

  .footer {
    margin-top: 100rpx;
  }
}
</style>
