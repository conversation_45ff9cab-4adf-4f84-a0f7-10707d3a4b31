<template>
  <view>
    <BaseNavbar :title="title" />
    <block v-if="tabList[curTabIndex]">
      <BaseTabs
        :list="tabList"
        :current="curTabIndex"
        :isShowBar="false"
        @change="tabChange"
      />
      <!-- <view
        class="top_title"
        @click="toRemittance"
        v-if="tabList[curTabIndex].way == 4"
      >
        <image
          src="../static/img/icon/secsse.png"
          v-if="tabList[curTabIndex].way == 4 && !isBindHzf"
        />

        {{
          tabList[curTabIndex].way == 4 && !isBindHzf
            ? '已实名认证'
            : '填写实名信息>>'
        }}
      </view> -->
      <view
        class="hzfCard"
        @click="toRemittance"
        v-if="tabList[curTabIndex].way == 4"
      >
        <block v-if="tabList[curTabIndex].way == 4 && !isBindHzf">
          <view class="hzfCard-title">到账{{ tabList[curTabIndex].name }}{{real_info_type==1?'(对公)':'(对私)'}}</view>
          <view>
            <view class="hzfCard-text">
              <view>持卡人</view>
              <view>{{ user_name }}</view>
            </view>
            <!-- <view class="hzfCard-text">
              <view>所属银行</view>
              <view>{{ '中国建设银行' }}</view>
            </view> -->
            <view><BaseIcon name="arrow-right" color="#333" size="30" /></view>
          </view>
          <view >
            <view class="hzfCard-text">
              <view>银行卡号</view>
              <view>{{ newStr(card_id) }}</view>
            </view>
          </view>
        </block>
        <block v-else @click="toRemittance">
          <view class="hzfCard-title">到账{{ '银行卡' }}</view>
          <view>
            <view class="hzfCard-undefind">
              <view>请实名认证并绑定</view>
            </view>
            <view><BaseIcon name="arrow-right" color="#333" size="40" /></view>
          </view>
        </block>
      </view>
      <WithdrawalCard
        v-if="tabList.length > 0"
        :balance="balance"
        ref="withdrawal"
        :type="curTabIndex"
        :name="tabList[curTabIndex].name"
        @confirm="confirm"
        :maxMoney="5000"
        :way="tabList[curTabIndex].way"
      />
      <BaseModal
        :show.sync="isconfirmWxcode"
        :mask="true"
        title="是否去绑定微信"
        @confirm="confirmWxcode"
      ></BaseModal>
      <BaseModal
        :show.sync="isShowWxQrcode"
        :isShowCancel="false"
        :mask="true"
        title="绑定微信"
        @confirm="confirmScanWxQrcode"
      >
        <view class="qrcode">
          <view class="qrcode-img">
            <Uqrcode
              ref="uQRCode"
              :text="vSelectSysObj.extra"
              :margin="10"
              mode="view"
            />
          </view>
          <view class="qrcode-tip">
            <view>您还未绑定微信</view>
            <view>请扫描此二维码绑定微信后提现</view>
          </view>
        </view>
      </BaseModal>
    </block>
    <BaseModal
      :show.sync="isconfirmHzfcode"
      :mask="true"
      title="需要进行实名认证后,可提现银行卡？"
      @confirm="confirmHzfcode"
    ></BaseModal>
    <view class="tips" v-if="tipsList[0]">
      <view class="tips-title">温馨提示</view>
      <view class="tips-box" v-for="(item, index) in tipsList" :key="index">
        {{ index + 1 }}、{{ item }}
      </view>
    </view>
  </view>
</template>

<script>
import BaseModal from '@/components/base/BaseModal.vue'
import BaseNavbar from '@/components/base/BaseNavbar.vue'
import BaseTabs from '@/components/base/BaseTabs.vue'
import Uqrcode from '@/components/uqrcode/uqrcode.vue'
import WithdrawalCard from '../components/cards/WithdrawalCard.vue'
import BaseIcon from '@/components/base/BaseIcon.vue'
import {newBit} from '@/wxutil/newBit'
export default {
  components: {
    BaseNavbar,
    BaseTabs,
    WithdrawalCard,
    BaseModal,
    Uqrcode,
    BaseIcon,
  },
  data() {
    return {
      title: '余额提现',
      tabList: [],
      curTabIndex: 0,
      isBindWx: false, //是否需要绑定微信
      isBindHzf: false, //是否绑定了汇支付
      userInfo: {},
      isShowModalQrcode: false,
      isconfirmWxcode: false, //绑定微信弹窗
      isShowWxQrcode: false, //绑定信息
      isconfirmHzfcode: false, //汇支付弹窗
      tipsList: [],
      confick: false,
      balance: 0,
      user_name: '', //用户名称
      tel_no: '', //用户电话
      email: '', //用户邮箱
      location: '', //用户地址
      // gender: '', //用户性别
      // nickname: '', //用户昵称
      cert_id: '', //用户身份证号
      card_id: '', //银行卡号
      real_info_type:0,//对私对公
    }
  },
  methods: {
    /* 弹出绑定微信 */
    confirmWxcode() {
      this.isShowWxQrcode = true
    },
    /* 汇支付跳转 */
    confirmHzfcode() {
      uni.navigateTo({ url: '/pagesD/Remittance/Remittance' })
    },
    toRemittance() {
      if (!this.isBindHzf) {
        uni.navigateTo({ url: '/pagesD/Remittance/Remittance?from=edit' })
      } else {
        uni.navigateTo({ url: '/pagesD/Remittance/Remittance' })
      }
    },
    tabChange(e) {
      this.curTabIndex = e
      let selectWay = this.tabList[e].way
      if (selectWay == 2 && this.isBindWx) {
        this.isconfirmWxcode = true
      } else if (selectWay == 4 && this.isBindHzf) {
        this.isconfirmHzfcode = true
      }
      this.setStorageValue(selectWay)
    },
    setStorageValue(selectWay) {
      // 缓存值设置
      // 获取数据
      let select_way = uni.getStorageSync('withdrawal_' + selectWay)
      this.$refs.withdrawal.financeForm.remarks = select_way.remarks
      this.$refs.withdrawal.financeForm.name = select_way.name
      this.$refs.withdrawal.financeForm.number = select_way.number
    },
    confirm(e) {
      if (this.confick) {
        return
      }
      this.confick = true
      this.withdrawal(e, this.tabList[this.curTabIndex].way)
    },
    getUserDetail() {
      let data = {
        id: this.vUserInfo?.user?.id,
      }
      let that = this
      this.$u.api.getUserDetail(data).then((res) => {
        // console.log("🚀 ~ res 1 ", res);
        that.userInfo = res
        that.isBindWx = res.openid == '' || !res.openid
        that.isBindHzf = res.identified != 1
        // that.isBindWx = (res.openid == "");
        if (
          that.$refs.withdrawal &&
          that.$refs.withdrawal.financeForm.balance
        ) {
          that.$refs.withdrawal.financeForm.balance = res.cash
        }
        that.vUserInfo.user.cash = res.cash
        if (!that.isBindHzf) {
          this.$u.api.getRealInfo().then((res) => {
            let data = res
            this.location = data?.location || ''
            this.tel_no = data?.tel_no || ''
            this.email = data?.email || ''
            // this.gender = data?.gender || ''
            this.cert_id = data?.cert_id || ''
            this.cert_validity_type = data?.cert_validity_type || 0
            this.user_name = data?.user_name || ''
            this.card_id = data?.card_id || ''
            this.real_info_type = data?.real_info_type || 0

          })
        }
      })
    },
    newStr(str){
      return newBit(str)
    },
    withdrawal(info, way) {
      let data = {
        way: way,
      }
      data['money'] = info.balance
      data['remark'] = info.remarks
      data['realname'] = info.name
      data['bank_card'] = info.number
      if (way == 2) {
        data['openid'] = this.userInfo.openid
      }
      let that = this
      this.$u.api
        .withdrawalMoney(data)
        .then((res) => {
          // 存储remark, realnmae,bankcard
          // 缓存数据
          uni.setStorage({
            key: 'withdrawal_' + way,
            data: {
              remarks: info.remarks,
              name: info.name,
              number: info.number,
            },
          })
          that.vUserInfo.user.cash = (
            that.vUserInfo.user.cash - info.balance
          ).toFixed(2)
          that.confick = false
          that.isShowSuccess('提现成功', 1, () => {}, true)
        })
        .catch((err) => {
          that.isShowSuccess(err || '提现失败')
          that.confick = false
        })
    },
    confirmScanWxQrcode() {
      uni.navigateTo({
        url:
          '/pagesC/webView/WebView?url=' +
          encodeURIComponent(this.vSelectSysObj.extra),
      })
    },
  },
  onLoad(opt) {
    // console.log('是否有值', this.vSiteConfig)
    this.tabList = this.vSiteConfig?.withdrawal_way || []
    // this.tabList = [{
    //   way:2,name:'微信'
    // },{
    //   way:4,name:'汇支付'
    // }];
    if (
      this.vSiteConfig &&
      this.vSiteConfig.site_info &&
      this.vSiteConfig.site_info.withdrawal_prompt
    ) {
      this.tipsList =
        this.vSiteConfig?.site_info?.withdrawal_prompt.split('|') || []
    }
    this.balance = opt.balance
    this.getUserDetail()

    // let e = this.curTabIndex
    // let selectWay = this.tabList[e].way;
    // if (selectWay == 2 && this.isBindWx) {
    //   this.isconfirmWxcode = true;
    // } else if (selectWay == 4 && this.isBindHzf) {
    //   this.isconfirmHzfcode = true
    // }
  },
  onShow() {
    //this.setStorageValue(this.tabList[this.curTabIndex].way);
    /*  #ifndef H5 */
    let pages = getCurrentPages()
    let currPage = pages[pages.length - 1] // 当前页
    if (currPage.data.isDoRefresh == true) {
      // 是否刷新
      this.getUserDetail()
    }
    /*#endif */
    /*  #ifdef H5 */

    if (this.vCurrPage.isDoRefresh == true) {
      // 是否刷新
      this.getUserDetail()
    }
    /*#endif */
  },
  mounted() {
    if (this.tabList && this.tabList[this.curTabIndex]) {
      this.setStorageValue(this.tabList[this.curTabIndex].way)
    }
  },
}
</script>
<style lang="scss">
page {
  background-color: $pageBgColor;
}
</style>
<style lang="scss" scoped>
.tips {
  padding: 30rpx;
  font-size: $font-size-base;

  &-box {
    line-height: 1.8;
  }
}

.top_title {
  // border: 1px solid #000;
  font-size: 20rpx;
  color: #0eade2;
  margin: 30rpx 30rpx 0 30rpx;
  text-decoration: underline;

  image {
    width: 20rpx;
    height: 20rpx;
    vertical-align: middle;
    margin: 0 10rpx;
  }
}
.hzfCard {
  background-color: #fff;
  padding: 20rpx;
  margin: 30rpx 30rpx 0 30rpx;
  border-radius: 15rpx;
  font-size: 30rpx;
  .hzfCard-title {
    margin-bottom: 5rpx;
    font-weight: bold;
  }
  > view {
    display: flex;
    // border: 2rpx solid red;
    justify-content: space-between;
    padding: 10rpx 20rpx;
    align-items: center;
  }

  .hzfCard-text {
    display: flex;
    // border: 2rpx solid red;
    // justify-content: space-between;
  }
  .hzfCard-text > view:nth-child(1) {
    font-weight: bold;
    margin-right: 10rpx;
  }
  .hzfCard-undefind {
    font-size: 50rpx;
    color: #333;
  }
}
.tips-title {
  margin-bottom: 20rpx;
  color: red;
}
.qrcode {
  display: flex;
  flex-direction: column;
  justify-content: center;
  text-align: center;

  &-top {
    color: $textBlack;
    font-size: $font-size-xlarge;
    font-weight: 700;
  }

  &-img {
    margin: 0 auto;
  }

  &-tip {
    margin-top: 10rpx;
  }
}
</style>
