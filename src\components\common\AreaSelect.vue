<template>
  <view>
    <BaseInput
      v-model="value"
      rightText="arrow"
      :disabled="true"
      :placeholder="placeholder"
      @onClick="show = true"
    />
    <u-picker
      mode="region"
      v-model="show"
      :safe-area-inset-bottom="true"
      @confirm="confirm"
      :default-region="defaultRegion"
    />
  </view>
</template>

<script>
import BaseInput from "../base/BaseInput.vue";
export default {
  components: { BaseInput },
  name: "AreaSelect",
  props: {
    placeholder: { type: String, default: "请输入内容" },
    value: { type: String, default: "" },
    defaultRegion: {
      type: Array,
      default: function () {
                return [];
            }
    }, //默认区域
  },
  data() {
    return {
      show: false,
      defaultTime: "",
      startYear: "",
    };
  },
  methods: {
    confirm(e) {
      let { province, city, area } = e;
      this.$emit("input", `${province.label}-${city.label}-${area.label}`);
      this.$emit('changs',e)
    },
  },
  onReady() {},
};
</script>

<style lang="scss" scoped>
</style>