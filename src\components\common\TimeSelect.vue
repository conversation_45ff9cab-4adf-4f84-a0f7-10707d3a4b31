<template>
  <view>
    <BaseInput v-model="value" rightText="arrow" :disabled="true" :placeholder="placeholder" @onClick="show = true" />
    <u-picker mode="time" v-model="show" :start-year="startYear" :default-time="defaultTime"
      :safe-area-inset-bottom="true" @confirm="confirm" />
  </view>
</template>

<script>
import BaseInput from "../base/BaseInput.vue";
export default {
  components: { BaseInput },
  name: "TimeSelect",
  props: {
    placeholder: { type: String, default: "请输入内容" },
    value: { type: String, default: "" },
    times: { type: Boolean, default: false }
  },
  data() {
    return {
      show: false,
      defaultTime: "",
      startYear: "",
    };
  },
  methods: {
    confirm(e) {
      let { year, month, day } = e;
      this.$emit("input", `${year}-${month}-${day}`);
    },
  },
  onReady() {
    let dateTime = new Date();
    let year = dateTime.getFullYear();
    let month =
      dateTime.getMonth() + 1 < 10
        ? "0" + (dateTime.getMonth() + 1)
        : dateTime.getMonth() + 1;
    let day =
      dateTime.getDate() < 10 ? "0" + dateTime.getDate() : dateTime.getDate();
    this.defaultTime = `${year}-${month}-${day}`;
    this.startYear = year - 2;
    if (this.times) {
      this.startYear = year - 80;
    } else {

      this.startYear = year - 2;
    }

  },
};
</script>

<style lang="scss" scoped></style>