<template>
  <view>
    <UniFilePicker
      v-model="imageValue"
      :fileMediatype="fileType"
      mode="grid"
      :auto-upload="auto"
      :limit="limit"
      @select="select"
      @delete="del"
    />
  </view>
</template>

<script>
import UniFilePicker from "@/components/uni-file-picker/uni-file-picker.vue";
export default {
  name: "UploadAll", //该组件只适用于 单个文件上传
  components: { UniFilePicker },
  props: {
    auto: { type: Boolean, default: true }, //自动上传
    limit: { type: [Number,String], default: 1 }, //最大选择数
    fileType: { type: String, default: "all" }, //image/video/all
    defaultImgList: { type: Array, default: [] }, //默认显示列表
  },

  computed: {
    action() {
      //return "https://test.51xhkj.com/api/common/imgUpload";

      return this.vSelectSysObj.extra + "/api/common/imgUpload";
    },
    header() {
      return {
        "XX-Token": this.vToken,
        "XX-Device-Type": "wxapp",
        "XX-Api-Version": "1.0.0",
        "XX-Wxapp-AppId": this.vAppId,
      };
    },
  },
  data() {
    return {
      imageValue: [
        // {
        //   url: "https://vkceyugu.cdn.bspapp.com/VKCEYUGU-dc-site/b7c7f970-517d-11eb-97b7-0dc4655d6e68.jpg",
        //   name: "shuijiao.png",
        // },
      ],
    };
  },
  methods: {
    select(e) {
      this.upLoad(e.tempFilePaths[0], e.tempFiles[0]);
    },
    upLoad(tempFilePaths, tempFiles) {
      uni.uploadFile({
        url: this.action,
        fileType: this.fileType === "all" ? "audio" : this.fileType,
        filePath: tempFilePaths,
        name: "file",
        header: this.header,
        success: ({ data, statusCode }) => {
          let newData = JSON.parse(data);
          this.imageValue = [
            {
              url: newData.data,
              name: tempFiles.name,
            },
          ];
          this.$emit("upload", this.imageValue[0]);
        },
        fail: (error) => {
          console.log("🚀 ~ error", error);
          this.imageValue = [];
          this.isShowErr("文件上传失败,请重试~");
        },
      });
    },
    del(e) {
      this.imageValue = this.imageValue?.filter(
        (item) => item.url != e.tempFilePath
      );

      this.$emit("upload", this.imageValue[0]);
    },
  },
  mounted() {
    this.imageValue = this.defaultImgList || [];
  },
};
</script>

<style lang="scss" scoped>
</style>