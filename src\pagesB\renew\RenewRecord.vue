<template>
  <view>
    <view class="sticker">
      <BaseNavbar title="续费记录" />
      <BaseTabs :current="curTabIndex" :list="tabList" :isShowBar="false" @change="tabChange" />
    </view>

    <ComList :loadingType="loadingType">
      <RenewRecordCard v-for="item in listData" :key="item.id" :info="item" />
    </ComList>
  </view>
</template>

<script>
import BaseNavbar from "@/components/base/BaseNavbar.vue";
import ComList from "@/components/list/ComList.vue";
import RenewRecordCard from "../components/cards/RenewRecordCard.vue";
import myPull from "@/mixins/myPull";
import BaseTabs from "../../components/base/BaseTabs.vue";
export default {
  components: { BaseNavbar, ComList, RenewRecordCard, BaseTabs },
  mixins: [myPull()],
  data() {
    return {
      tabList: [
        {
          name: "全部",
          status: 0,
        },
        {
          name: "订单完成",
          status: 1,
        },
        {
          name: "订单异常",
          status: 2,
        },
        {
          name: "订单取消",
          status: 3,
        },
      ],
      curTabIndex: 0,
      data:[{
        
      }]
    };
  },
  methods: {
    tabChange(e) {
      this.curTabIndex = e;
      this.refresh();
    },
    getList(page, done) {
      let data = {
        page,
        status: this.tabList[this.curTabIndex].status,
      };
      this.$u.api.getRenewOrder(data).then((res) => {
        done(res.data);
      });
    },
  },
  onLoad() {
    this.refresh();
  },
};
</script>
<style lang="scss">
page {
  background-color: $pageBgColor;
}
</style>
<style lang="scss" scoped>
</style>