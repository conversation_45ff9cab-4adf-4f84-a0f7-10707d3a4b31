<template>
  <view>
    <BaseNavbar :title="title" />
    <BaseDropdown :options-list="optionsList" @change="changeDropdown" />
    <ComList :loading-type="loadingType">
      <OrderListCard @refresh="refresh" v-for="item in 10" :key="item" />
    </ComList>
    <FixedAddIcon @onAdd="goMaintainApply" />
  </view>
</template>

<script>
import BaseNavbar from "@/components/base/BaseNavbar.vue";
import BaseDropdown from "@/components/base/BaseDropdown.vue";
import FixedAddIcon from "@/components/common/FixedAddIcon.vue";
import ComList from "@/components/list/ComList.vue";
import OrderListCard from "../components/cards/OrderListCard.vue";
export default {
  components: {
    BaseNavbar,
    BaseDropdown,
    FixedAddIcon,
    ComList,
    OrderListCard,
  },
  data() {
    return {
      title: "订单维护",
      optionsList: [
        {
          title: "全部",
          options: [
            { label: "全部", value: 0 },

            {
              label: "审核中",
              value: 1,
            },

            {
              label: "已通过",
              value: 2,
            },

            {
              label: "已驳回",
              value: 3,
            },
          ],
          value: 0,
        },
        {
          title: "全部",
          options: [
            { label: "全部", value: 0 },

            {
              label: "订单退款",
              value: 1,
            },

            {
              label: "结束订单",
              value: 2,
            },
          ],
          value: 0,
        },
        {
          title: "申请时间顺序",
          options: [
            {
              label: "申请时间顺序",
              value: 0,
            },

            {
              label: "申请时间倒叙",
              value: 1,
            },
          ],
          value: 0,
        },
      ],
    };
  },
  methods: {
    refresh(){
      this.refresh()
    },
    changeDropdown(item) {
      this.optionsList = item;
    },
    goMaintainApply() {
      uni.navigateTo({ url: "/pagesB/order/OrderMaintainApply" });
    },
  },
};
</script>

<style lang="scss">
page {
  background-color: $pageBgColor;
}
</style>
<style lang="scss" scoped>
</style>