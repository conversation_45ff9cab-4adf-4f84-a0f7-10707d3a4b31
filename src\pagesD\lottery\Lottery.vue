<template>
  <view class="box">
    <BaseNavbar :title="title" />
    <view class="lottery-title">
      <text>{{ vPointName }}名称：{{ hotel_num }}</text>
    </view>
    <view class="lottery-numpie">
      <view class="lot-num-pie">
        <text>设备数量</text>
        <text class="lot-num-color">{{ total }}</text>
      </view>
      <view class="lot-num-pie">
        <text>剩余时长</text>
        <text class="lot-num-color">00 ：{{ tims(times) }}</text>
      </view>
      <view class="lot-num-pie">
        <text>剩余次数</text>
        <text class="lot-num-color">{{ nums(countnum) }}</text>
      </view>
    </view>

    <view class="lottery-bakc">
      <image class="logoImg" :src="src" />
      <input class="lottory-dis-input" disabled="false" v-model="lottaryList[a].device_sn" focus />
    </view>
    <view>
      <text> 请输入随机任务次数 </text>
    </view>
    <view>
      <input class="lottory-num-input" @change="handleChange" v-model="countnum" :disabled="false" focus />
    </view>
    <view class="btn">
      <BaseButton type="primary" @onClick="ok()">开启随机任务</BaseButton>
      <view class="enter" :style="{ pointerEvents: enters ? 'none' : 'auto' }" @onClick="outLogin">
        <BaseButton type="eixt">停止随机任务</BaseButton>
      </view>
    </view>
    <!-- <view class="lottery-btn">
      <input class="lotteryInp" placeholder="0" />
      <view class="btn" @click="ok()">抽奖</view>
    </view> -->
    <BaseModal :show.sync="isShowDelModalckLotter" @confirm="confirmDelckLotter" content="确认要开启随机任务吗" title="温馨提示" />
  </view>
</template>

<script>
import BaseNavbar from "@/components/base/BaseNavbar.vue";
import BaseButton from "@/components/base/BaseButton.vue";
import BaseModal from "@/components/base/BaseModal.vue"
export default {
  // name:'lottery',
  components: { BaseNavbar, BaseButton, BaseModal },

  data() {
    return {
      // src: require("@/static/img/lottery.png"),
      src: 'data:image/png;base64,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',
      title: "随机任务",
      lottary: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10],
      lottaryList: [],
      //定义抽奖的索引
      a: 0,
      //声明计时器定时器
      lottaryTime: null,
      lottaryOut: null,
      //定义开关
      off: false,
      //定义次数

      countnum: 0,
      count: 0,
      //设置节流开关
      offbtn: true,

      //声明里面的定时器，
      yanshiOut: null,
      //
      fromData: "",
      dianwei: 0,
      hotel_id: 0,
      //总数
      total: 0,
      //计时器
      times: 59,
      // 倒计时
      timesTim: null,
      //场地放名称
      hotel_num: "",
      //停止任务。
      exit: false,
      //机器是否开机
      oks: false,
      //当前机器索引
      index: 0,
      //给停止加个开关
      enters: false,
      // 开启随机任务弹窗
      isShowDelModalckLotter: false,

    };
  },

  methods: {
    ok() {
      if (this.lottaryList.length < 1) {
        this.isShowErr("没有在线设备");
      } else {
        this.isShowDelModalckLotter = true
      }

    },
    /* 随机任务确认 */
    confirmDelckLotter() {
      if (this.offbtn && this.countnum !== 0) {
        this.a = 0;
        this.count = 0;
        this.enter();
      }
      if (this.offbtn) {
        this.exit = true;
      }
    },
    //抽奖逻辑
    enter() {
      this.offbtn = false;
      let random = Math.floor(Math.random() * this.lottaryList.length);
      this.lottaryTime = setInterval(() => {
        this.times = 59;
        if (this.off) {
          if (random == this.a) {
            // console.log(this.a, "随机数");
            clearInterval(this.lottaryTime);
            clearTimeout(this.lottaryOut);
            this.index = this.a;
            this.off = false;
            this.countnum--;
            //执行吐泡泡
            //机器是否开机

            this.enters = true;
            this.onStartDevice(2, 1);
            setTimeout(() => {
              this.enters = false;
            }, 6000);

            if (this.countnum > 0 && this.exit) {
              this.timesTim = setInterval(() => {
                if (this.times < 1) {
                  this.times = 0;
                } else {
                  this.times = this.times - 1;
                }
              }, 1000);
              this.yanshiOut = setTimeout(() => {
                //结束吐泡泡
                clearInterval(this.timesTim);

                //并重新开始计时
                this.times = 59;
                if (!this.exit) {
                  this.enter();
                }
              }, 65000);
            } else {
              // this.count = 0;
              this.offbtn = true;
              clearTimeout(this.yanshiOut);
            }
          } else {
            if (this.a < this.lottaryList.length - 1) {
              this.a++;
            } else {
              this.a = 0;
            }
          }
        } else {
          if (this.a < this.lottaryList.length - 1) {
            this.a++;
          } else {
            this.a = 0;
          }
        }
      }, 200);
      this.lottaryOut = setTimeout(() => {
        this.off = true;
      }, 3000);
    },
    //限制1-100
    handleChange() {
      this.count = 0;
      // 通过正则表达式判断输入是否符合要求
      if (/^(0|[1-9][0-9]*)$/.test(this.countnum)) {
        // 转换为整数并限制范围为1-100
        let num = parseInt(this.countnum);
        if (num < 1) {
          num = 0;
        } else if (num > 100) {
          num = 100;
        }
        this.countnum = num;
      } else {
        // 不符合要求则清空输入框
        this.countnum = 0;
      }
    },
    //数据的请求
    async getList() {
      try {
        this.isShowPopup = false;
        let data = {
          limit: 1,
          status: 1, // 是否在线
          dianweiid: this.dianwei,
          hotel_id: this.hotel_id,
          is_bind_hotel: true,
        };
        let res1 = await this.$u.api.getHotelMachines(data);
        this.total = res1.total;
        let data2 = {
          limit: this.total,
          status: 1, // 是否在线
          dianweiid: this.dianwei,
          hotel_id: this.hotel_id,
          is_bind_hotel: true,
        };
        let res2 = await this.$u.api.getHotelMachines(data2);
        this.lottaryList = res2.data || [];
      } catch (error) {
        console.error('请求失败:', error);
        // 处理请求失败的情况
      }
    },
    //停止任务

    outLogin() {
      this.exit = false;
      this.offbtn = true;
      this.count = 0;
      clearInterval(this.lottaryTime);
      if (this.oks) {
        this.onStartDevice(0, 0);
      }
      uni.navigateBack({
        delta: 1,
      });
    },
    //事件处理函数
    tims(a) {
      let b;
      if (a < 10) {
        b = "0" + a;
      } else {
        b = a;
      }
      return b;
    },
    //数字处理最小为0
    nums(a) {
      let b;
      if (a) {
        if (a < 0) {
          b = 0;
        } else {
          b = a;
        }
      } else {
        b = 0;
      }

      return b;
    },
    //出泡泡
    async onStartDevice(channel, time) {
      try{
        let data = {
        device_sn: this.lottaryList[this.index].device_sn, // 设备编号
        channel: channel, // 命令
        length_time: time, // 分钟
      };
      // console.log("启动设备结果参数 3 ：", data);
      await this.$u.api.startUM(data)
      this.isShowSuccess("操作成功");
        this.oks = true;
      }catch(error){
        this.isShowSuccess("操作失败");
      }
     
    },
  },

  onLoad(opt) {
    this.fromData = opt?.from;
    if (this.fromData == "place") {
      //点位来的
      this.dianwei = opt.dianwei;
      this.hotel_id = opt.hotel_id;
      this.hotel_num = opt.hotel_num;
      this.getList();
    }
  },
};
</script>

<style lang="scss">
page {
  background-color: $pageBgColor;
}
</style>
<style lang="scss" scoped>
* {
  margin: 0;
  padding: 0;
}

.box {
  // border: 1px solid #000;
  text-align: center;
  padding: 15rpx;
  // margin: 180rpx auto;
}

.lotter-box {
  width: 368rpx;
  border: 1rpx solid red;
  /* display: flex;
            justify-content: space-between;
            flex-wrap: wrap; */
  margin: 0 auto;
  overflow: hidden;
}

.red {
  color: red;
}

.pie {
  float: left;
  display: flex;
  justify-content: center;
  align-items: center;
  /* text-align: center; */
  width: 50rpx;
  height: 50rpx;
  border: 1rpx solid #000;
  margin: 20rpx;
}

.btn {
  margin: auto;
  width: 560rpx;

  padding: 0 30rpx;
  margin-top: 30rpx;

  :nth-child(1) {
    margin-bottom: 15rpx;
  }

  ::v-deep .u-btn {
    height: 72rpx !important;
  }
}

.lotteryInp {
  // height: 10rpx;
  width: 80rpx;
  height: 70rpx;
  // padding: 5rpx 20rpx;
  border: 1px solid #000;
  text-align: center;
}

.lottery-btn {
  border: 1px solid #000;
  display: flex;
  align-items: center;
  width: 200rpx;
  margin: 20rpx auto;
}

.lottery-title {
  color: #555;
  overflow-wrap: normal;
}

.lottery-numpie {
  padding: 15rpx 0;
  width: 560rpx;
  background-color: white;
  box-shadow: 4rpx 4rpx 15rpx rgb(197, 196, 196);
  margin: 20rpx auto;
  // border: 1px solid #000;
  display: flex;
  justify-content: center;
  align-items: center;

  .lot-num-pie {
    // border: 1px solid #000;
    display: flex;
    justify-content: center;
    flex-direction: column;
    align-items: center;

    text {
      font-weight: bold;
      padding: 5rpx 35rpx;
    }

    :nth-child(1) {
      font-size: 30rpx;
    }
  }

  .lot-num-pie:nth-child(2) {
    border-left: 1rpx solid #d1cfcf;
    border-right: 1rpx solid #d1cfcf;
  }

  .lot-num-color {
    font-size: 35rpx;
    color: #0eade2;
  }
}

.lottery-bakc {
  margin: auto;
  // border-radius: 50%;
  height: 540rpx;
  // border: 1px solid #000;
  width: 600rpx;
  // border: 1px solid #000;
  position: relative;

  image {
    width: 100%;
    height: 100%;
  }

  .lottory-dis-input {
    top: 200rpx;
    left: 128rpx;
    position: absolute;
    border-radius: 15rpx;
    background-color: #dbdbdb;
    box-shadow: 0rpx 0rpx 15rpx rgb(179, 177, 177) inset;
    // border: 1px solid #000;
    height: 65rpx;
    width: 350rpx;
    margin: 20rpx auto;
    text-align: center;
    font-size: 35rpx;
    // color: orangered;
    font-weight: bold;
  }
}

.lottory-num-input {
  border-radius: 15rpx;
  background-color: #dbdbdb;
  // border: 1px solid #000;
  height: 65rpx;
  width: 250rpx;
  margin: 20rpx auto;
  text-align: center;
  font-size: 40rpx;
  color: orangered;
  font-weight: bold;
}
</style>