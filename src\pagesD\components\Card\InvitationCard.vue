<template>
  <view class="invitation_card" @click="goDetails">
    <image :src="listBac" alt="" />
    <view class="invitation-text">
      <view class="invitation-text-left">
        <view>用户名称</view>
        <!-- <view>设备数</view> -->
        <view>创建时间</view>
      </view>
      <view class="invitation-text-right">
        <view>{{ info.user_name }} {{ info.user_nickname?' ('+info.user_nickname+')':'' }}</view>
        <!-- <view>{{info.num}}</view> -->
        <view>{{ getTime }}</view>
      </view>
    </view>

  </view>
</template>

<script>
import { timestampToTime } from "@/common/tools/utils.js";
export default {
  name: "InvitationCard",
  props: {
    info: { type: Object, default: {} },
  },
  data() {
    return {
      listBac: require("@/pagesD/static/img/InvitationListBac.png"),
    }
  },
  computed: {
    getTime() {
      return timestampToTime(this.info.create_time * 1000, true);
    },
  },
  methods: {
    goDetails() {
      uni.navigateTo({
        url: "/pagesC/webView/WebView?url=" + encodeURIComponent(this.info.url),
      });
    },

  },

};
</script>

<style lang="scss" scoped>
.invitation_card {
  // width: 330rpx;
  overflow: hidden;
  position: relative;
  height: 170rpx;
  border-radius: 15rpx;

  image {
    height: 110%;
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    z-index: -9;
  }

  .invitation-text {
    height: 100%;
    //  border: 1px solid #000;
    display: flex;
    align-items: center;
    text-align: left;
    padding: 10rpx 20rpx;

    .invitation-text-left {
      color: white;

      >view {
        margin: 10rpx;
      }
    }

    .invitation-text-right {
      flex: 1;
      // width: 450rpx;
      margin: 0 20rpx;
      background-color: white;
      padding: 0 15rpx;

      >view {
        margin: 10rpx;
      }
    }
  }
}</style>