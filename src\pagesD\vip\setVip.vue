<template>
    <view>
        <BaseNavbar :title="title" />
        <view class="list">
            <view class="list-li" v-for="(item, i) in listData" :key="i">
                <view class="list_item">
                    <text class="text"> 充值 {{ item.recharge_money }} 赠送 {{ item.gift_money }}</text>
                    <view class="text">
                        <text class="margin_right right" @click="editClick(item)">
                            编辑
                        </text>
                        <text class="red" @click="deletClick(item.id)">
                            删除
                        </text>
                    </view>
                </view>
            </view>
            <view class="list-li" @click="addClick">
                <image class="big-img" src="@/pagesD/static/img/add.png" alt="" />
                <text class="text">添加套餐</text>
            </view>
        </view>
    </view>
</template>

<script>
import BaseNavbar from "@/components/base/BaseNavbar.vue";
import BaseIcon from "@/components/base/BaseIcon.vue";
import BaseButton from "@/components/base/BaseButton.vue";
import myPull from '@/mixins/myPull.js'
export default {
    components: { BaseNavbar, BaseIcon, BaseButton },
    data() {
        return {
            title: "切换账号",
            orderTotal: 0

        };
    },
    mixins: [myPull()],
    computed: {
        logoImg() {
            return this.vSiteConfig?.site_info?.site_logo || "";

        },
    },
    created() {
    },
    methods: {
        async getList(page, done) {
            try {
                let data = {
                    page: page,
                    limit: 10
                }
                let res = await this.$u.api.getPackageList(data)
                done(res.data)
            } catch (error) {
                console.log(error)
            }

        },
        addClick() {
            console.log('点击添加套餐')
            uni.navigateTo({ url: "/pagesD/vip/addVip?from=add" });
        },
        editClick(item) {
            uni.navigateTo({ url: `/pagesD/vip/addVip?from=edit&item=${encodeURIComponent(JSON.stringify(item))}` });
        },
        async deletClick(id) {
            try {
                let data = {
                    id: id
                }
                await this.$u.api.delPackage(data)
                this.isShowSuccess("删除成功");
                this.refresh()
            } catch (error) {
                console.log(error)
            }

        },
        ondeleUser() {
            uni.navigateTo({ url: "/pagesD/user/UserList" });
        },
    },
    onLoad() {
        this.refresh()
    },
    onShow() {
        /*  #ifndef H5 */
        let pages = getCurrentPages()
        let currPage = pages[pages.length - 1] // 当前页
        if (currPage.data.isDoRefresh == true) {
            // 是否刷新
            currPage.data.isDoRefresh = false
            this.refresh()
        }
        /*#endif */
        /*  #ifdef H5 */
        if (this.vCurrPage.isDoRefresh == true) {
            // 是否刷新
            this.vCurrPage.isDoRefresh = false
            this.refresh()
        }
        /*#endif */
    },

};
</script>

<style lang="scss">
page {
    background-color: $pageBgColor;
}
</style>

<style lang="scss" scoped>
.title {
    text-align: center;
    // border: 1px solid #000;
    margin: 40rpx 0;
    font-size: 50rpx;
    color: #555555;
}

.margin_right {
    margin-right: 20rpx;
}

.list {

    // border: 1px solid #000;
    .list-li {
        height: 150rpx;
        margin: 25rpx 35rpx;
        background-color: white;
        border-radius: 15rpx;
        display: flex;
        align-items: center;
        position: relative;

        // border: 1px solid #000;
        image {
            margin: 0 20rpx;
            vertical-align: middle;
            width: 90rpx;
            height: 90rpx;
        }

        .big-img {
            width: 60rpx;
            height: 60rpx;
        }

        .text {
            font-size: 35rpx;
            font-weight: bold;

            vertical-align: middle;
        }

        .right {
            color: #206cc5;
        }

        .red {
            color: orangered;
        }

        .list-pie {
            position: absolute;
            top: 0;
            right: 0;
            font-size: 24rpx;
            padding: 5rpx 20rpx;
            border-top-right-radius: 10rpx;
            border-bottom-left-radius: 10rpx;
            color: white;
            background: #206cc5;
            background-image: linear-gradient(90deg, #7fbad1, #4da2e7, #0473f1);
        }
    }

    .list_item {
        padding: 0 20rpx;
        width: 100%;
        display: flex;
        justify-content: space-between;
    }
}

.buttom {
    margin-top: 50rpx;
    font-size: 30rpx;
    text-align: center;
    color: #206cc5;
}
</style>