<template>
  <view class="screener">
    <view class="screener_list">
      <view class="screener_list_item">
        <view>{{ vPointName }}名称</view>
        <view
          ><BaseInput v-model="placeNmae" placeholder="请输入名称"
        /></view>
      </view>
      <view class="screener_list_item">
        <view>设备编号</view>
        <view
          ><BaseInput v-model="deviceSn" placeholder="请输入设备编号"
        /></view>
      </view>
      <view class="screener_list_item">
        <view>订单/支付单号</view>
        <view
          ><BaseInput v-model="orderSn" placeholder="请输入订单编号/支付单号"
        /></view>
      </view>
     
      <view class="screener_list_item">
        <view>筛选时间</view>
        <view>
          <TimeSelect v-model="startTime" placeholder="请选择开始时间"
        /></view>
      </view>
      <view class="screener_list_item">
        <view class="time_end_title">筛选时间</view>
        <view
          ><TimeSelect v-model="endTime" placeholder="请选择结束时间"
        /></view>
      </view>
    </view>
    <view class="btn">
      <BaseButton :width="330" type="default" @onClick="resetData"
        >重置</BaseButton
      >
      <BaseButton :width="330" type="primary" @onClick="confirm"
        >确认</BaseButton
      >
    </view>
  </view>
</template>

<script>
import BaseInput from "@/components/base/BaseInput.vue";
import TimeSelect from "@/components/common/TimeSelect.vue";
import BaseButton from "@/components/base/BaseButton.vue";
export default {
  name: "OrderScreener", //订单筛选
  components: { BaseInput, TimeSelect, BaseButton },
  data() {
    return {
      placeNmae: "",
      deviceSn: "",
      orderSn: "",
      startTime: "",
      endTime: "",
    };
  },
  methods: {
    resetData() {
      this.placeNmae = "";
      this.deviceSn = "";
      this.orderSn = "";
      this.startTime = "";
      this.endTime = "";
    },
    confirm() {
      let data = {
        dianwei: this.placeNmae,
        device_sn: this.deviceSn,
        order_sn: this.orderSn,
        start_time: this.startTime,
        end_time: this.endTime,
      };
      this.$emit("confirm", data);
    },
  },
};
</script>

<style lang="scss" scoped>
.screener {
  padding: 50rpx 30rpx 30rpx;
  &_list {
    &_item {
      display: flex;
      align-items: center;
      > view {
        margin-bottom: 30rpx;
        &:first-child {
          margin-right: 20rpx;
          white-space: nowrap;
          font-size: $font-size-base;
          font-family: Source Han Sans CN;
          font-weight: bold;
          color: $textBlack;
        }
        &:last-child {
          width: 100%;
        }
      }
      .time_end {
        opacity: 0;
      }
    }
  }
  .btn {
    display: flex;
    justify-content: space-between;
  }
}
</style>