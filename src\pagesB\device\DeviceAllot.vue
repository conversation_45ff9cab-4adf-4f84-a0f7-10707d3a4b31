<template>
  <view>
    <BaseNavbar :title="title" />
    <view class="bindHotelCard">
      <view class="deviceCode">
        <view>设备编号</view>
        <view>{{ deviceInfo.device_sn ? deviceInfo.device_sn : '' }}</view>
      </view>
      <view class="hotelName" @click="selectUser">
        <view>拥有者</view>
        <view>{{ userLogin ? userLogin : '-选择拥有者-' }}</view>
      </view>
    </view>
    <BaseButton class="bind" width="630" @onClick="allotUser">分 配</BaseButton>
  </view>
</template>

<script>
import BaseNavbar from '@/components/base/BaseNavbar.vue'
import BaseButton from '../../components/base/BaseButton.vue'
export default {
  components: { BaseNavbar, BaseButton },
  data() {
    return {
      title: '设备分配',
      device_sn: '', //设备编号
      mid: '', //设备id
      isFromDevice: false, //设备来的
      deviceInfo: {}, // 设备信息
      isShowDetail: false,
      room_num: '',
      userLogin: '',
      user_id: '',
      isFromIndexScan: false, //首页扫码来的
    }
  },
  methods: {
    async isAllotMachine() {
      //判断是否是获取屏幕详细信息
      let data = {
        device_sn: this.device_sn,
        mid: this.mid,
      }
      let rtnData = await this.$u.api.isAllotMachine(data)
      this.deviceInfo = rtnData
      if (this.deviceInfo) {
        this.device_sn = this.deviceInfo.device_sn
        if (this.deviceInfo.user) {
          this.userLogin = this.deviceInfo.user.user_login
          this.user_id = this.deviceInfo.user.id
        }

        if (!this.deviceInfo.user) {
          // 未绑定点位
          let select_user = uni.getStorageSync('select_user')
          if (select_user) {
            this.userLogin = select_user.userLogin
            this.user_id = select_user.user_id
          }
        }
      }
    },
    allotUser() {
      // 分配用户
      let data = {
        allot_user_id: this.user_id,
        device_sn: this.device_sn,
        mid: this.mid,
      }
      this.$u.api
        .allotMachine(data)
        .then((res) => {
          this.isShowSuccess('分配成功', 1, () => {}, true)
        })
        .catch((err) => {
          console.log('错误信息', err)
        })
    },
    selectUser() {
      uni.navigateTo({
        url: `/pagesB/user/SelectUser?from=home_device_allot`,
      })
    },
  },
  onLoad(opt) {
    if (opt?.from) {
      if (opt.from == 'device') {
        // 设备绑定来的
        this.isFromDevice = true
      } else if (opt.from == 'index_scan') {
        // 首页扫码来的
        this.isFromIndexScan = true
      }
    }
    console.log('参数', opt)
    this.device_sn = opt?.device_sn
    this.mid = opt?.mid
    this.isAllotMachine()
  },
  onShow(e) {
    /*  #ifndef H5 */
    let pages = getCurrentPages()
    let currPage = pages[pages.length - 1] // 当前页
    if (currPage.data.item) {
      // 有值
      // 修改listData中值
      this.checkUser = currPage.data.item
      this.userLogin = this.checkUser.user_login
      this.user_id = this.checkUser.id
      // 缓存数据
      uni.setStorage({
        key: 'select_user',
        data: {
          userLogin: this.userLogin,
          user_id: this.user_id,
        },
      })
    }
    /*#endif */
    /*  #ifdef H5 */
    if (this.vCurrPage.item) {
      // 有值
      // 修改listData中值
      this.checkUser = this.vCurrPage.item;
      this.userLogin = this.checkUser.user_login;
      this.user_id = this.checkUser.id;
      // 缓存数据
      uni.setStorage({
        key: "select_user",
        data: {
          userLogin: this.userLogin,
          user_id: this.user_id,
        },
      });
    }
    /*#endif */
    // if (currPage.data.isDoRefresh == true) {
    //   // 是否刷新
    //   currPage.data.isDoRefresh = false;
    //   this.getUMHotel();
    // }
  },
}
</script>

<style lang="scss">
page {
  background-color: $pageBgColor;
}
</style>
<style lang="scss" scoped>
.bindHotelCard {
  margin-top: 20rpx;
  height: 300rpx;
  width: 100%;
  background: white;
  font-size: 26rpx;
  color: $textBlack;
}

.deviceCode,
.hotelName {
  height: 100rpx;
  display: flex;
  flex-direction: row;
  align-content: center;
  align-items: center;
  margin-left: 35rpx;
  border-bottom: 1rpx solid #e5e5e5;
}

.roomNumber {
  height: 100rpx;
  display: flex;
  flex-direction: row;
  align-content: center;
  align-items: center;
  margin-left: 35rpx;
  border-bottom: 1rpx solid #e5e5e5;
  border-bottom: 0;
}

.deviceCode view:first-child,
.hotelName view:first-child,
.roomNumber view:first-child {
  margin-right: 30rpx;
}

.bind {
  margin-top: 50rpx;
}
.addBtn {
  margin-top: 50rpx;
}
</style>
