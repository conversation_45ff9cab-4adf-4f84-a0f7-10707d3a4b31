<template>
  <view>
    <BaseNavbar :title="title" />
    <view class="content">
      <view class="title">选择星期</view>

      <!-- 筛选每天 -->
      <view class="line-flex">

        <view class="card">
          <view class="chunk" @click.capture="changeCheckAll">

          </view>
          <view class="card_left">
            <BaseCheck :checked.sync="isCheckAll" title="每天" />
            <!-- <text class="title">每天</text> -->
          </view>
        </view>
        <view v-for="item in week" :key="item.id">
          <weekListCard :info="item" @onCheck="selectItem(item)"></weekListCard>
        </view>
      </view>
      <view>
        <view class="title">选择开始时间</view>
        <TimeSelectHour v-model="begin_time" placeholder="请选择开始时间" />
      </view>
      <view>
        <view class="title">选择结束时间</view>
        <TimeSelectHour v-model="end_time" placeholder="请选择结束时间" />
      </view>
      <view class="btn">
        <view>
          <BaseButton type="primary" @onClick="confirmAdd">确 认</BaseButton>
        </view>

      </view>
    </view>
  </view>
</template>

<script>
import BaseButton from "@/components/base/BaseButton.vue";
import BaseCheck from "@/components/base/BaseCheck.vue";
import BaseNavbar from "@/components/base/BaseNavbar.vue";
import TimeSelectHour from "@/components/common/TimeSelectHour.vue";
import weekListCard from "../components/Card/weekListCard.vue"
export default {
  components: {
    BaseNavbar,
    BaseButton,
    BaseCheck,
    TimeSelectHour,

    weekListCard
  },
  data() {
    return {
      title: "添加定时出泡泡任务",
      isFromEdit: false,
      isFromAdd: false,
      roleList: [],
      roleValue: {},
      checked: false,
      radioIndex: 0,
      hotel_id: '',
      radioList: [
        {
          title: "是",
          name: "0",
          disabled: false,
          selectIndex: 1,
        },
        { title: "否", name: "1", disabled: false, selectIndex: 0 },
      ],
      user_login: "",
      user_pass: "",
      user_nickname: "",
      mobile: "",
      id: "",
      begin_time: '',
      end_time: '',
      show: false,
      week: [
        {
          name: '周一',
          id: 1,
        },
        {
          name: '周二',
          id: 2
        }
        ,
        {
          name: '周三',
          id: 3
        },
        {
          name: '周四',
          id: 4
        },
        {
          name: '周五',
          id: 5
        },
        {
          name: '周六',
          id: 6
        }
        ,
        {
          name: '周日',
          id: 7
        }

      ],
      isCheckAll: false,
      selectInfoList: [],
      off: false,//节流开关
    };
  },
  methods: {
    roleConfirm(e) {
      this.roleValue = this.roleList[e[0].value];
    },
    async confirmAdd() {
      try {
        let day_list = []
        for (let i = 0; i < this.selectInfoList.length; i++) {
          day_list.push(this.selectInfoList[i].id)
        }
        let hour1 = this.begin_time.split(':')[0] * 1
        let time1 = this.begin_time.split(':')[1] * 1
        let hour2 = this.end_time.split(':')[0] * 1
        let time2 = this.end_time.split(':')[1] * 1
        if (hour2 < hour1 || hour2 == hour1 && time2 <= time1) {
          return this.isShowErr("结束时间必须大于开始时间");
        }
        let data = {
          hotel_id: this.hotel_id,
          begin_time: this.begin_time,
          end_time: this.end_time,
          day_list
        }
        let rtnData = null;
        if (this.isFromAdd) {
          rtnData = await this.$u.api.setAddRand(data);
        } else if (this.isFromEdit) {
          data['id'] = this.id
          rtnData = await this.$u.api.setUpdateRand(data);
        }
        this.isShowSuccess("操作成功", 1, () => { }, true);
      }catch(error){
        this.isShowSuccess("操作失败");
      }
    
    },
    //获取角色列表
    async getRoleList() {
      try {
        let data = {
          id: 0,
        };
        let res = await this.$u.api.getRoleAllList(data)
        if (res.length > 0) {
          let i = 0;
          res.map((item) => {
            if (item.id > this.vUserInfo.role_id) {
              i++;
              return this.roleList.push({
                value: i - 1,
                label: item.label,
                roleId: item.id,
              });
            }
          });
          this.roleValue = this.roleList[0];
        }
      } catch (error) {
        console.log(error)
      }

    },
    //获取用户详情
    async getUserDetail(id) {
      try {
        let data = {
          id,
        };
        let res = await this.$u.api.getUserDetail(data)
        this.user_login = res.user_login;
        this.mobile = res.mobile;
        this.user_nickname = res.user_nickname;
        //radioIndex是选中项所在位置  item.selectIndex才是判断是否可添加商品
        this.radioList.forEach((item) => {
          if (item.selectIndex == res.is_allow_add_goods) {
            this.radioIndex = item.name;
          }
        });
      } catch (error) {
        console.log(error)
      }

    },
    selectItem(item) {

      if (typeof item.isCheck == "undefined") {
        this.$set(item, "isCheck", true);

      } else {
        item.isCheck = !item.isCheck;
      }

      this.selectInfoList = this.week?.filter((item) => item.isCheck) || [];
      if (this.selectInfoList.length == this.week.length) {
        this.isCheckAll = true
      } else {
        this.isCheckAll = false
      }
    },
    changeCheckAll() {
      // if(this.off){
      //  return this.isShowErr("操作过于平反");
      // }


      // this.off=true;
      // setTimeout(() => {
      //   this.off=false
      // }, 2000);
      // this.isCheckAll = this.isCheckAll;
      if (this.isCheckAll) {
        this.isCheckAll = false
      } else {
        this.isCheckAll = true
      }
      // console.log('打印', this.isCheckAll)
      this.week.forEach((item) => (item.isCheck = this.isCheckAll));
      if (this.isCheckAll) {
        this.selectInfoList = this.week;
      } else {
        this.selectInfoList = [];
      }
    },
  },

  onLoad(opt) {
    this.roleValue = this.roleList[0] || {}; //角色默认选择第一个

    if (opt?.from) {
      this.fromData = opt.from;
      // console.log('进来的数据', this.fromData, opt.data)

      if (this.fromData == "edit") {
        let data = JSON.parse(decodeURIComponent(opt.data));
        this.id = data.id
        this.hotel_id = data.hotel_id,
          this.begin_time = data.begin_time,
          this.end_time = data.end_time
        this.title = "编辑定时出泡泡任务";
        this.selectInfoList = []

        if (data.day_list.length == this.week.length) {
          this.changeCheckAll()


        } else {
          for (let i = 0; i < data.day_list.length; i++) {
            for (let j = 0; j < this.week.length; j++) {
              if (this.week[j].id == data.day_list[i]) {
                this.selectItem(this.week[j])
              }
            }
          }
        }
        // console.log('进来的数据', this.selectInfoList)
        this.isFromEdit = true;

        // this.getUserDetail(opt.id);
      } else if ((this.fromData = "add")) {
        this.hotel_id = opt.hotel_id
        this.title = "新增定时出泡泡任务";
        this.isFromAdd = true;
        // this.getRoleList();
      }
    }
  },
};
</script>

<style lang="scss" scoped>
.content {
  padding: 30rpx;

  .line-flex {
    display: flex;
    flex-wrap: wrap;

    justify-content: space-between;
    margin-top: 30rpx;

    >view {
      margin-right: 10rpx;
    }

  }

  >view {
    margin-bottom: 50rpx;
  }

  .title {
    color: $textBlack;
    // font-size: $font-size-middle;
    font-weight: bold;
    margin-bottom: 20rpx;
    font-size: 30rpx;
  }

  .title_h1 {
    // font-size: 35rpx;
    margin-bottom: 0;
  }

  .btn {
    margin-top: 100rpx;

    &_del {
      margin-top: 40rpx;
    }
  }

  .is-add-goods {
    margin-left: 20%;
  }

  .card_left {

    // position: absolute;
    // z-index: -11;
  }

  .card {
    // border: 1px solid #000;
    // background-color: ;
    //  background-color: red;
    position: relative;
    // display: flex;

    align-items: center;
    padding: 10rpx;
    width: 150rpx;

    .chunk {
      position: absolute;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
      // background-color: red;
      // border: 1px solid #000;
      z-index: 999;
    }

    &_right {
      flex: 1;
      z-index: 999;

      >view {
        display: flex;
        justify-content: space-between;
        font-size: $font-size-base;
        color: $textGray;
        margin-bottom: 22rpx;


        :last-child {
          margin-bottom: 0;
        }

        .title {
          text-align: right;
          width: 136rpx;
          display: inline-block;
        }

        .themeColor {
          color: $themeComColor;
        }

        .textBlack {
          color: $textBlack;
        }
      }

      .remain_money {
        width: 190rpx;
        display: flex;
        justify-content: space-between;
      }
    }
  }
}
</style>