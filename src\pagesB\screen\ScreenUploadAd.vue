<template>
  <view>
    <BaseNavbar title="上传屏幕广告" />
    <view class="container">
      <view class="container-box">
        <view class="container-box-title">屏幕编号</view>
        <view class="container-box-txt">
          <BaseInput :disabled="true" v-model="screen_sn" />
        </view>
      </view>
      <view class="container-box">
        <view class="container-box-title">设备编号</view>
        <view class="container-box-txt">
          <BaseInput :disabled="true" v-model="device_sn" />
        </view>
      </view>
      <view class="container-box">
        <view class="container-box-title">跑马灯文字</view>
        <view class="container-box-txt">
          <BaseInput placeholder="请输入跑马灯文字" v-model="tex" />
        </view>
      </view>
      <view class="container-box">
        <view class="container-box-title">A区图片</view>
        <view class="container-box-txt upload">
          <BaseUpload
            v-for="item in 5"
            :key="item"
            :index="item"
            width="200"
            :maxCount="1"
            :auto="true"
            :maxSize="1"
            :fileList="defaultFileListA[item]"
            @onUpload="onUploadA"
          />
        </view>
      </view>
      <view class="container-box">
        <view class="container-box-title">B区图片</view>
        <view class="container-box-txt">
          <BaseUpload
            width="200"
            :maxCount="1"
            :maxSize="1"
            :auto="true"
            :fileList="defaultFileListB"
            @onUpload="onUploadB"
          />
        </view>
      </view>
    </view>
    <view class="tips">
      <view class="tips-title">注意事项：</view>
      <view class="tips-box">
        <view class="tips-box-item">1.请上传小于1M的图片</view>
        <view class="tips-box-item">
          2.如更改广告后屏幕无变化,可尝试重启屏幕和重新上传广告
        </view>
      </view>
    </view>
    <view
      class="fixed-btn"
      :style="{ paddingBottom: 20 + vIphoneXBottomHeight + 'rpx' }"
    >
      <view class="btn">
        <BaseButton @onClick="save">保 存</BaseButton>
      </view>
    </view>
    <SafeBlock :height="120" />
  </view>
</template>

<script>
import BaseNavbar from "@/components/base/BaseNavbar.vue";
import BaseInput from "../../components/base/BaseInput.vue";
import BaseUpload from "../../components/base/BaseUpload.vue";
import SafeBlock from "../../components/common/SafeBlock.vue";
import BaseButton from "../../components/base/BaseButton.vue";
export default {
  components: { BaseNavbar, BaseInput, BaseUpload, SafeBlock, BaseButton },
  data() {
    return {
      mid: "",
      device_sn: "",
      screen_sn: "",
      defaultFileListA: [
        [],
        [],
        [],
        [],
        [],
        // {
        //   url: "https://hd.handaiwulian.com/upload/20220325/8bf8cdc64342b21912307e110f25afce.png",
        // },
      ],
      defaultFileListB: [],
      tex: "", //跑马灯文字
      uploadImgListA: [
        { gid: 0, img_url: "" },
        { gid: 1, img_url: "" },
        { gid: 2, img_url: "" },
        { gid: 3, img_url: "" },
        { gid: 4, img_url: "" },
      ],
      uploadImgListB: [{ gid: 0, img_url: "" }],
    };
  },
  methods: {
    onUploadB(e) {
      this.uploadImgListB.img_url = e?.[0] || "";
    },
    onUploadA(e, i) {
      this.uploadImgListA[i].img_url = e?.[0] || "";
    },
    //获取当前绑定广告信息
    getScreenAdv(mid) {
      let data = {
        mid,
      };
      this.$u.api.getScreenAdv(data).then((res) => {
        this.tex = res.data?.tex?.content_text || "";
        //规整数据A
        if (res.data?.screen) {
          res.data?.screen?.forEach((item, index) => {
            if (item?.img_url) {
              this.defaultFileListA[index].push({ url: item.img_url });
              this.uploadImgListA.splice(index, 1, {
                gid: item.gid,
                img_url: item.img_url,
              });
            }
          });
        }

        //规整数据B
        if (res.data?.bottom_index) {
          this.uploadImageListB = [
            {
              gid: res.data.bottom_index?.gid || 0,
              img_url: res.data.bottom_index?.img_url || "",
            },
          ];

          if (res.data.bottom_index?.img_url)
            this.defaultFileListB = [{ url: this.uploadImageListB[0].img_url }];
        }

        // console.log("🚀 ~ this.uploadImgListA ", this.uploadImgListA);
        // console.log("🚀 ~ this.uploadImgListB ", this.uploadImgListB);
        // console.log("🚀 ~ this.defaultFileListA ", this.defaultFileListA);
        // console.log("🚀 ~ this.defaultFileListB ", this.defaultFileListB);
      });
    },
    //规整提交参数
    getField(type, img_url, content_text = "", time = "") {
      let typeName = "";
      switch (type) {
        case 4:
          typeName = "A区";
          break;
        case 5:
          typeName = "B区";
          break;
        case 12:
          typeName = "跑马灯文字";
          break;
        case 13:
          typeName = "屏幕二维码";
          break;
      }
      return {
        type,
        mid: this.mid,
        ad_desc: "屏幕编号：" + this.screen_sn + ",广告区域：" + typeName,
        img_href: "",
        img_url,
        content_text,
        time, //展示时长
      };
    },
    //保存广告信息
    save() {
      let list = [];
      //A区数据规整
      this.uploadImgListA?.forEach((item) =>
        list.push(this.getField(4, item.img_url))
      );
      //B区数据规整
      this.uploadImgListB?.forEach((item) =>
        list.push(this.getField(5, item.img_url))
      );
      //跑马灯文字规整
      list.push(this.getField(12, "", this.tex));
      let data = {
        list,
        screen_sn: this.screen_sn,
      };
      this.$u.api.sendScreenImg(data).then((res) => {
        this.isShowSuccess("屏幕广告推送成功", 1);
      });
    },
  },
  onLoad(opt) {
    this.mid = opt?.mid || "";
    this.device_sn = opt?.device_sn || "";
    this.screen_sn = opt?.screen_sn || "";
    this.getScreenAdv(this.mid);
  },
};
</script>

<style lang="scss" scoped>
.container {
  padding: 20rpx;
  &-box {
    margin-bottom: 30rpx;
    &-title {
      color: $textBlack;
      font-size: $font-size-middle;
      font-weight: 700;
      margin-bottom: 20rpx;
    }
    &-txt {
    }
    .upload {
      display: flex;
      flex-wrap: wrap;
    }
  }
}
.tips {
  padding: 20rpx;
}
.fixed-btn {
  padding: 20rpx;
  position: fixed;
  background-color: #fff;
  bottom: 0;
  left: 0;
  right: 0;
}
</style>