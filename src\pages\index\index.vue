<template>
  <view class="content">
    <BaseNavbar color="#fff" :isShowBack="false" :title="vSiteConfig.site_info.site_name || ''" v-if="backgroundStyle()"
      :background="backgroundStyle()" :isLeft="false" />
    <view class="search-top">
      <BaseSearch width="80" placeholder="请输入设备编号" @onClickIcon="onClickIcon" @search="searchDevice"
        :isShowSerch="false" />
      <view class="search-right-icon">
        <button open-type="contact" class="button_bt serverMenu flexColumnHorzCenter">
          <image class="imgServer" src="@/static/img/icon/service-icon.png"></image>
        </button>
        <view class="notice" @click="goNotice">
          <image class="notice-icon" src="@/static/img/icon/notice-icon.png" />
          <view v-if="vNoticeNum > 0" class="circle"></view>
        </view>
      </view>
    </view>

    <!-- 主体内容区域start -->
    <view class="myHone u-skeleton">
      <view class="myHomes">
        <view class="tabsBox flexRowBetween">
          <view class="tabs">
            <BaseTabs height="44" :current="curTabIndex" :list="tabList" :isShowBar="false" @change="tabChange" />
          </view>
          <view class="showMoneytIcon flexRowAllCenter">
            <block>
              <BaseIcon v-if="isShowMoney" name="eye" size="50" @onClick="onIsShowMoney" />
              <view class="eyefillBox flexRowAllCenter" v-else>
                <img class="eyefill" src="@/static/img/icon/eyefill-icon.png" @click="onIsShowMoney" />
              </view>
            </block>

            <BaseIcon class="infoCircle" name="info-circle" @onClick="isShowClick" />
          </view>
        </view>
        <view class="moneyPanel" @click="go()">
          <image class="panelImg" src="@/static/img/moneyBg.png" />
          <view class="turnover flexColumnAllBetween">
            <view class="title">收 益</view>
            <view class="moneyBox" v-if="isShowMoney">
              <text>￥</text>
              <BaseCountTo :value="salesInfo[curTabIndex].amount[0]" :size="44" />
              <text>.</text>
              <text v-if="salesInfo[curTabIndex].amount[1] < 10"
                style="font-size: 15px; font-weight: bold; color: #fff;">
                0
              </text>
              <BaseCountTo :value="salesInfo[curTabIndex].amount[1]" />
            </view>
            <view v-else class="moneyBox">*****</view>
          </view>
          <view class="dealNum flexColumnAllBetween">
            <view class="title">成交数</view>
            <view class="num">
              <BaseCountTo v-if="isShowMoney" :value="salesInfo[curTabIndex].orderNum" />
              <view v-else>*****</view>
            </view>
          </view>
        </view>
        <view class="updata">
          <BaseUpdata title="收益" @onClick="updataClick" :refresh="manyUpdata"></BaseUpdata>
        </view>
        <view class="myDevice">
          <view class="flex">
            <view class="title">
              设备概况
            </view>
            <view class="title-right" v-if="vSiteConfig && vSiteConfig.site_info && vSiteConfig.site_info.manage_horse">
              <u-notice-bar :list="[vSiteConfig.site_info.manage_horse]" fontSize="25"></u-notice-bar>
            </view>
          </view>

          <view class="panel">
            <image class="img" src="@/static/img/myDeviceBg.png" />
            <view class="box flexRowAllCenter">
              <view class="flexColumnAllCenter" v-for="(item, i) in machineStatus" :key="i" @click="device(item, i)">
                <!-- <BaseCountTo :value="item.number" :size="40" color="#333" /> -->
                <view class="font-position">
                  <view class="font-witer">
                    {{ item.number }}
                  </view>
                  <view class="type">{{ item.name }}</view>
                </view>
              </view>
            </view>
          </view>
        </view>
        <view class="updata">
          <BaseUpdata title="设备概况" @onClick="undataTimes" :refresh="devUpdata"></BaseUpdata>
          <!-- <view class="fiex" @click="navigatorUrl()">
            跳转到关注公众号页面
          </view> -->
        </view>
        <view class="funcs">
          <uni-swiper-dot :info="funcsListSlice" :current="current" :dotsStyles="{
      backgroundColor: 'rgba(83, 200, 249, 0.3)',
      selectedBackgroundColor: 'rgba(83, 200, 249, 0.9)',
      bottom: '0',
    }">
            <swiper class="swiper-box" @change="change">
              <swiper-item v-for="(item, index) in funcsListSlice" :key="index">
                <BaseGrid :gridList="item" @onClick="navigator" width="90rpx" />
              </swiper-item>
            </swiper>
          </uni-swiper-dot>
        </view>
        <!-- <view class="cloudCollege" v-if="articleList.length > 0">
          <view class="top flexRowBetween">
            <view class="title">
              {{ vSiteConfig.site_info.site_name }}云学院
            </view>
            <view class="more" @click="goMore">
              更多
              <BaseIcon :size="30" name="arrow-right" />
            </view>
          </view>
          <view class="cloudList">
            <CloudVideoCard v-for="(item, index) in articleList" :key="index" :info="item" />
          </view>
        </view> -->
      </view>
    </view>
    <!-- 主体内容区域end -->
    <BaseModal :show.sync="isShowInfoCircle" :isShowCancel="isIndex == 1" @cancel="cancel" :title="modelTitle"
      @confirm="confirmOk()">
      <view slot="default" class="noun" v-if="isIndex == 0">
        <view class="noun-item">
          <view class="noun-item-title">成交额</view>
          <view>统计筛选时段内,状态为已支付得订单金额总和</view>
        </view>
        <view class="noun-item">
          <view class="noun-item-title">成交数</view>
          <view>统计筛选时段内,状态为已支付得订单数量总和</view>
        </view>
        <view class="noun-item">
          <view class="noun-item-title">筛选时间范围示例</view>
          <view class="noun-info">
            <view class="noun-info-item">今天是12月31日,对应选择得周期为</view>
            <view class="noun-info-item">【昨日】则统计12月30日00:00:00-23:59:59的数据</view>
            <view class="noun-info-item">【今日】则统计12月31日00:00:00-实时的数据</view>
            <view class="noun-info-item">【近7日】则统计12月24日00:00:00-12月30日23:59:59的数据</view>
            <view class="noun-info-item">【近30日】则统计12月1日00:00:00-12月30日23:59:59的数据</view>
          </view>
        </view>
      </view>
      <view class="center" v-else-if="isIndex == 1">是否查看缺电设备？</view>
    </BaseModal>
    <!-- 绑定微信 -->
    <!-- <BaseModal :show.sync="isShowModalQrcode" title="扫码、登录、绑定微信" :mask="true" confirmText="保存二维码"
      @confirm="confirmScanQrcode">
      <view class="qrcode">
        <view class="qrcode-img">
          <Uqrcode ref="uQRCode" :text="vSelectSysObj.extra" :margin="10" mode="canvas" />
        </view>
        <view class="qrcode-tip">
          <view>请绑定微信获取消息</view>
          <view>获取消费者消费信息推送</view>
        </view>
      </view>
    </BaseModal> -->
    <!-- 缺电弹窗 -->
    <!-- <BaseModal :show.sync="isElectricity" title="缺电警告" @confirm="ok()" @cancel="cancel">
      <view class="center">是否查看缺电设备？</view>
    </BaseModal> -->

    <MyTabbar :isShowPopup="isShowPopup" @exit="exit" :isSuccess="isSuccess" :sweepFunc="sweepFunc" />
    <!-- <Floating v-if="isChunk" @onClick="ok()"></Floating> -->
    <Floating v-if="isChunk" @onClick="ok()"></Floating>
    <!-- <Floating dragWidth="110" dragHeight="110" x="365" y="100" :kefuUrl="require('@/static/img/gzh.gif')"
      @onClick="navigatorUrl()"></Floating> -->
  </view>
</template>

<script>
import BaseSearch from '@/components/base/BaseSearch.vue'
import BaseTabs from '@/components/base/BaseTabs.vue'
import MyTabbar from './MyTabbar.vue'
import BaseNavbar from '@/components/base/BaseNavbar.vue'
import BaseIcon from '@/components/base/BaseIcon.vue'
import BaseCountTo from '@/components/base/BaseCountTo.vue'
import BaseGrid from '@/components/base/BaseGrid.vue'
import CloudVideoCard from '@/components/card/CloudVideoCard.vue'
import UniSwiperDot from '@/components/uni-swiper-dot/uni-swiper-dot'
import BaseModal from '@/components/base/BaseModal.vue'
import BaseUpdata from '../../components/base/BaseUpdata.vue'
import Floating from '../../components/floating/Floating.vue'
import { getUrlParams, getUrlDynamicData } from '@/common/tools/utils'
import { navigateToReplenishList } from '@/wxutil/navgate.js'
export default {
  components: {
    MyTabbar,
    BaseSearch,
    BaseTabs,
    BaseNavbar,
    BaseIcon,
    BaseCountTo,
    BaseGrid,
    CloudVideoCard,
    UniSwiperDot,
    BaseModal,
    BaseUpdata,
    Floating,
  },

  data() {
    return {
      //title: vSiteConfig.site_info.site_name ,
      upTime: '',
      curTabIndex: 0,
      tabList: [
        {
          name: '今日',
          status: 0,
        },
        {
          name: '昨日',
          status: 1,
        },
        {
          name: '近7日',
          status: 2,
        },
        {
          name: '近30日',
          status: 3,
        },
      ],

      isShowMoney: true,
      funcsList: [],
      funcsListSlice: [],
      machineStatusList: [],
      machineStatus: [
        {
          number: 0,
          name: '在线设备',
        },
        {
          number: 0,
          name: '故障设备',
        },
        {
          number: 0,
          name: '工作设备',
        },
        {
          number: 0,
          name: '总设备数',
        },
      ],
      salesInfo: [],
      current: 0,
      isShowInfoCircle: false, //显示名词解释弹窗
      articleList: [], //推荐文章
      isShowModalQrcode: false,
      handleInfo: {
        machineStatus: {
          useing: 0,
          waiting: 0,
          total: 0,
        },
        tAmount: {
          orderNum: 0,
          amount: 0,
        },
        yAmount: {
          orderNum: 0,
          amount: 0,
        },
        wAmount: {
          orderNum: 0,
          amount: 0,
        },
        mAmount: {
          orderNum: 0,
          amount: 0,
        },
      }, //手动修改的休息
      manyUpdata: false, //余额更新事件
      devUpdata: false, //设备更新时间
      offBtn: false,
      isShowPopup: false, //扫描之后的显示隐藏
      isSuccess: false,
      isElectricity: false,
      sweepFunc: [],
      mid: '',
      vscode: "",
      isChunk: false,
      isLogin: false,
      isIndex: 0,
      modelTitle: '温馨提示',
    }
  },
  onLoad(opt) {
    //没有数据就回到首页
    if (this.vUserInfo && this.vUserInfo.user && this.vUserInfo.user.openid) {
      // 当 this.vUserInfo 及其属性存在时执行的代码
    } else {
      this.isShowModalQrcode = true
    }
    if (opt && opt.from == 'login') {
      this.isLogin = true
      this.isChunk = true
    } else {
      this.isLogin = false
    }
    // 获取数据和权限
    this.loadDataAndPermissions()
    // 注册事件监听
    this.registerEventListeners()
    // 在小程序环境下显示分享菜单
    /*  #ifdef MP-WEIXIN */
    wx.showShareMenu({
      withShareTicket: true,
      menus: ['shareAppMessage', 'shareTimeline'],
    })
    /*   #endif */
  },
  onShow() {
    this.offBtn = false
    // const pages = getCurrentPages()
    // 声明一个pages使用getCurrentPages方法
    // wx.clearStorageSync();
  },
  methods: {
    //跳转关注公众号页面
    navigatorUrl() {
      uni.navigateTo({
        url: "/pagesC/webView/WebView?url=" + encodeURIComponent('https://mp.weixin.qq.com/s/kbKCepicndQ-Zovpd452mw'),
      });
    },
    confirmOk() {
      if (this.isIndex == 1) {
        this.ok()
      } else {
        this.isShowInfoCircle = false
      }
    },
    isShowClick() {
      this.isShowInfoCircle = !this.isShowInfoCircle
      this.isIndex = 0
      this.modelTitle = '名词说明'
    },
    loadDataAndPermissions() {
      // 获取绑定的酒店信息
      this.getBindHotelUMs()

      // 刷新数据
      this.refresh()

      // 设置权限
      this.setPermissions(this.vUserInfo.role_id)

      //获取角色列表
      this.getroleID()

      // 处理功能列表
      this.processFuncsList()
    },

    processFuncsList() {
      // 处理功能列表
      this.funcsList = Array.isArray(this.vUserInfo.mini_menu) ? [...this.vUserInfo.mini_menu] : [];

      let itemIndex = 0,
        colNum = 8 //每页显示数量
      this.funcsListSlice = []
      while (itemIndex < this.funcsList.length) {
        this.funcsListSlice.push(
          this.funcsList.slice(itemIndex, itemIndex + colNum),
        )
        itemIndex += colNum
      }
    },

    registerEventListeners() {
      // 注册登录事件监听
      uni.$off('login')
      uni.$on('login', (param) => {
        if (param.from == 'login') {
          this.isLogin = true
          this.isChunk = true
          this.loadDataAndPermissions()
        } else {
          this.isLogin = false
        }
      })

      // 注册 tab 切换事件监听（仅在 H5 环境下）
      /* #ifdef H5 */
      uni.$off('tabSwitch')
      uni.$on('tabSwitch', (params) => {
        if (params.from == 'Scan' && params.mid) {
          this.showMasseg()
        } else if (params.result) {
          this.urlTodevice(params.result)
        }
      })
      /* #endif */
    },
    backgroundStyle() {
      if (this.vSiteConfig && this.vSiteConfig.xcx_custom_theme_color) {
        // 如果是有效的颜色值，则返回一个包含该颜色值的对象
        return { background: this.vSiteConfig.xcx_custom_theme_color };
      } else {
        // 如果不是有效的颜色值，或者不是一个对象，则返回默认的背景颜色
        return null;
      }
    },
    cancel() {
      if (this.isIndex == 1) {
        this.isChunk = true
      }
    },
    // electricity() {
    //   this.isElectricity = true
    // },
    async ok() {
      if (this.offBtn) {
        return;
      }
      this.isChunk = true;
      this.offBtn = true;
      const url = `/pagesB/replenish/ReplenishList?from=replenish&status=2`;
      try {
        await navigateToReplenishList(url);
      } catch (err) {
        console.error('Navigation error:', err);
      } finally {
        this.offBtn = false;
      }
    },
    /* 请求是否有缺电的 */
    async getBindHotelUMs() {
      try {
        let data = { electricity: 0, page: 1 };
        let res = await this.$u.api.getBindHotelUMs(data);
        const hasData = res && res.data && res.data.length > 0;
        const isLoggedIn = this.isLogin && !this.isShowModalQrcode;
        this.isShowInfoCircle = hasData && isLoggedIn;
        if (this.isShowInfoCircle) {
          this.isIndex = 1
          this.modelTitle = '缺电警告'
        }
        this.isChunk = hasData || false; // Default to false if no data
      } catch (error) {
        // Error handling
        console.error('获取缺电列表：', error);
      }
    },

    /* 根据url获取设备信息 */
    async urlTodevice(result) {
      try {
        if (result.includes('vscode')) {
          this.vscode = getUrlParams(result, 'vscode')
          // 小程序扫码蓝牙码
          let data = {
            vscode: this.vscode,
          }
          let rtn = await this.$u.api.getUMVscode(data)
          this.mid = rtn?.id
        } else if (result.includes('mid')) {
          this.mid = getUrlDynamicData(result, 'mid')
        } else if (result.includes('coupon/index/id')) {
          //跳转到优惠券核销界面
          //https://towel.wrlsw.com/response/coupon/index/id/1
          const id = getUrlDynamicData(result, 'id')
          if (!id) return this.isShowErr('核销二维码格式错误~')
          let url = `/pagesB/coupon/CouponCheck?id=${id}`
          await navigateToReplenishList(url);
          // 导航成功后的代码
          return
        } else {
          if (result.includes('device_sn')) {
            this.device_sn = getUrlDynamicData(result, 'device_sn')
          } else {
            this.device_sn = result
          }
          let data = {
            device_sn: this.device_sn,
          }
          let rtn = await this.$u.api.getUMByDeviceSn(data)
          this.mid = rtn?.id
        }
        if (this.mid > 0) {
          this.showMasseg()
        }
      } catch (error) {
        // 异常处理
        console.error('获取设备信息错误：', error);
        // 导航失败后的代码
      }
    },
    /* 根据id显示状态 */
    async showMasseg() {
      let param = '?from=index_scan&mid=' + this.mid
      if (this.vUserInfo.role_id >= 1 && this.vButtonPermisFour) {
        this.sweepFunc.push({
          name: '绑定' + this.vPointName,
          src: './../../static/img/index_scan/bind.png',
          url: '/pagesB/place/BindPlace' + param,
        })

        this.sweepFunc.push({
          name: '设备操作',
          src: ' ./../../static/img/index_scan/goodsSet.png',
          url: '/pagesB/device/DeviceGoodsList' + param + '&isFrom=replenish',
        })
        // this.sweepFunc.push({
        //   name: "设备商品",
        //   src: "./../../static/img/index_scan/deviceDetails.png",
        //   url:
        //     "/pagesB/device/DeviceGoodsList" +
        //     param +
        //     "&isFrom=device_goods",
        // });

        this.sweepFunc.push({
          name: '领取设备',
          src: './../../static/img/index_scan/finance.png',
          url: '/pagesB/device/DeviceActivate' + param,
        })

        this.sweepFunc.push({
          name: '分配设备',
          src: './../../static/img/index_scan/deviceDetails.png',
          url: '/pagesB/device/DeviceAllot' + param,
        })
        // this.sweepFunc.push({
        //   name: "遥控设备",
        //   src: "./../../static/img/index_scan/remote.png",
        //   url: "/pagesB/remote/RemoteEquipment" + param,
        // });
        // this.sweepFunc.push({
        //   name: "关联设备",
        //   src: "./../../static/img/index_scan/relevance-equipment.png",
        //   url: "/pagesB/relevance/RelevanceEquipment" + param,
        // });
      } else if (this.vUserInfo.role_id == 5) {
        this.sweepFunc.push({
          name: '绑定' + this.vPointName,
          src: './../../static/img/index_scan/bind.png',
          url: '/pagesB/place/BindPlace' + param,
        })

        this.sweepFunc.push({
          name: '设备操作',
          src: ' ./../../static/img/index_scan/goodsSet.png',
          url: '/pagesB/device/DeviceGoodsList' + param + '&isFrom=replenish',
        })

        this.sweepFunc.push({
          name: '领取设备',
          src: './../../static/img/index_scan/finance.png',
          url: '/pagesB/device/DeviceActivate' + param,
        })
      } else if (this.vUserInfo.role_id == 7) {
        // this.sweepFunc.push({
        //   name: "绑定" + this.vPointName,
        //   src: "./../../static/img/index_scan/bind.png",
        //   url: "/pagesB/place/BindPlace" + param,
        // });
        this.sweepFunc.push({
          name: '领取设备',
          src: './../../static/img/index_scan/finance.png',
          url: '/pagesB/device/DeviceActivate' + param,
        })
        this.sweepFunc.push({
          name: '设备操作',
          src: ' ./../../static/img/index_scan/goodsSet.png',
          url: '/pagesB/device/DeviceGoodsList' + param + '&isFrom=replenish',
        })

        // this.sweepFunc.push({
        //   name: "遥控设备",
        //   src: "./../../static/img/index_scan/remote.png",
        //   url: "/pagesB/remote/RemoteEquipment" + param,
        // });
      } else {
        // this.sweepFunc.push({
        //   name: '设备操作',
        //   src: ' ./../../static/img/index_scan/goodsSet.png',
        //   url: '/pagesB/device/DeviceGoodsList' + param + '&isFrom=replenish',
        // })
        // 直接补货界面
        let url = '/pagesB/device/DeviceGoodsList' + param + '&isFrom=replenish'
        try {
          await navigateToReplenishList(url);
          // 导航成功后的代码
        } catch (err) {
          // 导航失败后的代码

        }
        this.isShowPopup = true
        this.isSuccess = true
      }
    },
    async getroleID() {
      try {
        let data = {
          id: 0,
        }
        let res = await this.$u.api.getRoleAllList(data)
        if (res.length > 0) {
          let i = 0
          let list = []
          res.map((item) => {
            if (item.id > this.vUserInfo.role_id) {
              i++
              return list.push({
                ...item,
              })
            }
          })
          this.$u.vuex('vRoleList', list)
        }
      } catch (error) {
        console.log('error', error)
      }
    },
    async setPermissions(id) {
      // console.log('剪切广告调用此方法')
      const vuexActions = {
        vInputDisable: id > 5 && id != 7,
        vInputPermissions: id > 5 && id != 12,
        vButtonPermissions: id < 6 || id == 12,
        vButtonPermis: id != 6 && id != 8,
        vButtonPermisTwo: (id != 6 && id < 8) || id == 12,
        vButtonPermisThree: id < 8 || id == 12,
        vButtonPermisFour: id < 5 || id == 12,
        vButtonPermisAides: id != 12
      };

      for (const [action, condition] of Object.entries(vuexActions)) {
        this.$u.vuex(action, condition);
      }
    },
    async navigator(url) {
      if (this.offBtn) {
        return
      }
      this.offBtn = true
      // let that = this
      // console.log('跳转地址', url)
      try {
        await navigateToReplenishList(url);
        // 导航成功后的代码
        this.offBtn = false;
      } catch (err) {
        // 导航失败后的代码
        this.offBtn = false;
      }
    },
    /* 获取当前时间 */

    /* 更新时间 */
    updataClick() {
      this.getSalesInfo()
    },
    undataTimes() {
      this.getMachineStatus()
    },
    /* 跳转财务管理 */
    async go() {
      let url = '/pagesB/finance/FinanceList'
      try {
        await navigateToReplenishList(url);
        // 导航成功后的代码
      } catch (err) {
      }
    },
    //确定修改数据

    confrimHandle() {
      this.machineStatus[0].number = this.handleInfo.machineStatus.useing ?? 0
      this.machineStatus[1].number = this.handleInfo.machineStatus.waiting ?? 0
      this.machineStatus[3].number = this.handleInfo.machineStatus.total ?? 0

      let res = this.handleInfo
      let moneyArr = []
      moneyArr = parseFloat(res?.tAmount?.amount).toFixed(2).split('.')
      this.salesInfo[0] = {
        orderNum: res.tAmount.orderNum,
        amount: moneyArr,
      }
      moneyArr = parseFloat(res?.yAmount.amount).toFixed(2).split('.')
      this.salesInfo[1] = {
        orderNum: res.yAmount.orderNum,
        amount: moneyArr,
      }
      moneyArr = parseFloat(res?.wAmount.amount).toFixed(2).split('.')
      this.salesInfo[2] = {
        orderNum: res.wAmount.orderNum,
        amount: moneyArr,
      }
      moneyArr = parseFloat(res?.mAmount.amount).toFixed(2).split('.')
      this.salesInfo[3] = {
        orderNum: res.mAmount.orderNum,
        amount: moneyArr,
      }
    },
    change(e) {
      this.current = e.detail.current
    },
    onClickIcon(e) {
      // console.log("e", e);
    },
    tabChange(e) {
      // console.log("点击了Tbas", e);
      this.curTabIndex = e
    },
    onIsShowMoney() {
      this.isShowMoney = !this.isShowMoney
    },
    //获取设备信息
    async getMachineStatus() {
      try {
        let res = await this.$u.api.getMachineStatus()
        let i = 0
        for (let key in res) {
          //for循环let key是对象里面的键，再通过,[]的形式this.objNum[item]去获取对象的value值
          if (i >= 4) break
          // console.log('key', key);
          // console.log('value', res[key]);
          this.machineStatus[i] = {
            number: res[key], //rtnData["Normal"],
            name: key, // "在线设备"
          }
          i++
        }
        this.devUpdata = !this.devUpdata
        return
      } catch (error) {
        console.log('获取设备信息', error);
        return
      }

    },
    //获取销售额信息
    async getSalesInfo() {
      try {
        let res = await this.$u.api.getSalesInfo()
        this.salesInfo = []
        let moneyArr = []

        // this.salesInfo.push({
        //   orderNum: 332,
        //   amount: ["489", "00"],
        // });
        // this.salesInfo.push({
        //   orderNum: 0,
        //   amount: ["0", "00"],
        // });
        // this.salesInfo.push({
        //   orderNum: 2447,
        //   amount: ["4890", "00"],
        // });
        // this.salesInfo.push({
        //   orderNum: 10479,
        //   amount: ["20900", "08"],
        // });

        moneyArr = parseFloat(res?.todayData?.tAmount).toFixed(2).split('.')
        this.salesInfo.push({
          orderNum: res.todayData?.tOrderNum,
          amount: moneyArr,
        })
        moneyArr = parseFloat(res?.yesterdayData?.yAmount)
          .toFixed(2)
          .split('.')
        this.salesInfo.push({
          orderNum: res.yesterdayData?.yOrderNum,
          amount: moneyArr,
        })
        moneyArr = parseFloat(res?.weekData?.wAmount).toFixed(2).split('.')
        this.salesInfo.push({
          orderNum: res.weekData?.wOrderNum,
          amount: moneyArr,
        })
        moneyArr = parseFloat(res?.monthData?.mAmount).toFixed(2).split('.')
        this.salesInfo.push({
          orderNum: res.monthData?.mOrderNum,
          amount: moneyArr,
        })
        // console.log("收益数据", this.salesInfo);
        this.manyUpdata = !this.manyUpdata
      } catch (error) {
        console.log('获取销售额信息', error)
      }
    },
    async goMore() {
      try {
        await navigateToReplenishList('/pagesB/cloud/CloudCollege');
      } catch (err) {
      }
    },
    async getArticleList() {
      try {
        let data = {
          recommended: true,
        }
        let res = await this.$u.api.articleList(data)
        this.articleList = res

      } catch (error) {
        console.log('error', error)
      }

    },
    async searchDevice(e) {
      try {
        await navigateToReplenishList('/pagesB/device/DeviceList?from=home&device_sn=' + e);
        // 导航成功后的代码
      } catch (err) {
        // 导航失败后的代码
      }
    },
    async userMessage() {
      try {
        let res = await this.$u.api.userMessage()
        this.$u.vuex('vNoticeNum', res.data.count)
      } catch (error) {
        console.log('获取消息失败', error)
      }

    },
    async goNotice() {
      try {
        await navigateToReplenishList('/pagesB/notice/NoticeList')
        // 导航成功后的代码
      } catch (err) {
        // 导航失败后的代码
      }

    },
    confirmScanQrcode() {
      // uni.navigateTo({
      //   url:
      //     "/pagesC/webView/WebView?url=" +
      //     encodeURIComponent(this.vSelectSysObj.extra),
      // });
      this.$refs.uQRCode.toTempFilePath({
        success: (res) => {
          // console.log("🚀 ~ res", res);
          uni.saveImageToPhotosAlbum({
            filePath: res?.tempFilePath,
            success: (result) => {
              this.isShowSuccess('二维码保存成功')
            },
            fail: (error) => {
              this.isShowErr('二维码保存失败~')
            },
          })
        },
        fail: (err) => {
          this.isShowErr('二维码保存失败~')
        },
      })
    },
    refresh() {
      //设置默认销售数据
      this.userMessage()
      this.getArticleList()

      let salesInfoData = { orderNum: 0, amount: ['0', '00'] }
      for (let i = 0; i < 4; i++) {
        this.salesInfo.push(salesInfoData)
      }
      this.getSalesInfo()
      this.getMachineStatus()
    },
    /* 设备跳转 */
    async device(item, i) {
      if (this.offBtn) {
        return
      }
      this.offBtn = true;
      const urlOptions = [
        "/pagesB/device/DeviceList?from=device&status=1",
        "/pagesB/replenish/ReplenishList?from=replenish",
        "/pagesB/replenish/ReplenishList?from=replenish&status=6",
        "/pagesB/device/DeviceList?from=device&status=0"
      ];

      let url = '';
      if (this.vInputPermissions) {
        url = urlOptions[1]; // ReplenishList for permissions
      } else if (i >= 0 && i < urlOptions.length) {
        url = urlOptions[i]; // Based on index
      }

      try {
        await navigateToReplenishList(url);
      } catch (err) {
        console.error('Navigation error:', err);
      } finally {
        this.offBtn = false;
      }
    },
  },

  onPullDownRefresh() {
    this.refresh()
  },
}
</script>
<style lang="scss">
/*当button里面包含图文时，图文之间会有间距，消除间距的方式是首先对button采用flex竖向布局，然后将子元素的margin和padding设置为0*/
.button_bt {
  &:after {
    border: none;
  }


  .button[plain] {
    border: none;
  }

  &:first-child {
    margin-top: 0;
  }

  &:active {
    background-color: white;
  }

  background-color: white;
  border-radius: 0;
  padding: 0;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
</style>
<style lang="scss" scoped>
.center {
  text-align: center;
}

.flex {
  display: flex;
  width: 100%;
  align-items: center;
  justify-content: space-between;
}

.content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
}

.logo {
  height: 200rpx;
  width: 200rpx;
  margin: 200rpx auto 50rpx auto;
}

.search-top {
  position: relative;
  width: 100%;
  display: flex;
  align-items: center;
  z-index: 999;
  background-color: #fff;

  .search-right-icon {
    display: flex;
    flex: 1;
    justify-content: space-between;

    .serverMenu {
      border: none;
      padding: 0 !important;
      margin: 0 !important;

      .imgServer {
        width: 44rpx;
        height: 44rpx;
        padding: 0 !important;
        margin: 0 !important;
      }
    }

    .notice {
      position: relative;
      margin-right: 30rpx;

      &-icon {
        width: 40rpx;
        height: 44rpx;
      }

      .circle {
        position: absolute;
        right: 0;
        top: 0;
        width: 12rpx;
        height: 12rpx;
        border-radius: 50%;
        background-color: $textWarn;
      }
    }
  }
}

::v-deep .u-grid-item-box {
  flex: none;
}

.text-area {
  display: flex;
  justify-content: center;

}

.title {
  font-size: 36rpx;
  color: #8f8f94;
}

.title-right {
  width: 500rpx;
  // margin-left: 20rpx;
  height: 50rpx;
}

.myHone {
  width: 100%;
  overflow: hidden;

  .myHomes {
    padding: 20rpx 30rpx;
    box-sizing: border-box;
  }

  .tabsBox {
    width: 100%;
    height: 52rpx;

    .tabs {
      width: 100%;
    }

    .showMoneytIcon {
      margin-left: 60rpx;

      .eyefillBox {
        width: 50rpx;
        height: 50rpx;

        .eyefill {
          width: 42rpx;
          height: 24rpx;
        }
      }

      .infoCircle {
        margin-left: 32rpx;
      }
    }
  }

  .moneyPanel {
    position: relative;
    margin-top: 34rpx;
    width: 100%;
    height: 142rpx;
    border-radius: 16rpx;
    overflow: hidden;

    .panelImg {
      width: 100%;
      height: 100%;
    }

    .turnover,
    .dealNum {
      width: 50%;
      height: 100%;
      position: absolute;
      top: 0;
      box-sizing: border-box;
      padding: 20rpx 0;

      .title {
        color: $textWhite;
        font-size: $font-size-small;
      }
    }

    .turnover {
      left: 0;

      .moneyBox {
        color: $textWhite;
        font-size: $font-size-middle;
        font-weight: 700;
      }
    }

    .dealNum {
      right: 0;
      color: $textWhite;
      font-weight: bold;
    }
  }

  .myDevice {
    margin-bottom: 10rpx;

    .title {
      color: $textBlack;
      font-weight: 700;
      margin: 40rpx 0 30rpx;
    }

    .panel {
      width: 100%;
      height: 142rpx;
      position: relative;
      border-radius: 16rpx;
      box-sizing: border-box;

      .img {
        width: 100%;
        height: 100%;
      }

      .box {
        position: absolute;
        width: 100%;
        height: 100%;
        top: 0;
        left: 0;

        >view {
          width: 25%;
          height: 100%;
          color: $textBlack;

          .type {
            font-size: $font-size-small;
            margin-top: 16rpx;
          }
        }
      }
    }
  }

  .cloudCollege {
    margin-top: 60rpx;

    .top {
      .title {
        color: $textBlack;
        font-size: $font-size-xlarge;
        font-weight: bold;
      }

      .more {
        color: $textDarkGray;
        font-size: $font-size-base;
      }
    }
  }

  .funcs {
    .swiper-box {
      height: 430rpx;
    }
  }
}

.noun {
  font-size: $font-size-base;
  color: $textDarkGray;

  &-item {
    margin-top: 30rpx;

    &-title {
      font-weight: 700;
      color: $textBlack;
      margin-bottom: 30rpx;
    }
  }

  &-info {
    .noun-info-item {
      margin-bottom: 10rpx;
    }
  }
}

.qrcode {
  display: flex;
  flex-direction: column;
  justify-content: center;
  text-align: center;

  &-top {
    color: $textBlack;
    font-size: $font-size-xlarge;
    font-weight: 700;
  }

  &-img {
    margin: 0 auto;
  }

  &-tip {
    margin-top: 10rpx;
  }
}

.font-min {
  text-align: center;
  width: 240rpx;
  font-size: 22rpx !important;
}

.font-witer {
  font-weight: bold;
  font-size: 26rpx;
}

.updata {
  margin-top: 5rpx;
  // border: 1px solid #000;
}

.font-position {
  height: 100rpx;
  // border: 1px solid #000;
  display: flex;
  flex-direction: column;
  justify-content: space-around;
  align-items: center;
}

//手动修改
.handle {
  .item {
    &-title {
      color: #333;
      font-weight: 700;
      font-size: $font-size-middle;
    }

    &-content {
      display: flex;
      font-size: $font-size-base;

      &-box {
        display: flex;
        align-items: center;

        &-title {
          flex-shrink: 0;
        }

        &:last-child {
          margin-left: 10rpx;
        }
      }
    }

    &-device {
      font-size: $font-size-base;

      &-box {
        display: flex;
        align-items: center;
        margin-bottom: 10rpx;

        :first-child {
          flex-shrink: 0;
        }

        &-input {
          width: 100%;
          margin-left: 10rpx;
        }
      }
    }
  }
}
</style>
