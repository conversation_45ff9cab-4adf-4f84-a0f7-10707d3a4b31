<template>
  <view class="card">
    <view class="content">
      <view>
        <view>任务：</view>
        <view>{{ info.name }}</view>
      </view>
      <view>
        <view>用户：</view>
        <view>{{ info.user_nickname }}</view>
      </view>
      <view>
        <view>状态：</view>
        <view :style="info.status == 1 ? '' : 'color:red'">{{
          info.status == 1 ? "完成" : "未完成"
        }}</view>
      </view>
      <view>
        <view>时间：</view>
        <view>{{
          $u.timeFormat(info.time * 1000, "yyyy-mm-dd hh:MM:ss")
        }}</view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: "ScoreDetailsCard",
  props: { info: { type: Object, default: {} } },
};
</script>

<style lang="scss" scoped>
.card {
  padding: 20rpx 30rpx;
}
.content {
  > view {
    display: flex;
    > view {
      &:first-child {
        color: $textDarkGray;
        font-size: $font-size-middle;
      }
      &:last-child {
        color: $textBlack;
        font-size: $font-size-base;
      }
    }
  }
}
</style>