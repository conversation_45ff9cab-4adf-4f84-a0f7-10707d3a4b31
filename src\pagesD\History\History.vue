<template>
  <view>
    <BaseNavbar :title="title" />
    <view class="history_top">
      <image src="../static/img/Historybcg.png" />

      <view class="history_list">
        <view class="history_text">
          <view>{{ vPointName }}：</view>
          <view>
            {{ hotelName || '未绑定' }}
          </view>
        </view>
        <view class="history_text">
          <view>
            {{ '设备编号：' }}
          </view>
          <view>
            {{ device_sn||'' }}
          </view>
        </view>
        <view class="history_text">
          <view>
            {{ '系统码：' }}
          </view>
          <view>
            {{ vCode || '未绑定' }}
          </view>
        </view>

        <view class="history_text">
          <view>
            {{ '详细地址：' }}
          </view>
          <view>
            {{ addressDetail || '无' }}
          </view>
        </view>
        <view class="history_text">
          <view>
            {{ '拥有者：' }}
          </view>
          <view>
            {{ user_login || '无' }}
          </view>
        </view>
      </view>
    </view>
    <u-notice-bar :list="list"></u-notice-bar>
    <ComList :loading-type="loadingType">
      <view class="history_main">
        <view v-if="dataList.length > 0">
          <view v-for="(item, i) in dataList" :key="i">
            <view v-for="(items, index) in item" :key="index">
              <view class="time_item" v-if="index == 0">
                <view class="box box_title" :class="i == 0 && index == 0 ? 'green' : ''">
                  {{ tiem(items.time) }}
                  <view class="dot" v-if="false"></view>
                </view>
              </view>
              <view class="time_item">
                <view class="box" :class="i == 0 && index == 0 ? 'green' : ''">
                  {{ items.time.split(' ')[1].slice(0, -3) }}
                  <view class="dot" :class="i == 0 && index == 0 ? 'dot_green' : ''"></view>
                </view>
                <view class="item_right">
                  <view class="u-order-title" :class="i == 0 && index == 0 ? 'green' : ''">
                    {{ items.type }}
                  </view>
                  <view class="u-order-time blue" :class="i == 0 && index == 0 ? 'green' : ''">
                    {{ items.time }}
                  </view>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </ComList>
  </view>
</template>

<script>
import BaseNavbar from '@/components/base/BaseNavbar.vue'
import myPull from '@/mixins/myPull.js'
import { timestampToTime } from '@/common/tools/utils.js'
import ComList from '@/components/list/ComList.vue'
export default {
  components: {
    BaseNavbar,
    ComList,
  },
  mixins: [myPull()],
  data() {
    return {
      title: '行为记录',
      device_sn: '',
      dataList: [],
      upTime: '',
      yesterupTime: '',
      upTimez: '',
      user_nickname: '',
      hotelName: '',
      user_login: '',
      vCode: '',
      adr_title: '',
      addressDetail: '',
      list: [
        '只显示最近7天的记录'
      ],
    }
  },

  methods: {
    tiem(tiem) {
      return tiem.split(' ')[0] == this.upTime.split(' ')[0]
        ? '今天'
        : tiem.split(' ')[0] == this.yesterupTime.split(' ')[0]
          ? '昨天'
          : tiem.split(' ')[0] == this.upTimez.split(' ')[0]
            ? '前天'
            : tiem.split(' ')[0]
    },
    groupDataByDate(data, index = 0) {
      if (index >= data.length) {
        return []
      }

      const currentData = data[index]
      const currentDate = currentData.time.split(' ')[0]

      let foundGroup = false

      for (let i = 0; i < this.dataList.length; i++) {
        if (
          this.dataList[i][0] &&
          this.dataList[i][0].time.split(' ')[0] === currentDate
        ) {
          this.dataList[i].push(currentData)
          foundGroup = true
          break
        }
      }

      if (!foundGroup) {
        this.dataList.push([currentData])
      }

      return this.groupDataByDate(data, index + 1)
    },
    //数据的请求
    async getList(page, done) {
      try {
        this.isShowPopup = false
        let data = {
          device_sn: this.device_sn,
          page,
          limit: 15,
        }
        let res = await this.$u.api.getUMLogList(data)
        //获取当前时间戳，
        //转为年月日
        //筛选进一个数组
        //把这个数组push到一个新的数组中

        // for (let i = 0; i < res.data.length; i++) {
        //     if (res.data[i].time.split(' ')[0] == upTime.split(' ')[0]) {
        //         data[0].push(res.data[i])
        //     } else if (res.data[i].time.split(' ')[0] == yesterupTime.split(' ')[0]) {
        //         data[1].push(res.data[i])
        //     } else if (res.data[i].time.split(' ')[0] == upTimez.split(' ')[0]) {
        //         data[2].push(res.data[i])
        //     }

        // }
        this.groupDataByDate(res.data)
        /* 其他时间 */

        // this.total = res.total;
        done(res.data)
      } catch (error) {
        console.log(error)
      }

    },
  },
  computed: {},
  onLoad(opt) {
    let data = decodeURIComponent(opt.data)
    data = JSON.parse(data)
    this.device_sn = data.device_sn
    this.user_nickname = data?.dw?.user_nickname
    this.adr_title = data?.adr_title
    this.hotelName = data?.hotelName
    this.user_login = data?.user?.user_login
    this.vCode = data?.vCode
    this.addressDetail = data?.addressDetail
    /* 今天 */
    let a = new Date()
    this.upTime = timestampToTime(a)
    //获取昨天
    a.setDate(a.getDate() - 1)
    const yesterdayTimestamp = a.getTime()
    this.yesterupTime = timestampToTime(yesterdayTimestamp)
    /* 前天 */
    a.setDate(a.getDate() - 1)
    const dayTimestamp = a.getTime()
    this.upTimez = timestampToTime(dayTimestamp)
    this.refresh()
  },
}
</script>

<style lang="scss" scoped>
.history_top {
  width: 100%;
  // height: 250rpx;
  position: relative;
  font-size: 24rpx;

  image {
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    width: 100%;
    z-index: -1;
  }

  .history_list {
    padding: 10rpx 30rpx;
    color: white;

    .history_text {
      display: flex;
      margin: 5rpx 0;

      >view:nth-child(1) {
        width: 180rpx;
        text-align: justify;
        text-align-last: justify;
      }
    }
  }
}

/* .wxss 文件 */

.history_main {
  margin-top: 15rpx;
  padding: 10rpx 30rpx;
}

.u-node {
  width: 44rpx;
  height: 44rpx;
  border-radius: 100rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  background: #d0d0d0;
}

.u-order-title {
  color: #333333;
  font-weight: bold;
  font-size: 28rpx;
}

.u-order-desc {
  color: rgb(150, 150, 150);
  font-size: 28rpx;
  margin-bottom: 6rpx;
}

.time_item {
  display: flex;

  .box {
    font-size: 22rpx;
    width: 145rpx;
    // border: 2rpx solid #000;
    border-right: 2rpx solid rgb(179, 179, 179);
    color: #999;
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;

    // text-align: right;
  }

  .item_right {
    padding: 20rpx;
    // border: 2rpx solid #000;
    width: 450rpx;
  }

  .dot {
    width: 10rpx;
    height: 10rpx;
    background-color: rgb(182, 182, 182);
    border-radius: 50%;
    position: absolute;
    top: 50%;
    right: 0;
    transform: translate(50%, -50%);
  }

  .dot_red {
    width: 26rpx;
    height: 26rpx;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: orangered;
    color: white;
  }

  .dot_green {
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: #09b268;
    color: white;
  }

  .blue {
    color: rgb(136, 176, 246);
  }

  .red {
    color: orangered;
  }

  .green {
    color: #09b268;
  }

  .box_title {
    // border: 1px solid #000;
    padding: 0;
  }
}

.u-order-time {
  font-size: 22rpx;
}
</style>
