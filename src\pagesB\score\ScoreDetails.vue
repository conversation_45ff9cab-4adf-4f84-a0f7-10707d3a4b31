<template>
  <view>
    <BaseNavbar title="积分记录" />
    <ComList :loading-type="loadingType">
      <ScoreDetailsCard
        v-for="(item, index) in listData"
        :key="index"
        :info="item"
      />
    </ComList>
  </view>
</template>

<script>
import myPull from "@/mixins/myPull.js";
import ComList from "@/components/list/ComList.vue";
import BaseNavbar from "@/components/base/BaseNavbar.vue";
import ScoreDetailsCard from "../components/cards/ScoreDetailsCard.vue";
export default {
  components: { BaseNavbar, ComList, ScoreDetailsCard },
  data() {
    return {
      id: "",
    };
  },
  methods: {
    getList(page, done) {
      let data = {
        page,
        limit: 10,
        id: this.id,
      };
      this.$u.api.getPointList(data).then((res) => {
        done(res.data);
      });
    },
  },
  onLoad(opt) {
    this.id = opt?.id;
    this.refresh();
  },
  mixins: [myPull()],
};
</script>
<style lang="scss">
page {
  background-color: $pageBgColor;
}
</style>
<style lang="scss" scoped>
</style>