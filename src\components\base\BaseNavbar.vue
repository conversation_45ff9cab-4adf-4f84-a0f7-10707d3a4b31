<template>
  <view>
    <u-navbar :is-back="isShowBack" :title="title" :title-bold="true" :titleWidth="600" :border-bottom="false"
      :title-color="color" :background="background" :custom-back="leftimg" :immersive="isShowBg"
      :back-icon-color="isShowBg ? '#fff' : ''" :is-fixed="true" z-index="99999">
      <!-- #ifdef H5 -->
      <view class="slot-wrap" @click="leftimg" v-if="isLeft">
        <u-icon name="arrow-left" :color="color" size="40"></u-icon>
      </view>
      <!-- #endif -->
    </u-navbar>
  </view>
</template>

<script>
export default {
  name: "BaseNavbar",
  props: {
    title: { type: String, default: "" },
    /* #ifndef H5 */
    isShowBack: { type: Boolean, default: true },
    /* #endif */
    /* #ifdef H5 */
    isShowBack: { type: Boolean, default: false },
    isLeft: { type: Boolean, default: true },
    /* #endif */
    isShowBg: { type: Boolean, default: false },
    background: {
      type: Object, default: function () {
        return { background: '#fff' }; // 返回一个空对象作为默认值
      }
    },
    color: {
      type: String, default: "#333"
    },
    pageShow: {
      type: Boolean, default: false
    }
  },
  methods: {
    leftimg() {
      const pages = getCurrentPages(); // 获取当前页面栈
      const currentPage = pages.length; // 获取当前页面实例
      const routeArray = currentPage; // 获取路由中的数组
      // 根据具体逻辑处理是否需要返回上一页或者首页
      // 这里只是一个示例，你可以根据你的业务逻辑进行修改
      if (this.pageShow) {
        uni.reLaunch({
          url: '/pages/index/index'
        });// 否则返回首页
      } else if (routeArray && routeArray > 1) {

        uni.navigateBack({
          delta: 1,
          fail: () => {
            uni.reLaunch({
              url: '/pages/index/index'
            });
          }
        });
      } else {
        uni.reLaunch({
          url: '/pages/index/index'
        }); // 否则返回首页
      }
    },
    findRouteArray() {
      // 查询路由中的数组的具体实现，你可以根据你的需求修改
      const pages = getCurrentPages(); // 获取当前页面栈
      const currentPage = pages.length; // 获取当前页面实例
      return currentPage;
    },
    goBack() {
      uni.navigateBack({
        delta: 1,
        fail: () => {
          uni.reLaunch({
            url: '/pages/index/index'
          });
        }
      });
    },
    goHome() {
      uni.reLaunch({
        url: '/pages/index/index'
      });
    },
  },
  data() {
    return {

    };
  },
};
</script>

<style lang="scss" scoped>
.slot-wrap {
  margin-left: 20rpx;
}
</style>