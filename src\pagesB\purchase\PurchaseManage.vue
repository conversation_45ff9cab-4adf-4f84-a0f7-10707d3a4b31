<template>
  <view>
    <view class="sticker">
      <BaseNavbar title="集采管理" />
      <BaseTabs :current="curTabIndex" :list="tabList" @change="tabChange" />
    </view>

    <ComList :loading-type="loadingType">
      <PurchaseOrderCard
        v-for="item in listData"
        :key="item"
        :info="item"
        :is-manage="true"
        @confirm="confirm"
      />
    </ComList>
  </view>
</template>

<script>
import BaseNavbar from "@/components/base/BaseNavbar.vue";
import ComList from "@/components/list/ComList.vue";
import myPull from "@/mixins/myPull.js";
import BaseTabs from "../../components/base/BaseTabs.vue";
import PurchaseOrderCard from "../components/cards/PurchaseOrderCard.vue";
export default {
  components: { BaseNavbar, ComList, BaseTabs, PurchaseOrderCard },
  data() {
    return {
      tabList: [
        {
          name: "全部",
          status: "",
        },
        {
          name: "待审核",
          status: 0,
        },
        {
          name: "待付款",
          status: 1,
        },
        {
          name: "待发货",
          status: 2,
        },
        {
          name: "发货完成",
          status: 3,
        },
      ],
      curTabsIndex: 0,
    };
  },
  methods: {
    tabChange(e) {
      this.curTabsIndex = e;
      this.refresh();
    },

    getList(page, done) {
      let data = {
        page,
        limit: 10,
        status: this.tabList[this.curTabsIndex].status,
      };
      this.$u.api.getMyLowerPurchaseList(data).then((res) => {
        done(res.data);
      });
    },
    confirm(msg) {
      this.isShowSuccess(msg + "成功", 0, this.refresh());
    },
  },
  onLoad(opt) {
    this.refresh();
  },
  mixins: [myPull()],
};
</script>

<style lang="scss">
page {
  background-color: $pageBgColor;
}
</style>
<style lang="scss" scoped>
</style>