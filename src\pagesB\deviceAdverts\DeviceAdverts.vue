<template>
  <view>
    <BaseNavbar title="设备广告" />
    <BaseSearch placeholder="请输入设备编号" @search="search" listType="deviceAdverts" />
    <!-- <BaseList listType="deviceAdverts" @searchChange="searchChange" /> -->
    <ComList :loadingType="loadingType" :bottom="isFromAdSelectDevice ? 120 : 0">
      <DeviceAdvertsCard v-for="item in listData" :key="item.device_sn" :info="item" :isShowCheck="isFromAdSelectDevice"
        @refresh="refresh" @onCheck="selectItem(item)" />
    </ComList>
    <FixedSubButton v-if="isFromAdSelectDevice" :isShowTotal="false" :info="fixedSubButtonInfo" btnTitle="确 定"
      @changeCheckAll="changeCheckAll" @confirm="confirmSelect" />
  </view>
</template>

<script>
import BaseNavbar from "@/components/base/BaseNavbar.vue";
import DeviceAdvertsCard from "../components/cards/DeviceAdvertsCard.vue";
import ComList from "@/components/list/ComList.vue";
import myPull from "../../mixins/myPull";
import BaseSearch from "../../components/base/BaseSearch.vue";
import FixedSubButton from "../../components/common/FixedSubButton.vue";
// import BaseList from '@/components/base/BaseList.vue'
import { AddValueInObject } from '@/wxutil/list'
export default {
  components: {
    BaseNavbar,
    DeviceAdvertsCard,
    ComList,
    BaseSearch,
    FixedSubButton,
    // BaseList
  },
  mixins: [myPull()],
  computed: {
    fixedSubButtonInfo() {
      let selectList = this.listData?.filter((item) => item.isCheck) || [];
      let isAllCheck =
        selectList.length === this.listData.length && selectList.length !== 0;
      return {
        isAllCheck, //是否全选
        selectNum: selectList.length || 0, //选中数量
      };
    },
  },
  data() {
    return {
      device_sn: "", //设备编号
      fromData: "",
      isFromAdSelectDevice: false,
      hotelName: ""
    };
  },
  methods: {
    searchChange(val) {
      this.hotelName = val
      this.search(val)
    },
    search(e) {
      let list = AddValueInObject(this.vServerList.deviceAdverts, e)
      this.$u.vuex(`vServerList.deviceAdverts`, list)
      this.refresh();
    },
    async getList(page, done) {
      try {
        let data = { page, device_sn: this.device_sn };
        let res = await this.$u.api.bindAdvertList(data)
        done(res);
      } catch (error) {
        console.log('错误信息', error)
      }

    },
    //点击全选
    changeCheckAll(isAllCheck) {
      this.listData = this.listData.map((item) => {
        if (typeof item.isCheck == "undefined") {
          this.$set(item, "isCheck", isAllCheck);
        } else {
          item.isCheck = isAllCheck;
        }
        return item;
      });
    },
    //确认选择
    confirmSelect() {
      let deviceList = this.listData?.filter((item) => item.isCheck) || [];
      /*  #ifndef H5 */
      let pages = getCurrentPages();
      let currPage = pages[pages.length - 1]; //当前页面
      let prevPage = pages[pages.length - 2]; //上一个页面
      //直接调用上一个页面的setData()方法，把数据存到上一个页面中去
      prevPage.setData({
        ad_select_device: deviceList,
      });
      /*#endif */
      /*  #ifdef H5 */
      this.vCurrPage.ad_select_device = [...deviceList]
      /*#endif */
      uni.navigateBack({ delta: 1 });
    },
    selectItem(item) {
      if (typeof item.isCheck == "undefined") {
        this.$set(item, "isCheck", true);
      } else {
        item.isCheck = !item.isCheck;
      }
    },
  },
  onLoad(opt) {
    this.fromData = opt?.from;
    if (opt?.from === "ad_select_device") {
      this.isFromAdSelectDevice = true;
    }
    this.refresh();
  },
  onShow(e) {
    /*  #ifndef H5 */
    let pages = getCurrentPages();
    let currPage = pages[pages.length - 1]; // 当前页
    if (currPage.data.isDoRefresh == true) {
      // 是否刷新
      currPage.data.isDoRefresh = false;
      this.refresh();
    }
    /*#endif */
    /*  #ifdef H5 */
    if (this.vCurrPage.isDoRefresh == true) {
      // 是否刷新
      this.vCurrPage.isDoRefresh = false;
      this.refresh();
    }
    /*#endif */
  },
};
</script>
<style lang="scss">
page {
  background-color: $pageBgColor;
}
</style>
<style lang="scss" scoped></style>