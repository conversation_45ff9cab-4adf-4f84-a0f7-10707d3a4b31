<template>
  <view class="card">
    <view class="content">
      <view class="content-item">
        <view class="content-item-title">设备编号：</view>
        <view class="content-item-txt">{{ info.device_sn }}</view>
      </view>
      <view class="content-item">
        <view class="content-item-title">认养金额：</view>
        <view class="content-item-txt">{{ info.amount }} 元</view>
      </view>
      <view class="content-item">
        <view class="content-item-title">认养年限：</view>
        <view class="content-item-txt">{{ info.year }} 年</view>
      </view>
      <view class="content-item">
        <view class="content-item-title">预留号码：</view>
        <view class="content-item-txt phone" @click="callPhone(info.buyer_phone)">
          {{ info.buyer_phone }}</view>
      </view>
      <view class="content-item">
        <view class="content-item-title">认筹昵称：</view>
        <view class="content-item-txt"> {{ info.buyer || "保密" }}</view>
      </view>
      <view class="content-item">
        <view class="content-item-title">开始时间：</view>
        <view class="content-item-txt">{{
          $u.timeFormat(info.start_time, "yyyy-mm-dd hh:MM:ss")
        }}</view>
      </view>

      <view class="content-item">
        <view class="content-item-title">结束时间：</view>
        <view class="content-item-txt">{{
            $u.timeFormat(info.end_time, "yyyy-mm-dd hh:MM:ss")
          }}</view>
      </view>
      <block v-if="info.status >= 2">
        <view class="content-item">
          <view class="content-item-title">分成比例：</view>
          <view class="content-item-txt">{{ info.buyer_per }} %</view>
        </view>
        <view class="content-item">
          <view class="content-item-title">认养收益：</view>
          <view class="content-item-txt">{{ info.total_dis_money }} 元</view>
        </view>
      </block>
    </view>
    <view class="bottom">
      <view class="status">
        <view class="status-title">订单状态：</view>
        <view class="status-txt">{{
          info.pay_status === 1 ? status[info.status] : "未付款"
        }}</view>
      </view>
      <!-- <view
        class="btn"
        v-if="info.pay_status === 1 && [0, 1].includes(info.status)"
      >
        <BaseButton
          shape="circle"
          type="default"
          @onClick="btnClick(info.status)"
          >{{ btnTitle[info.status] }}
        </BaseButton>
      </view> -->
    </view>

    <!-- 认养审核通过 -->
    <!-- <BaseModal
      :show.sync="isShowModal"
      @confirm="adopt"
      content="是否确定让该笔认养订单通过审核，此操作不可恢复，是否确认？"
    />
    <BaseModal :show.sync="isShowModalUser" @confirm="applyUser">
      <view>
        <BaseInput v-model="info.phone" placeholder="请输入创建认养人的账号" />
      </view>
    </BaseModal> -->
  </view>
</template>
<script>
import BaseButton from "@/components/base/BaseButton.vue";
import BaseModal from "@/components/base/BaseModal.vue";
import BaseInput from "@/components/base/BaseInput.vue";
export default {
  name: "InvestListCard",
  props: {
    info: { type: Object, default: {} },
  },
  data() {
    return {
      status: {
        0: "待审核",
        1: "已审核",
        2: "认养中",
        3: "认养结束",
      },
      btnTitle: {
        0: "审核通过",
        1: "创建账号",
      },
      isShowModal: false,
      isShowModalUser: false,
      user_login: "",
    };
  },
  methods: {
    callPhone(phoneNumber) {
      uni.makePhoneCall({
        phoneNumber,
        success: (result) => { },
        fail: (error) => { },
      });
    },
    btnClick(i) {
      if (i == 0) {
        this.isShowModal = true;
      } else if (i == 1) {
        this.isShowModalUser = true;
      }
    },
    //订单审核通过
    async adopt() {
      try {
        let data = {
          id: this.info.id,
        };
        await this.$u.api.investProcess(data)
        this.isShowSuccess("审核已通过", 0, () => {
          this.$emit("refresh");
        });
      } catch (error) {
        console.log('错误信息', error)
      }

    },
    //申请用户
    async applyUser() {
      try {
        if (!this.$u.test.mobile(this.info.phone))
          return this.isShowErr("请填写正确手机号");
        let data = {
          id: this.info.id,
          user_login: this.info.phone,
        };
        await this.$u.api.investCreateNumber(data)
        this.isShowSuccess("创建账号成功", 0, () => {
            this.$emit("refresh");
          });
      }catch(error){
        console.log('错误信息', error)
      }
     
    },
  },
  onLoad() { },
  components: { BaseButton, BaseModal, BaseInput },
};
</script>


<style scoped lang='scss'>
.card {
  padding: 20rpx;
}

.content {
  &-item {
    display: flex;
    font-size: $font-size-middle;
    line-height: 1.6;

    &-title {
      color: $textDarkGray;
      flex-shrink: 0;
    }

    &-txt {
      color: $textBlack;
    }

    .phone {
      color: rgb(41, 121, 255);
      border-bottom: 1px solid rgb(41, 121, 255);
    }
  }
}

.bottom {
  display: flex;
  justify-content: space-between;

  .status {
    display: flex;
    font-size: $font-size-middle;
    align-items: center;

    &-title {
      color: $textBlack;
    }

    &-txt {
      color: $themeComColor;
    }
  }
}
</style>