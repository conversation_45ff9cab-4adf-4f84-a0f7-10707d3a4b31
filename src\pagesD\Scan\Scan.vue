<template>
    <view class="sectionview">
      <view id="reader"></view>
    </view>
  </template>
  
  <script>
  import { Html5Qrcode } from 'html5-qrcode'
  import { getUrlParams, getUrlDynamicData } from '@/common/tools/utils.js'
  export default {
    destroyed () {
      if(this.stops){

        this.stop()
      }
    },
    data() {
      return {
        title: '扫描',
        Html5Qrcode: null,
        isNavigated: false,
        mid: '',
        batch: false, //批量
        hotel_id:'',
        stops:false,
      }
    },
  
    methods: {
      stop() {
        // 停止扫描器
       
        this.html5QrCode
          .stop()
          .then((ignore) => {
            // QR Code scanning is stopped.
            this.stops=true
            console.log('QR Code scanning stopped.', ignore)
          })
          .catch((err) => {
            // Stop failed, handle it.
            console.log('Unable to stop scanning.', err)
          })
      },
      async start() {
        if (this.isNavigated) {
          return // 如果已经执行过跳转，不再执行
        }
        await this.html5QrCode
          .start(
            { facingMode: 'environment' },
            {
              fps: 20, // 设置每秒多少帧
              qrbox: { width: 250, height: 250 }, // 设置取景范围
              // scannable, rest shaded.
            },
            async (decodedText, decodedResult) => {
              // alert('扫码结果' + decodedText)
              if (decodedText) {
                console.log('成功信息' + decodedText)
  
                let result = decodeURIComponent(decodedText)
                console.log('🚀 ~ result 解析后 ', result)
                if (!result) return
  
                if (result.includes('vscode')) {
                  this.vscode = getUrlParams(result, 'vscode')
                  console.log('🚀 ~ this.vscode', this.vscode)
                  // 小程序扫码蓝牙码
                  let data = {
                    vscode: result.split('=')[1],
                  }
                  let rtn = await this.$u.api.getUMVscode(data)
                  this.mid = rtn?.id
                } else if (result.includes('mid')) {
                  this.mid = getUrlDynamicData(result, 'mid')
                } else if (result.includes('coupon/index/id')) {
                  //跳转到优惠券核销界面
                  //https://towel.wrlsw.com/response/coupon/index/id/1
                  const id = getUrlDynamicData(result, 'id')
                  if (!id) return this.isShowErr('核销二维码格式错误~')
                  uni.navigateTo({ url: `/pagesB/coupon/CouponCheck?id=${id}` })
                  return
                } else {
                  if (result.includes('device_sn')) {
                    this.device_sn = getUrlDynamicData(result, 'device_sn')
                  } else {
                    this.device_sn = result
                  }
                  console.log('🚀 ~ this.device_sn', this.device_sn)
                  let data = {
                    device_sn: this.device_sn,
                  }
  
                  let rtn = await this.$u.api.getUMByDeviceSn(data)
                  this.mid = rtn?.id
                }
                console.log('🚀 ~   this.mid', this.mid)
                if (this.mid > 0 && !this.isNavigated) {
                  this.isNavigated = true // 设置标志为 true，防止重复跳转
                  let that = this
                  if (!this.batch) {
                    uni.switchTab({
                      url: `/pages/index/index`,
                      success: function (e) {
                        uni.$emit('tabSwitch', { from: 'Scan', mid: that.mid })
                      },
                    })
                  } else {
                    uni.navigateTo({ url: `/pagesD/bindList/BindList?from=Scan&mid=${that.mid}&hotel_id=${this.hotel_id}` })
                  }
  
                  this.stop()
                } else {
                  // this.isShowErr('请扫码正确的二维码,二维码信息' + result, 1)
                }
              }
            },
            (errorMessage) => {
              // parse error, ideally ignore it. For example:
              // console.log(`QR Code no longer in front of camera.`);
              console.log('暂无额扫描结果', errorMessage)
            },
          )
          .catch((err) => {
            // Start failed, handle it. For example,
            console.log(`Unable to start scanning, error: ${err}`)
            this.isShowErr('错误信息' + err)
          })
      },
      getCameras() {
        Html5Qrcode.getCameras()
          .then((devices) => {
            /**
             * devices would be an array of objects of type:
             * { id: "id", label: "label" }
             */
            console.log(devices, 'cameraId')
            // this.start();
            if (devices && devices.length) {
              this.html5QrCode = new Html5Qrcode('reader')
              // start开始扫描
              this.start()
            }
          })
          .catch((err) => {
            console.log('err', err)
            this.isShowErr('错误信息' + err)
          })
      },
    },
    mounted() {
      this.current = this.$route.query.current || 0
  
      setTimeout(() => {
        this.getCameras()
      }, 100)
    },
    onLoad(opt) {
      if (opt.from == 'batch') {
        this.batch = true
        console.log('opt', opt)
        this.hotel_id=opt.hotel_id
      }
    },
    onshow(){
      setTimeout(() => {
        this.getCameras()
      }, 100)
    }
  }
  </script>
  
  <style lang="scss" scoped>
  .sectionview {
    background-color: #303030;
    position: fixed;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    z-index: 999999;
  
    #reader {
      margin-top: 50%;
    }
  }
  .btn {
    color: red;
  }
  </style>
  