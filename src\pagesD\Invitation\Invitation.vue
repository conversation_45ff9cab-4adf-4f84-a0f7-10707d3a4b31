<template>
    <view class="box">
        <BaseNavbar :title="title" />
        <!-- <image :src="listBac" />
      <image :src="bac" /> -->
        <view class="topBcg">
            <image class="bacg" :src="bac" alt="" />
            <view class="title">
                <text class="title-left">
                    我的邀请码<text class="title-code">{{ vUserInfo.user.invite_code ? vUserInfo.user.invite_code : ''
                        }}</text>
                </text>
                <view class="title-btn" @click="setClipboardData">
                    <BaseIcon name="play-left-fill" color="#fff" size="25" />
                    <text>
                        点击复制
                    </text>
                </view>
            </view>
            <view class="title-btom">
                <text>我的邀请</text>
            </view>
        </view>
        <view>
            <ComList :loading-type="loadingType">
                <InvitationCard v-for="(item, index) in listData" :key="index" :info="item">
                </InvitationCard>
            </ComList>
        </view>

    </view>
</template>

<script>
import BaseNavbar from "@/components/base/BaseNavbar.vue";
import ComList from "@/components/list/ComList.vue";
import InvitationCard from "../components/Card/InvitationCard.vue";
import BaseIcon from "@/components/base/BaseIcon.vue";
import { setClipboardData } from "@/wxutil/copy";

import myPull from "@/mixins/myPull.js";
export default {
    // name:'lottery',
    components: { BaseNavbar, ComList, InvitationCard, BaseIcon },

    data() {
        return {
            data: [{
                user_name: '王小鱼',
                create_time: 1688433956,
                user_nickname: '笑傲'
            }],
            bac: require("@/pagesD/static/img/InvitationBac.jpg"),
            title: "我的邀请",

        };
    },

    methods: {
        /* 复制到剪切板 */
        setClipboardData() {
            setClipboardData(this.code)
        },



        //数据的请求
        async getList(page, done) {
            try {
                this.isShowPopup = false;
                let data = {
                    limit: 10,
                    page,

                };
                let res = this.$u.api.getMyInviteUserList(data)
                this.total = res.total;
                done(res.data);

            } catch (error) {
                console.log(error)
            }

        },


    },

    onLoad(opt) {
        // this.fromData = opt?.from;
        this.refresh();

    },
    mixins: [myPull()],
};
</script>

<style lang="scss">
page {
    background-color: $pageBgColor;
}
</style>
<style lang="scss" scoped>
* {
    margin: 0;
    padding: 0;
}

.box {
    // border: 1px solid #000;
    text-align: center;
    // padding: 15rpx;
    // margin: 180rpx auto;
}

.topBcg {
    height: 320rpx;
    overflow: hidden;
    position: relative;

    image {
        height: 600%;
        position: absolute;
        top: 0;
        left: 0;
        z-index: -99;
        width: 100%;
    }

    .title {
        // border: 1px solid #000;
        display: flex;
        font-size: 30rpx;
        color: white;
        height: 100%;
        justify-content: center;
        // margin-top: 100rpx;
        align-items: center;
        margin-top: -35rpx;
        // border: 1px solid #000;
    }

    .title-code {
        display: inline-block;
        background-color: white;
        color: #333;
        font-size: 42rpx;
        font-weight: bold;
        // padding: 2rpx 75rpx;
        width: 280rpx;
        height: 70rpx;
        text-align: center;
        margin: 0 20rpx;
        border-radius: 15rpx;
        vertical-align: middle;
        // border: 1px solid #000;

        line-height: 70rpx;
    }

    .title-btn {
        font-size: 25rpx;
    }

    .title-btom {
        width: 100%;
        height: 90rpx;
        position: absolute;
        bottom: 0;
        left: 0;
        border-top-left-radius: 25rpx;
        border-top-right-radius: 25rpx;
        background-color: rgb(255, 199, 96);
        display: flex;
        justify-content: center;
        align-items: center;
        font-weight: bold;
    }
}
</style>