<template>
  <view>
    <BaseNavbar :title="title" />

    <BaseSearch placeholder="请输入用户账号" @search="search" listType="userList" />
    <BaseDropdown :options-list="optionsList" @change="change" :num="total" />
    <ComList :loading-type="loadingType">
      <UserListCard v-for="item in listData" :key="item.id" :info="item" @setDivide="popShow(item, 'setDivide')"
        @enableUser="popShow(item, 'enableUser')" @bindWx="popShow(item, 'bindWx')" @clickCrad="clickCrad(item)"
        @many="many(item)" />
    </ComList>
    <FixedAddIcon @onAdd="goAddUser" />
    <BaseModal :show.sync="isShowModal" @confirm="confirmModal" :content="modalContent">
      <template v-if="isDivide">
        <view class="input-percentage" slot="default">
          <BaseInput v-model="percentage" placeholder="请输入比例数值" rightText="%" />
        </view>
      </template>
      <template v-if="isBindWx">
        <view v-if="isUnbindWx">确定解绑该用户的微信吗?</view>
        <view slot="default" v-else>
          1、关注“{{ vSiteConfig.site_info.site_name }}”公众号
          <br />
          2、登录“{{ vSiteConfig.site_info.site_name }}云平台”
          <br />
          3、绑定微信号
        </view>
      </template>
    </BaseModal>
    <BaseBackTop @onPageScroll="onPageScroll" :scrollTop="scrollTop"></BaseBackTop>
  </view>
</template>

<script>
import BaseNavbar from '@//components/base/BaseNavbar.vue'
import BaseSearch from '@//components/base/BaseSearch.vue'
import myPull from '@/mixins/myPull.js'
import ComList from '@//components/list/ComList.vue'
import UserListCard from '../components/cards/UserListCard.vue'
import FixedAddIcon from '@//components/common/FixedAddIcon.vue'
import BaseModal from '@//components/base/BaseModal.vue'
import BaseInput from '@//components/base/BaseInput.vue'
// import BaseList from '@/components/base/BaseList.vue'
import { AddValueInObject } from '@/wxutil/list'
import BaseDropdown from '@/components/base/BaseDropdown.vue'
import BaseBackTop from '@/components/base/BaseBackTop.vue'
export default {
  components: {
    BaseNavbar,
    BaseSearch,
    ComList,
    UserListCard,
    FixedAddIcon,
    BaseModal,
    BaseInput,
    // BaseList,
    BaseDropdown,
    BaseBackTop,
  },
  data() {
    return {
      scrollTop: 0,
      title: '用户管理',
      isShowModal: false,
      isDivide: false,
      isEnableUser: false,
      isBindWx: false,
      percentage: '', //设置分成
      searchValue: '', //搜索框value
      modalContent: '是否禁用该用户？',
      selectItem: {},
      isUnbindWx: false, //是否是解绑微信
      hotelName: '', //搜索
      total: 0,
      optionsList: [
        {
          title: '全部',
          options: [{ label: '全部', value: 0, status: 0 }],
          value: 0,
        },
      ],
      select_role_id: 0,
      index: 0,
    }
  },
  methods: {
    onPageScroll(e) {
      this.scrollTop = e.scrollTop
    },
    change(item) {
      this.optionsList = item
      let index = item[0].value
      this.select_role_id = item[0].options[index].status
      // console.log("🚀 ~ this.status", this.status);
      this.refresh()
    },
    goAddUser() {
      uni.navigateTo({ url: '/pagesB/user/UserAdd?from=add' })
    },

    //确认设置分成
    async setDivide() {
      try {
        let data = {
          role_id: this.selectItem.role_id,
          per_user_id: this.selectItem.id,
          per: this.percentage,
        }
        await this.$u.api.divideMoney(data)
        this.isShowSuccess('设置成功', 0, () => this.refresh())
        this.percentage = ''
      } catch (error) {
        console.log(error)
      }

    },
    //确认 设置用户禁用和启用
    async enableUser() {
      try {
        let data = {
          id: this.selectItem.id,
          user_status: this.selectItem.user_status == 1 ? 0 : 1,
        }
        await this.$u.api.enableUser(data)
        this.isShowSuccess('设置成功', 0, () => this.refresh())
      } catch (error) {
        console.log(error)
      }

    },
    //解绑微信
    async unBindWx() {
      try {
        if (!this.isUnbindWx) return
        let data = {
          id: this.selectItem.id,
        }
        await this.$u.api.unBingWX(data)
        this.isShowSuccess('解绑成功', 0, () => this.refresh())
      } catch (error) {
        console.log(error)
      }

    },
    async getList(page, done) {
      try {
        let data = {
          page,
          user_login: this.searchValue,
          select_role_id: this.select_role_id,
        }
        let res = await this.$u.api.getMyUserList(data)
        if (page == 1) {
          this.total = res.total || 0
        }
        done(res.data)
      } catch (error) {
        console.log(error)
      }

    },
    searchChange(val) {
      this.hotelName = val
      this.search(val)
    },
    search(val) {
      this.searchValue = val
      let list = AddValueInObject(this.vServerList.userList, val)
      this.$u.vuex(`vServerList.userList`, list)
      this.refresh()
    },

    popShow(item, from) {
      this.selectItem = item
      this.modalContent = ''

      if (from == 'setDivide') {
        this.isDivide = true

        this.isEnableUser = false
        this.isBindWx = false
      } else if (from == 'enableUser') {
        this.modalContent =
          item.user_status == 1 ? '是否禁用该用户?' : '是否解禁该用户'
        this.isEnableUser = true

        this.isDivide = false
        this.isBindWx = false
      } else if (from == 'bindWx') {
        this.isUnbindWx = item?.openid ? true : false
        this.isBindWx = true

        this.isDivide = false
        this.isEnableUser = false
      }
      this.isShowModal = true
    },
    confirmModal() {
      if (this.isDivide) {
        this.setDivide()
      } else if (this.isBindWx) {
        this.unBindWx()
      } else if (this.isEnableUser) {
        this.enableUser()
      }
    },
    clickCrad(item) {
      uni.navigateTo({ url: `/pagesB/user/UserAdd?from=edit&id=${item.id}` })
    },
    many(item) {
      uni.navigateTo({
        url: `/pagesB/finance/FinanceList?from=userList&user_login=${item.user_login}&user_id=${item.id}`,
      })
    },
  },
  onLoad(options) {
    let that = this
    if (this.vRoleList.length > 0) {
      this.vRoleList.map((item, i) => {
        this.optionsList[0].options.push({
          label: item.role_name,
          value: i + 1,
          status: item.id,
        })
      })
    }
    this.refresh()
  },
  onShow() {
    /*  #ifndef H5 */
    let pages = getCurrentPages()
    let currPage = pages[pages.length - 1] // 当前页
    if (currPage.data.isDoRefresh == true) {
      // 是否刷新
      currPage.data.isDoRefresh = false
      this.refresh()
    }
    /*#endif */
    /*  #ifdef H5 */
    if (this.vCurrPage.isDoRefresh == true) {
      // 是否刷新
      this.vCurrPage.isDoRefresh = false
      this.refresh()
    }
    /*#endif */
  },
  mixins: [myPull()],
}
</script>

<style lang="scss">
page {
  background-color: $pageBgColor;
}
</style>

<style lang="scss" scoped></style>
