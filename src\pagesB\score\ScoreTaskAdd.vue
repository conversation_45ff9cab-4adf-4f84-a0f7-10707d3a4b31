<template>
  <view>
    <BaseNavbar :title="title" />
    <view class="content">
      <view class="content-box">
        <view class="content-box-title">
          <view>任务类型：</view>
        </view>
        <BaseInput
          @onClick="showTaskType = true"
          :disabled="true"
          placeholder="请选择任务类型"
          v-model="info.task_cat"
        />
      </view>
      <view class="content-box">
        <view w class="content-box-title">
          <view>任务名称：</view>
        </view>
        <BaseInput placeholder="请输入任务名称" v-model="info.name" />
      </view>
      <view class="content-box">
        <view w class="content-box-title">
          <view>任务积分：</view>
        </view>
        <BaseInput placeholder="请输入任务积分" v-model="info.point_number" />
      </view>
      <view class="content-box">
        <view class="content-box-title">
          <view>任务次数：</view>
        </view>
        <BaseInput placeholder="请输入任务次数" v-model="info.task_times" />
      </view>
      <view class="content-box">
        <view class="content-box-title task-status">
          <view>任务状态：</view>
          <u-switch v-model="taskStatusChecked"></u-switch>
        </view>
      </view>
      <!-- 保存按钮 -->
      <view class="footer">
        <view class="footer__buttom">
          <BaseButton @onClick="save">{{ btnTitle }}</BaseButton>
        </view>
      </view>
    </view>

    <!--popup 任务类型-->
    <BasePopup :show.sync="showTaskType" :safeArea="true">
      <TypeSelectPopup
        :title="`选择${vPointName}类型`"
        :list="taskTypeList"
        @comfirm="comfirmType"
      />
    </BasePopup>
  </view>
</template>

<script>
import BaseButton from "../../components/base/BaseButton.vue";
import BaseInput from "../../components/base/BaseInput.vue";
import BaseNavbar from "@/components/base/BaseNavbar.vue";
import BasePopup from "../../components/base/BasePopup.vue";
import TypeSelectPopup from "../../components/common/TypeSelectPopup.vue";
export default {
  components: { BaseNavbar, BaseInput, BaseButton, BasePopup, TypeSelectPopup },
  data() {
    return {
      title: "新增任务",
      fromData: "",
      info: {},
      taskStatusChecked: false,
      btnTitle: "添加",
      taskTypeList: [], //任务类型
      showTaskType: false,
    };
  },
  methods: {
    comfirmType(item, indexlList) {
      this.showTaskType = false;
      this.info.task_cat = item;
    },
    save() {
      let data = {
        task_cat: this.info.task_cat,
        name: this.info.name,
        point_number: this.info.point_number,
        task_times: this.info.task_times,
        sort: this.info.sort,
        status: this.taskStatusChecked ? 1 : 0,
        ids: this.info.id,
      };

      this.$u.api.addTask(data).then((res) => {
        this.isShowSuccess("保存成功", 1, () => {}, true);
      });
    },
  },
  onLoad(opt) {
    this.taskTypeList = this.vTaskType.map((item) => item.detail_name);
    if (opt?.from) {
      this.fromData = opt.from;
      if (opt.from == "add") {
        this.title = "新增任务";
        this.btnTitle = "添 加";
      } else if (opt.from == "edit") {
        this.title = "编辑任务";
        this.btnTitle = "编 辑";
        this.info = JSON.parse(opt.data);
        this.taskStatusChecked = this.info.status == 1;
      }
    }
  },
};
</script>

<style lang="scss" scoped>
.content {
  padding: 20rpx 30rpx;
  &-box {
    margin-bottom: 20rpx;
    &-title {
      display: flex;
      align-items: center;
      color: $textBlack;
      font-size: $font-size-middle;
      font-weight: bold;
      margin-bottom: 20rpx;
    }
    .task-status {
      justify-content: space-between;
    }
  }
  .footer {
    margin-top: 100rpx;
  }
}
</style>