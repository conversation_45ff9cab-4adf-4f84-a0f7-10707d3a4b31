<template>
  <view class="base-dropdown flexRowBetween border-bottom">
    <u-dropdown class="u-dropdown" :active-color="activeColor" :inactive-color="inactiveColor" :height="height"
      title-size="26" ref="uDropdown" @open="open" @close="close">
      <u-dropdown-item v-for="(item, i) in optionsList" :key="i" @change="change" v-model="item.value"
        :title="item.title" :options="item.options"></u-dropdown-item>
    </u-dropdown>
    <view class="num"> 数量：{{ num }} </view>
  </view>
</template>

<script>
export default {
  name: "BaseDropdown",
  props: {
    optionsList: {
      type: Array,
      default: function () {
        return [];
      },
    },
    num: { type: [Number, String], default: 0 },
  },
  data() {
    return {
      activeColor: "#0EADE2",
      inactiveColor: "#333333",
      height: "78",
      borderBottom: true,
      closeIndex: 0,
    };
  },
  methods: {
    open(index) {
      this.$refs.uDropdown.highlight(index);
    },
    close(index) {
      this.closeIndex = index;
      this.$refs.uDropdown.highlight(index);
    },
    change(index) {
      this.optionsList[this.closeIndex].title =
        this.optionsList[this.closeIndex].options[index].label;
      this.$emit("change", this.optionsList);
    },
  },
  options: { styleIsolation: "shared" }, //组件必须加,才能修改内部样式
};
</script>

<style lang="scss" scoped>
.border-bottom {
  background: $uni-bg-color;
  // padding-right: 50rpx;
  width: 100%;
  box-sizing: border-box;
  border-bottom: 2rpx solid #fafafa
}

.u-dropdown {
  width: 50%;
  box-sizing: border-box;

  ::v-deep .u-dropdown__content {
    width: 100vw;
  }
}

.num {
  width: 50%;
  padding-left: 20%;
  // text-align:center;
  color: $themeComColor;
  font-size: $font-size-base;
}
</style>