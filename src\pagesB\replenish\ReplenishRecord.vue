<template>
  <view>
    <BaseNavbar :title="title" />
    <view class="bg">
      <image class="bg_img" src="@/pagesB/static/img/replenishRecordBg.png" />
      <view class="bg_content flexColumnBetween">
        <view class="name">{{
          umDetail.hotelName || "暂无" + vPointName + "昵称"
        }}</view>
        <view class="textMaxTwoLine">{{
          umDetail.addressDetail || "暂无具体位置"
        }}</view>
        <view class="flexRowBetween">
          <view>设备编号：{{ device_sn }}</view>

          <view class="bind">
            <block v-if="umDetail.hotelId > 0">
              <image
                class="bind_icon"
                src="@/pagesB/static/img/icon/isBind_icon.png"
              />
              <view class="theme-color">已绑定</view>
            </block>
            <block v-else>
              <image
                class="bind_icon"
                src="@/pagesB/static/img/icon/noBind_icon.png"
              />
              <view class="text-warn">未绑定</view>
            </block>
          </view>
        </view>
      </view>
    </view>
    <view class="type">
      <view>货道</view>
      <view>商品名称</view>
      <view>数量</view>
    </view>
    <ComList :loading-type="loadingType">
      <ReplenishRecordCard
        v-for="item in listData"
        :key="item.id"
        :info="item"
      />
    </ComList>
  </view>
</template>

<script>
import BaseNavbar from "@/components/base/BaseNavbar.vue";
import ComList from "@/components/list/ComList.vue";
import ReplenishRecordCard from "../components/cards/ReplenishRecordCard.vue";
import myPull from "@/mixins/myPull.js";
export default {
  components: { BaseNavbar, ComList, ReplenishRecordCard },
  mixins: [myPull()],
  data() {
    return {
      title: "补货记录",
      device_sn: "",
      umDetail: {}, //设备详情
    };
  },
  methods: {
    getList(page, done) {
      let data = { device_sn: this.device_sn, limit: 10 };
      this.$u.api.bhLogList(data).then((res) => {
        if (!this.umDetail?.device_sn) {
          this.umDetail = res.data.umDetail;
        }
        done(res.data.log_list);
      });
    },
  },
  onLoad(opt) {
    this.device_sn = opt?.device_sn;
    this.refresh();
  },
};
</script>

<style lang="scss"  >
page {
  background-color: $pageBgColor;
}
</style>
<style lang="scss" scoped>
.bg {
  position: relative;
  height: 276rpx;
  &_img {
    width: 100%;
    height: 100%;
  }
  &_content {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    color: $textWhite;
    font-size: $font-size-base;
    padding: 50rpx 36rpx;
    .name {
      font-size: $font-size-middle;
      font-weight: bold;
    }
  }
}
.type {
  height: 80rpx;
  display: flex;
  color: $textBlack;
  font-size: $font-size-base;
  font-weight: bold;
  background-color: $uni-bg-color;
  padding: 0 30rpx;
  > view {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
  }
}
</style>