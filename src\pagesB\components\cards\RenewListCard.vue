·<template>
  <view class="card" @click="onCheck">
    <view class="card_left">
      <BaseCheck :checked.sync="info.isCheck" />
    </view>
    <view class="card_right">
      <view class="item">
        <view>
          <text class="title">设备编号：</text>
          <text class="textBlack">{{ info.device_sn }}</text>
        </view>
        <view class="remain_money">
          <text>剩余天数：</text>
          <text class="themeColor">{{ remaicnDay }}</text>
        </view>
      </view>
      <view class="item">
        <view>
          <text class="title">拥有者：</text>
          <text class="textBlack">{{ info.user_login }}</text>
        </view>
        <view class="remain_money">
          <text>年费金额：</text>
          <text class="themeColor">{{ info.money }}</text>
        </view>
      </view>

      <view class="item">
        <view>
          <text class="title">开始时间：</text>
          <text class="textBlack">{{ info.start_date }}</text>
        </view>
      </view>
      <view class="item">
        <view>
          <text class="title">有效期限：</text>
          <text class="textBlack">{{ info.due_date }}</text>
        </view>
        <view class="themeColor" @click.stop="goRecord"> 续费记录 </view>
      </view>
    </view>
  </view>
</template>

<script>
import BaseButton from "@/components/base/BaseButton.vue";
import BaseCheck from "@/components/base/BaseCheck.vue";
import { navigateToReplenishList } from '@/wxutil/navgate.js'
export default {
  components: { BaseButton, BaseCheck },
  name: "RenewListCard",
  props: {
    info: { type: Object, default: {} },
  },
  computed: {
    remainDay() {
      let startTime = new Date(this.info.start_date);
      let endTime = new Date(this.info.due_date);
      return parseInt(Math.abs(endTime - startTime) / 1000 / 60 / 60 / 24);
    },
  },
  methods: {
    onCheck() {
      this.$emit("onCheck");
    },
    async goRecord() {
      try {
        await navigateToReplenishList("/pagesB/renew/RenewRecord");
        // 导航成功后的代码
      } catch (err) {
        // 导航失败后的代码
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.card {
  display: flex;
  align-items: center;
  padding: 20rpx;
  &_right {
    flex: 1;
    > view {
      display: flex;
      justify-content: space-between;
      font-size: $font-size-base;
      color: $textGray;
      margin-bottom: 22rpx;
      :last-child {
        margin-bottom: 0;
      }
      .title {
        text-align: right;
        width: 136rpx;
        display: inline-block;
      }
      .themeColor {
        color: $themeComColor;
      }

      .textBlack {
        color: $textBlack;
      }
    }
    .remain_money {
      width: 190rpx;
      display: flex;
      justify-content: space-between;
    }
  }
}
</style>