<template>
  <view class="card">
    <view class="content flexRowBetween" :class="show ? 'border-bottom' : ''">
      <view class="left">
        <view class="name">{{ info.realname }}</view>
        <view class="device_sn">
          <text>提现账号：</text>
          <text user-select="true">{{
      info.way == 2 ? info.bank_card : info.alipay_num
    }}</text>
        </view>
        <view class="time">
          {{ $u.timeFormat(info.create_time * 1000, "yy-mm-dd hh:MM:ss") }}
        </view>
        <view class="device_sn">
          <text>提现备注：</text>
          <text user-select="true">{{ info.apply_remarks || "暂无" }}</text>
        </view>
      </view>
      <view class="right">
        <view class="right-check" v-if="isShowCheckInfo" @click.stop="isShowCheckModal = true">修改</view>
        <view>{{ wayName }}</view>
        <view class="money">{{ info.money }}元</view>
        <view class="status flexRowAllCenter" @click="show = !show">
          <view>{{ statusName }}</view>
          <view class="arrow" :class="show ? 'rotate' : ''">
            <BaseIcon name="arrow-right" color="#0EADE2" size="28" />
          </view>
        </view>
      </view>
    </view>

    <view class="info" v-if="show">
      <view v-if="info.status != 0">
        <view v-if="info.check_time">
          更新时间：{{
      $u.timeFormat(info.check_time * 1000, "yy-mm-dd hh:MM:ss")
    }}
        </view>
        <block v-if="info.status == 1 || info.status == 3">
          <view>备注：{{ info.check_remarks || "暂无" }}</view>
        </block>
        <block v-if="info.status == 2">
          <view>备注：{{ info.pay_remarks || "暂无" }}</view>
        </block>
        <block v-else-if="info.status == -1">
          驳回理由：{{ info.check_remarks }}
        </block>
      </view>
      <view class="flexRowBetween" :style="{ marginTop: '20rpx' }" v-if="info.status != -1 && info.status != 2">
        <block v-if="info.status == 0">
          <BaseButton type="default" width="310" @onClick.stop="showModalFun(false, 'check')">
            驳回申请
          </BaseButton>
          <BaseButton width="310" type="primary" @onClick.stop="showModalFun(true, 'check')">
            审核通过
          </BaseButton>
        </block>
        <block v-else-if="info.status == 1">
          <BaseButton type="primary" @onClick.stop="showModalFun(true, 'pay')" width="690">
            确认打款
          </BaseButton>
        </block>
        <block v-else-if="info.status == 3 || info.status == 4">
          <BaseButton type="primary" @onClick.stop="showModalFun(true, 'pay')" width="690">
            重新打款
          </BaseButton>
        </block>
      </view>
    </view>
    <BaseModal :show.sync="isShowModal" @confirm="confirmModal">
      <view slot="default">
        <BaseInput height="160" type="textarea" :placeholder="placeholderName" v-model="reason" />
      </view>
    </BaseModal>
    <BaseModal :show.sync="isShowCheckModal" @confirm="confirmCheck" title="请填写修改信息">
      <view slot="default">
        <view>
          <BaseInput v-model="realname" placeholder="请输入姓名" />
        </view>
        <view v-if="info.way != 2" :style="{ marginTop: '20rpx' }">
          <BaseInput v-model="account" placeholder="请输入账号" />
        </view>
      </view>
    </BaseModal>
  </view>
</template>

<script>
import BaseButton from "@/components/base/BaseButton.vue";
import BaseIcon from "@/components/base/BaseIcon.vue";
import BaseModal from "@/components/base/BaseModal.vue";
import BaseInput from "@/components/base/BaseInput.vue";
export default {
  components: { BaseIcon, BaseButton, BaseModal, BaseInput },
  props: { info: { type: Object, default: {} } },
  data() {
    return {
      show: false,
      isShowModal: false,
      reason: "", //原因备注
      isExamine: false, //审核通过true 或者 驳回false
      isShowCheckModal: false,
      realname: "",
      account: "",
      hanldeType: "check",
    };
  },
  computed: {
    statusName() {
      let status = this.info.status;
      if (status == 0) {
        return "审核中";
      } else if (status == 1) {
        return "审核通过";
      } else if (status == 2) {
        return "付款成功";
      } else if (status == 3) {
        return "付款失败";
      } else if (status == -1) {
        return "审核失败";
      }
      return "异常";
    },
    wayName() {
      let wayType = this.info.way;
      if (wayType == 1) {
        return "支付宝";
      } else if (wayType == 2) {
        return "微信";
      } else if (wayType == 3) {
        return "银行卡";
      } else {
        return "异常";
      }
    },
    isShowCheckInfo() {
      return (
        (this.info.status == 1 || this.info.status == 3) && this.info.way != 2
      );
    },
    placeholderName() {
      if (this.hanldeType == "check") {
        return `请输入${this.isExamine ? "通过" : "拒绝"}原因`;
      } else if (this.hanldeType == "pay") {
        return `请输入打款原因`;
      }
      return `请输入备注`;
    },
  },
  methods: {
    showModalFun(flag, hanldeType) {
      this.isShowModal = true;
      this.isExamine = flag;
      this.hanldeType = hanldeType;
    },
    async confirmModal() {
      let rtn = null;
      let data = {
        id: this.info.id,
      };
      if (this.hanldeType == "check") {
        data["remake"] = this.reason; //备注

        if (this.isExamine) {
          rtn = await this.$u.api.checkTrue(data);
        } else {
          rtn = await this.$u.api.checkFalse(data);
        }
      } else if (this.hanldeType == "pay") {
        data["pay_remarks"] = this.reason; //备注
        rtn = await this.$u.api.financeConfirmPay(data);
      }
      rtn &&
        this.isShowSuccess("操作成功", 0, () => {
          this.$emit("refresh");
        });
    },
    async confirmCheck() {
      try {
        let data = {
          realname: this.realname,
          id: this.info.id,
          way: this.info.way,
        };
        if (this.info.way != 2) {
          data["account"] = this.account;
        }
        await this.$u.api.modifyAccount(data);
        this.isShowSuccess("修改成功", 0, () => {
          this.$emit("refresh");
          this.checkInfo = {};
        });
      } catch (error) {
        console.log('错误信息', err)
      }

    },
  },
};
</script>

<style lang="scss" scoped>
.border-bottom {
  border-bottom: 2rpx solid $dividerColor;
  box-sizing: border-box;
}

.content {
  padding: 20rpx 20rpx 0;
  transition: all 0.5s;

  .left {
    flex: 1;

    >view {
      margin-bottom: 20rpx;
    }

    .name {
      color: $textBlack;
      font-size: $font-size-middle;
      font-weight: bold;
    }

    .device_sn {
      font-size: $font-size-base;

      >text {
        &:first-child {
          color: $textDarkGray;
        }

        &:last-child {
          color: $textBlack;
        }
      }
    }

    .time {
      color: $textDarkGray;
      font-size: $font-size-small;
    }
  }

  .right {
    flex-shrink: 0;

    .money {
      color: #ef0000;
      font-size: $font-size-middle;
      margin-top: 20rpx;
      margin-bottom: 26rpx;
    }

    .status {
      color: $themeComColor;
      font-size: $font-size-base;
    }

    .arrow {
      transition: all 0.4s;
      margin-left: 6rpx;
    }

    .rotate {
      transform: rotate(90deg);
    }

    &-check {
      color: $themeComColor;
      margin-bottom: 10rpx;
    }
  }
}

.info {
  padding: 30rpx 20rpx;
  color: $textBlack;

  view {
    line-height: 1.8;
  }
}

.check-button {
  display: flex;
  justify-content: flex-end;
  padding: 20rpx;
}
</style>