<template>
  <view class="goods-list-card flexRowBetween" @click="onClick">
    <view v-if="isShowCheck">
      <BaseCheck :checked.sync="info.isCheck" />
    </view>
    <image class="img" :src="info.original_img || vDefaultIcon" />
    <view class="right flexColumnBetween">
      <view class="name">{{ info.goods_name }}</view>
      <view class="post flexRowBetween">
        <view>{{ info.tempower == -1 ? "自定商品" : "上级商品" }}</view>
        <view>{{ info.storage_type || "" }}</view>
      </view>
      <view class="purchase flexRowBetween">
        <view
          ><text>进货价：</text
          ><text class="textRed">￥{{ info.cost_price }}</text></view
        >
        <view
          ><text>市场价：</text
          ><text class="textRed">￥{{ info.market_price }}</text></view
        >
      </view>
    </view>
  </view>
</template>

<script>
import BaseCheck from "@/components/base/BaseCheck.vue";
export default {
  components: { BaseCheck },
  name: "GoodsListCard",
  props: {
    info: {
      type: Object,
      default: function () {
                return {};
            }
    },
    isShowCheck: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {};
  },
  methods: {
    onClick() {
      this.$emit("selectItem");
    },
  },
};
</script>

<style lang="scss" scoped>
.goods-list-card {
  box-sizing: border-box;
  padding: 20rpx;
  background-color: $uni-bg-color;
  margin-bottom: 30rpx;
  border-radius: $cardRadius;
  .img {
    width: 150rpx;
    height: 150rpx;
    border-radius: $imgRadius;
  }
  .right {
    flex: 1;
    margin-left: 20rpx;
    height: 150rpx;
    font-size: $font-size-small;
    .name {
      color: $textBlack;
      font-weight: bold;
      font-size: $font-size-middle;
    }
    .post {
      color: $textBlack;
    }
    .purchase {
      color: $textDarkGray;
    }
    .textRed {
      color: $mainRed;
    }
  }
}
</style>