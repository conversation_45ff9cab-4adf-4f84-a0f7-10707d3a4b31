<template>
  <view>
    <BaseNavbar title="广告管理" />
    <BaseSearch @search="search" placeholder="请输入广告名称"  listType="advertsMange" />
    <ComList :loadingType="loadingType">
      <AdvertsManageCard
        v-for="item in listData"
        :key="item.id"
        :info="item"
        :isShowCheck="isShowCheck"
        @refresh="refresh"
      />
    </ComList>
    <FixedAddIcon @onAdd="goAdd" />
  </view>
</template>

<script>
import BaseNavbar from '@/components/base/BaseNavbar.vue'
import ComList from '@/components/list/ComList.vue'
import AdvertsManageCard from '../components/cards/AdvertsManageCard.vue'
import myPull from '@/mixins/myPull'
import FixedAddIcon from '@/components/common/FixedAddIcon.vue'
import BaseSearch from '../../components/base/BaseSearch.vue'
import { AddValueInObject } from '@/wxutil/list'
export default {
  components: {
    BaseNavbar,
    ComList,
    AdvertsManageCard,
    FixedAddIcon,
    BaseSearch,
  },
  mixins: [myPull()],
  data() {
    return {
      ad_desc: '', //广告名称
      isShowCheck: false,
      hotelName:''
    }
  },
  methods: {
    getList(page, done) {
      let data = {
        page,
        ad_desc: this.ad_desc,
      }
      this.$u.api
        .getAdvertList(data)
        .then((res) => {
          done(res.data)
        })
        .catch((err) => {
          console.log('错误信息', err)
        })
    },
    goAdd() {
      uni.navigateTo({ url: '/pagesB/advertsManage/AdverstAdd?from=add' })
    },
    searchChange(val){

      this.hotelName=val

      this.search(val)

    },
    search(e) {
      this.ad_desc = e
      let list=AddValueInObject(this.vServerList.advertsMange,e)
      this.$u.vuex(`vServerList.advertsMange`, list);
      this.refresh()
    },
  },
  onLoad(opt) {
    if (opt?.from === 'deivce_adverts') {
      this.isShowCheck = true
    }
    this.refresh()
  },
  onShow() {
    /*  #ifndef H5 */
    let pages = getCurrentPages()
    let currPage = pages[pages.length - 1] // 当前页
    if (currPage.data.isDoRefresh == true) {
      // 是否刷新
      currPage.data.isDoRefresh = false
      this.refresh()
    }
    /*#endif */
    /*  #ifdef H5 */
    if (this.vCurrPage.isDoRefresh == true) {
      // 是否刷新
      this.vCurrPage.isDoRefresh = false
      this.refresh()
    }
    /*#endif */
  },
}
</script>

<style lang="scss">
page {
  background-color: $pageBgColor;
}
</style>
<style lang="scss" scoped></style>
