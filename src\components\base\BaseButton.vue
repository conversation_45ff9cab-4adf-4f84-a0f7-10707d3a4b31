<template>
  <!--  #ifndef H5-->
  <u-button
    class="defaultStyle"
    :class="[type]"
    :custom-style="customStyle"
    :ripple="true"
    hover-class="none"
    :shape="shape"
    @click="onClick"
  >
    <slot></slot>
  </u-button>
  <!--   #endif -->
  <!--  #ifdef H5-->
  <button :class="type" :style="customStyle" @click="onClick">
    <slot></slot>
  </button>
  <!--   #endif -->
</template>

<script>
export default {
  name: 'BaseButton',
  props: {
    width: {
      type: [Number,String], //按钮宽度
      default: 'auto',
    },
    type: {
      type: String, //按钮类型primary default
      default: 'primary',
    },
    shape: {
      type: String, //按钮圆角 circle square
      default: 'square',
    },
    /*  #ifdef H5*/
    padding: {
      type: [Number, String],
      default: 10,
    },
    fontSize: {
      type: [Number, String],
      default: 30,
    },
    /* #endif */
  },
  data() {
    return {
      customStyle: {
        width: this.width == 'auto' ? this.width : this.width + 'rpx',
        /*  #ifdef H5*/
        padding: `0 ${this.padding}rpx`,
        fontSize: `${this.fontSize}rpx`,
        /* #endif */
      },
    }
  },
  methods: {
    onClick() {
      this.$emit('onClick')
    },
  },
  options: { styleIsolation: 'shared' }, //组件必须加,才能修改内部样式
}
</script>

<style lang="scss" scoped>
// .defaultStyle {
//  ::v-deep button {
//     height: 88rpx;
//     font-size: $font-size-xlarge;
//     &::after {
//       border: none;
//     }
//   }
// }
// .primary {
//  ::v-deep button {
//     color: $textWhite;
//     background: linear-gradient(268deg, #206bc5 0%, #0eade2 99%);
//   }
// }
// .default {
//  ::v-deep button {
//     color: $textBlack;
//     background: $uni-bg-color;
//     border: 2rpx solid #c8c8c8;
//   }
// }
// .theme {
//  ::v-deep button {
//     color: $textWhite;
//     background: $themeComColor;
//     font-size: $font-size-middle;
//     height: 78rpx;
//   }
// }

.defaultStyle {
  /*  #ifdef H5*/
  height: 88rpx;
  font-size: $font-size-xlarge;

  &::after {
    border: none;
  }
  /* #endif */
  ::v-deep .u-btn {
    height: 88rpx;
    font-size: $font-size-xlarge;

    &::after {
      border: none;
    }
  }
}

.primary {
  /*  #ifdef H5*/
  color: $textWhite;
  background: linear-gradient(268deg, #206bc5 0%, #0eade2 99%);
  /* #endif */
  ::v-deep .u-btn {
    color: $textWhite;
    background: linear-gradient(268deg, #206bc5 0%, #0eade2 99%);
  }
}
.eixt {
  /*  #ifdef H5*/
  color: $textWhite;
  background: linear-gradient(268deg, #ff2919 0%, #ff5223 99%);
  /* #endif */
  ::v-deep .u-btn {
    color: $textWhite;
    background: linear-gradient(268deg, #ff2919 0%, #ff5223 99%);
  }
}
.default {
  /*  #ifdef H5*/
  color: $textBlack;
  background: $uni-bg-color;
  border: 2rpx solid #c8c8c8;
  /* #endif */
  ::v-deep .u-btn {
    color: $textBlack;
    background: $uni-bg-color;
    border: 2rpx solid #c8c8c8;
  }
}

.theme {
  /*  #ifdef H5*/
  color: $textWhite;
  background: $themeComColor;
  font-size: $font-size-middle;
  height: 78rpx;
  /* #endif */
  ::v-deep .u-btn {
    color: $textWhite;
    background: $themeComColor;
    font-size: $font-size-middle;
    height: 78rpx;
  }
}
</style>
