<template>
  <view>
    <BaseNavbar :title="title" />
    <view class="content">
      <view>
        <view>点位名称</view>
        <view><BaseInput placeholder="请输入点位名称" /></view>
      </view>
      <view>
        <view>详细位置</view>
        <view><BaseInput  placeholder="请输入详细位置" /></view>
      </view>
      <view>
        <view>设备编号</view>
        <view><BaseInput placeholder="请输入设备编号" /></view>
      </view>
      <block v-if="isAdd">
        <view>
          <view>广告区</view>
          <view><BaseInput placeholder="请输入广告区" /></view>
        </view>
        <view>
          <view>拥有者</view>
          <view><BaseInput placeholder="请输入拥有者" /></view>
        </view>
      </block>

      <view>
        <view>投放周期</view>
        <view class="cycle">
          <view>
            <view>选择开启屏幕时间(年/月/日)</view>
            <view><TimeSelect placeholder="请选择开始时间" /></view>
          </view>
          <view>
            <view>选择开启屏幕时间(时/分/秒)</view>
            <view><TimeSelect placeholder="请选择开启屏幕时间" /></view>
          </view>
          <view>
            <view>选择关闭屏幕时间(年/月/日)</view>
            <view><TimeSelect placeholder="请选择关闭屏幕时间" /></view>
          </view>

          <view>
            <view>选择关闭屏幕时间(时/分/秒)</view>
            <view><TimeSelect placeholder="请选择关闭屏幕时间" /></view>
          </view>
        </view>
      </view>
      <view class="btn">
        <BaseButton type="primary">确认</BaseButton>
      </view>
    </view>
    <SafeBlock :height="30" />
  </view>
</template>

<script>
import BaseButton from "@/components/base/BaseButton.vue";
import BaseInput from "@/components/base/BaseInput.vue";
import BaseNavbar from "@/components/base/BaseNavbar.vue";
import TimeSelect from "@/components/common/TimeSelect.vue";
import SafeBlock from "@/components/common/SafeBlock.vue";
export default {
  components: { BaseNavbar, BaseInput, TimeSelect, BaseButton, SafeBlock },
  data() {
    return {
      title: "新增屏幕",
      fromData: "",
      isEdit: false,
      isAdd: false,
    };
  },
  onLoad(opt) {
    if (opt?.from) {
      this.fromData = opt.from;
      if (this.fromData == "add") {
        //新增来的
        this.isAdd = true;
      } else if (this.fromData == "edit") {
        //编辑来的
        this.title = "编辑屏幕";
        this.isEdit = true;
      }
    }
  },
};
</script>

<style lang="scss" scoped>
.content {
  padding: 30rpx;
  > view {
    margin-bottom: 50rpx;
    > view {
      &:first-child {
        color: $textBlack;
        font-size: $font-size-middle;
        font-weight: bold;
        margin-bottom: 20rpx;
      }
    }
    &:last-child {
      margin-bottom: 0;
    }
  }
  .cycle {
    margin-top: 30rpx;
    > view {
      margin-top: 30rpx;
      > view {
        &:first-child {
          margin-bottom: 16rpx;
        }
      }
    }
  }
}
</style>