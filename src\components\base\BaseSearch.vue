<template>
  <view class="searchContent searchContent-width flexRowBetween" :style="{ '--searchWidth': width + 'vw' }"
    @click="click">
    <view class="searchInput">
      <view class="searchInput-box">
        <view class="searchIcon" @click.stop="leftClickIcon()">
          <u-icon name="search" class="searchIcon" color="#999" size="30"></u-icon>
        </view>

        <u-search class="searchValue" placeholder-color="#999999" bg-color="#F2F2F2" height="80"
          :placeholder="placeholder" :shape="shape" :clearabled="clear" :animation="true" :show-action="false"
          :disabled="disabled" v-model="keyword" @search="search" @focus="focus" @blur="clears"
          search-icon=""></u-search>
        <view class="search-box" v-if="isShowSerch && isShow && vServerList[listType].length > 0">
          <!-- <view class="search-title">最近在搜</view> -->
          <view class="search-list">
            <view v-for="(n, i) in vServerList[listType]" class="list-item" :key="i" @click="searchClick(n)">
              <view class="list-txt">{{ n }}</view>
              <view class="dis-top" @click.stop="exit(n)">
                X
              </view>
            </view>
          </view>
        </view>
      </view>

      <BaseIcon v-if="showScan" name="scan" class="scan" color="#999" @onClick="onClickScan" />
    </view>
    <view class="img flexRowAllCenter" v-if="typeImg">
      <image v-for="item in imgIcon[typeImg]" class="iconStyle" :src="item.url" :key="item.id" :style="item.style"
        @click.stop="onClickIconImg(item.id)" />
    </view>
  </view>
</template>

<script>
import { getUrlParams, getUrlDynamicData } from '../../common/tools/utils'
import { deleteValueInObject } from '@/wxutil/list'
import BaseIcon from './BaseIcon.vue'
export default {
  components: { BaseIcon },
  name: 'BaseSearch',
  props: {
    placeholder: { type: String | Object, default: '请输入搜索内容' }, //提示文字
    shape: { type: String, default: 'square' }, //是否圆角
    clear: { type: Boolean, default: false }, //显示清除控件
    typeImg: { type: String | Object, default: '' }, //右侧icon类型
    showScan: { type: Boolean, default: false }, //是否显示扫码icon
    width: { type: [Number, String], default: '100' },
    disabled: { type: Boolean, default: false },
    hotelName: { type: String, default: '' },
    listType: { type: String, default: 'analysisList' },
    isShowSerch: { type: Boolean, default: true },//是否加搜索提示
  },
  data() {
    return {
      imgIcon: {
        service: [
          {
            url: require('@/static/img/icon/service-icon.png'),
            id: 0,
            style: 'width:44rpx;height:44rpx',
          },
          {
            url: require('@/static/img/icon/notice-icon.png'),
            id: 1,
            style: 'width: 40rpx;height:44rpx',
          },
        ],
        place: [
          {
            url: require('@/static/img/icon/location-icon.png'),
            id: 0,
            style: 'width:36rpx;height:44rpx',
          },
          {
            url: require('@/static/img/icon/screen-icon.png'),
            id: 1,
            style: 'width:38rpx;height:38rpx',
          },
        ],
        screen: [
          {
            url: require('@/static/img/icon/screen-icon.png'),
            id: 0,
            style: 'width:38rpx;height:38rpx',
          },
        ],
      },
      keyword: '', // 查询数据
      isShow: false
    }
  },
  mounted() {
    this.keyword = this.hotelName
  },
  watch: {
    hotelName: {
      handler: function () {
        if (this.hotelName) {
          // 检查 this.leftImg 是否存在
          this.keyword = this.hotelName
        } else {
          this.keyword = ''
        }
      },
      deep: true,
      immediate: true,
    },
  },
  options: { styleIsolation: 'isolated' }, //组件必须加,才能修改内部样式
  methods: {
    leftClickIcon(){
      // console.log('点击搜索')
      if(this.keyword){
        this.$emit('search', this.keyword)
      }
      

      // this.$emit('leftClickIcon', this.keyword)
    },
    onClickScan() {
      uni.scanCode({
        onlyFromCamera: false,
        scanType: ['qrCode', 'barCode'],
        success: ({ result, scanType, charSet, path }) => {
          if (!result) return this.isShowErr('请扫描正确二维码~')
          result = decodeURIComponent(result)
          let option = {},
            vscode,
            device_sn,
            mid
          if (result.includes('vscode')) {
            vscode = getUrlParams(result, 'vscode')
            this.keyword = vscode
          } else if (result.includes('mid')) {
            mid = getUrlDynamicData(result, 'mid')
          } else {
            if (result.includes('device_sn')) {
              device_sn = getUrlDynamicData(result, 'device_sn')
            } else {
              device_sn = result
            }
            this.keyword = device_sn
          }
          option = {
            vscode,
            device_sn,
            mid,
          }
          this.$emit('onClickScan', result, option)
        },
        fail: (error) => {
          if (error?.errMsg.includes('cancel')) return
          this.isShowErr('请扫描正确二维码~')
        },
      })
    },
    onClickIconImg(e) {
      this.$emit('onClickIcon', e)
    },
    search(val) {
      if (!val) {
        return
      }
      this.$emit('search', val)
    },

    click() {
      this.$emit('click')
    },
    searchClick(val) {
      this.keyword = val
      this.search(val)
    },
    exit(val) {
      this.$emit('exit', val)
      let item = [...this.vServerList[this.listType]]
      let items = deleteValueInObject(item, val)
      this.$u.vuex(`vServerList.${this.listType}`, items)
    },
    focus() {
      this.isShow = true
    },
    clears() {
      this.isShow = false
    }
  },
}
</script>

<style lang="scss" scoped>
.searchContent-width {
  width: var(--search-width);
}

.searchContent {
  position: relative;
  z-index: 999;
  padding: 20rpx 30rpx;
  background-color: $uni-bg-color;
  box-sizing: border-box;

  .searchInput {
    flex: 1;
    position: relative;

    .scan {
      position: absolute;
      font-size: 50rpx;
      color: $textGray;
      right: 20rpx;
      top: 50%;
      transform: translateY(-50%);
      z-index: 900;
    }
  }

  .iconStyle {
    width: 44rpx;
    height: 44rpx;
    margin-left: 40rpx;
  }
}

.search-box {
  border: 2rpx solid #ecebeb;
  position: absolute;
  top: 70rpx;
  z-index: 999999;
  width: 100%;
  // padding: 10rpx 30rpx;
  background-color: #fff;

  .search-title {
    font-size: 20rpx;
    color: #9a9a9a;
    margin-bottom: 10rpx;
  }

  border-bottom-left-radius: 15px;
  /* 圆角的左下角的半径 */
  border-bottom-right-radius: 10px;
  /* 圆角的右下角的半径 */
  // border-bottom-left: 2rpx solid #ecebeb;
}

.search-list {
  // height: 50rpx;

  .list-item {
    width: 100%;
    // border: 2rpx solid #fab6b6;
    padding: 10rpx;
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 10rpx 0;
    color: #333;

    &:hover {
      background-color: $uni-bg-color;
    }

    .list-txt {
      white-space: nowrap;
      /* 防止文本换行 */
      overflow: hidden;
      /* 隐藏超出容器的部分 */
      text-overflow: ellipsis;
      /* 显示省略号 */
      width: 95%;
    }

    .dis-top {
      //   margin-left: 40rpx;
      font-size: 30rpx;
    }
  }
}

.searchInput-box {
  position: relative;
  // display: flex;

  .searchIcon {
    position: absolute;
    left: 10rpx;
    top: 50%;
    transform: translateY(-50%);
    z-index: 900;
    background-color: "#F2F2F2";
    height: 80rpx;
  }




}


</style>
