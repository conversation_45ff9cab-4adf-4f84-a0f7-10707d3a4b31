<template>
  <view class="selectHotel">
    <BaseNavbar title="选择用户" />
    <BaseSearch placeholder="请输入用户名" @search="search" listType="selectUser" />
    <!-- <BaseList listType="selectUser" @searchChange="searchChange" /> -->
    <view class="hotelItemContent">
      <ComList :loading-type="loadingType">
        <view v-for="(item, index) in listData" :key="index" class="hotelItem">
          <radio :checked="selectIndex == index" color="#2979ff" style="transform: scale(0.7)" />

          <view class="hotelItemDesc" @click="select(item, index)">
            <view class="hotelItemDescContent">
              <view class="hotelName">{{ item.role_name }} - {{ item.user_login }}</view>
              <view class="address">{{ item.user_status_text || "正常" }}</view>
            </view>
          </view>
        </view>
      </ComList>
    </view>
  </view>
</template>

<script>
import myPull from "@/mixins/myPull.js";
import BaseSearch from "@/components/base/BaseSearch.vue";
import BaseNavbar from "@/components/base/BaseNavbar.vue";
import ComList from "@/components/list/ComList.vue";
// import BaseList from '@/components/base/BaseList.vue'
import { AddValueInObject } from '@/wxutil/list'
export default {
  data() {
    return {
      code: 0,
      isFoucs: false,
      isConfirm: false,
      from: "",
      userLogin: "",
      selectIndex: -1,
    };
  },
  components: {
    BaseNavbar,
    BaseSearch,
    ComList,
    // BaseList
  },
  methods: {
    async getList(page, done) {
      let data = {
        user_login: this.userLogin,
        role_id: 5, // 只获取5以上的数据
        page: page,
        limit: 10,
      };
      // 获取数据
      let rtnData = await this.$u.api.getMyUserList(data);
      done(rtnData.data);
    },
    select(item, index) {
      this.selectIndex = index;
      if (this.from && this.from === "home_device_allot") {
        // 设备商品列表
        // 修改指定位置的数据
        /*  #ifndef H5 */
        var pages = getCurrentPages()
        var currPage = pages[pages.length - 1] //当前页面
        var prevPage = pages[pages.length - 2] //上一个页面
        //直接调用上一个页面的setData()方法，把数据存到上一个页面中去
        prevPage.setData({
          item: item,
        })
        /*#endif */
        /*  #ifdef H5 */
        this.vCurrPage.item = item
        /*#endif */
        uni.navigateBack({
          delta: 1,
        });

        return;
      }
    },
    searchChange(val) {
      this.hotelName = val
      this.search(val)
    },
    search(text) {
      this.userLogin = text;
      let list = AddValueInObject(this.vServerList.selectUser, text)
      this.$u.vuex(`vServerList.selectUser`, list)
      this.refresh();
    },
  },
  onLoad(options) {
    if (options.from) {
      this.from = options.from;
    }
    this.refresh();
  },

  mixins: [myPull({})],
};
</script>



<style lang="scss">
page {
  background-color: $pageBgColor;
}
</style>

<style lang="scss" scoped>
$margin-buttom-item: 10rpx;

.hotelItem {
  width: 100%;
  background: rgba(255, 255, 255, 1);
  display: flex;
  align-items: center;
  border-bottom: 1rpx solid rgba(229, 229, 229, 1);
  border-radius: $cardRadius;
  box-sizing: border-box;
  padding: 20rpx 30rpx;
  margin-bottom: 20rpx;

  .hotelItemDesc {
    width: 100%;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;

    .hotelName {
      font-size: $font-size-middle;
      color: rgba(40, 40, 40, 1);
    }

    .address,
    .linkman {
      color: rgba(104, 104, 104, 1);
    }

    view:last-child {
      font-size: 16rpx;
    }

    .hotelItemDescContent view {
      &:first-child {
        font-size: $font-size-middle;
        margin-bottom: $margin-buttom-item;
      }

      &:last-child {
        font-size: $font-size-base;
      }
    }

    .linkContent view {
      text-align: right;
      color: rgba(104, 104, 104, 1);

      &:first-child {
        font-size: $font-size-middle;
        margin-bottom: $margin-buttom-item;
      }

      &:last-child {
        font-size: $font-size-base;
      }
    }
  }
}
</style>