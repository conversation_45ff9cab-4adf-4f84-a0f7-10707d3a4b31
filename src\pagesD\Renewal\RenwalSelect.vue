<template>
  <view class="selectHotel">
    <BaseNavbar
      :title="`选择${vPointName}${from == 'home_placeAdd' ? '账号' : ''}`"
    />
    <BaseSearch
      :placeholder="'请输入' + vPointName + '名'"
      @search="search"
      listType="renwalselect"
    />
    <!-- <BaseList listType="renwalselect" @searchChange="searchChange" /> -->
    <view class="hotelItemContent">
      <ComList :loading-type="loadingType">
        <u-checkbox-group @change="checkboxGroupChange">
          <view
            class="hotelItem"
            v-for="(item, index) in listData"
            :key="index"
          >
            <u-checkbox
              @change="checkboxChange(item)"
              :name="item.id"
              v-model="item.checked"
            >
              <view class="hotelItemDesc">
                <view class="hotelItemDescContent">
                  <view v-if="from == 'home_placeAdd'">
                    <view class="hotelName">
                      {{ item.user_login ? item.user_login : '' }}
                    </view>
                  </view>
                  <view v-else>
                    <view class="hotelName">
                      {{ item.hotelName ? item.hotelName : '' }}
                    </view>
                  </view>
                  <view v-if="from == 'home_placeAdd'">
                    <view class="hotelName">
                      {{ item.roleName ? item.roleName : '' }}
                    </view>
                  </view>
                  <view v-else>
                    <view class="address">
                      {{ item.address ? item.address : '' }}
                    </view>
                  </view>
                </view>
                <view class="linkContent">
                  <view v-if="from == 'home_placeAdd'">
                    <view class="hotelName">
                      分成比例：{{
                        item.per ? `${(item.per * 1).toFixed(0)}%` : '0%'
                      }}
                    </view>
                  </view>
                  <view v-else>
                    <view class="linkman">
                      {{ item.linkman ? item.linkman : '' }}
                    </view>
                    <view class="phoneNumber">
                      {{ item.linkmanPhone ? item.linkmanPhone : '' }}
                    </view>
                  </view>
                </view>
              </view>
            </u-checkbox>
          </view>
        </u-checkbox-group>
      </ComList>
    </view>
    <!-- <view @click="ok()">
            确认
        </view> -->
  </view>
</template>

<script>
import myPull from '@/mixins/myPull.js'
import BaseSearch from '@/components/base/BaseSearch.vue'
import BaseNavbar from '@//components/base/BaseNavbar.vue'
import ComList from '@/components/list/ComList.vue'
// import BaseList from '@/components/base/BaseList.vue'
import { AddValueInObject } from '@/wxutil/list'
export default {
  data() {
    return {
      code: 0,
      isFoucs: false,
      isConfirm: false,
      from: '',
      hotelName: '',
      selectIndex: -1,
    }
  },
  components: {
    BaseNavbar,
    BaseSearch,
    ComList,
    // BaseList,
  },
  methods: {
    selectItem(e) {},
    // 选中某个复选框时，由checkbox时触发
    checkboxChange(e) {
      if (e.checked) {
        e.checked = ''
      } else {
        e.checked = true
      }
      // console.log(e)
    },
    // 选中任一checkbox时，由checkbox-group触发
    checkboxGroupChange(e) {
      // console.log(e)
    },
    ok() {
      let data = this.listData.filter((item) => item.checked == true)
      // console.log('data选中数据', data)
    },
    async getList(page, done) {
      // 获取数据
      let rtnData
      if (this.from == 'home_placeAdd') {
        let data = {
          select_role_id: 6,
          page: page,
        }
        rtnData = await this.$u.api.getMyUserList(data)
      } else {
        let data = {
          hotel_name: this.hotelName,
          page: page,
          limit: 10,
        }
        rtnData = await this.$u.api.getMyHotels(data)
      }

      done(rtnData.data)
    },
    select(item, index) {
      this.selectIndex = index
      // 设备商品列表
      // 修改指定位置的数据
      /*  #ifndef H5 */
      var pages = getCurrentPages()
      // console.log('pages', pages)
      var currPage = pages[pages.length - 1] //当前页面
      var prevPage = pages[pages.length - 2] //上一个页面
      //直接调用上一个页面的setData()方法，把数据存到上一个页面中去
      prevPage.setData({
        item: item,
      })
      /*#endif */
      /*  #ifdef H5 */
      this.vCurrPage.item = item
      /*#endif */
      uni.navigateBack({
        delta: 1,
      })

      return
    },
    searchChange(val) {
      this.hotelName = val
      this.search(val)
    },
    search(text) {
      this.hotelName = text
      let list = AddValueInObject(this.vServerList.device, text)
      this.$u.vuex(`vServerList.device`, list)
      this.refresh()
    },
  },
  onLoad(options) {
    if (options.from) {
      this.from = options.from
    }
    this.refresh()
  },

  mixins: [myPull({})],
}
</script>

<style lang="scss">
page {
  background-color: $pageBgColor;
}
</style>

<style lang="scss" scoped>
$margin-buttom-item: 10rpx;

.hotelItem {
  width: 100%;
  height: 150rpx;
  // height: 200rpx;
  background: rgba(255, 255, 255, 1);
  // border: 1px solid #000;
  border-bottom: 1rpx solid rgba(229, 229, 229, 1);
  border-radius: $cardRadius;
  box-sizing: border-box;
  padding: 20rpx 30rpx;
  margin-bottom: 20rpx;

  ::v-deep .u-checkbox {
    // border: 1px solid #000;
    width: 100% !important;
  }

  ::v-deep .u-checkbox__label {
    width: 100%;
    // border: 1px solid #000;
  }

  .hotelItemDesc {
    width: 100%;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;

    .hotelName {
      width: 250rpx;

      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;

      font-size: $font-size-middle;
      color: rgba(40, 40, 40, 1);
    }

    .address {
      width: 250rpx;

      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      color: rgba(104, 104, 104, 1);
    }

    .linkman {
      color: rgba(104, 104, 104, 1);
    }

    view:last-child {
      font-size: 16rpx;
    }

    .hotelItemDescContent view {
      &:first-child {
        font-size: $font-size-middle;
        margin-bottom: $margin-buttom-item;
      }

      &:last-child {
        font-size: $font-size-base;
      }
    }

    .linkContent view {
      text-align: right;
      color: rgba(104, 104, 104, 1);

      &:first-child {
        font-size: $font-size-middle;
        margin-bottom: $margin-buttom-item;
      }

      &:last-child {
        font-size: $font-size-base;
      }
    }
  }
}
</style>
