<template>
  <view>
    <BaseNavbar :title="title" />
    <view class="map-box">
      <view class="search-box">
        <BaseSearch placeholder="请输入地址" @onClickIcon="onClickIcon" @search="searchAddress" :isShowSerch="false" />
        <!-- <input type="text" v-model="searchQuery" placeholder="请输入地址" />
        <button @tap="searchAddress">搜索</button> -->
      </view>
      <map id="map" :longitude="longitude" :latitude="latitude" scale="13" @tap="bindtapMap" :markers="markers"
        :polyline="polyline" :polygons="polygons" show-location style="width: 100%; height: 65vh;">
      </map>
      <view class="but_box">
        <button @tap="addPolygons">添加</button>
        <button @tap="removePolygons">删除当前</button>
        <button @tap="AllPolygons">全部删除</button>
        <button @tap="successPolygons">完成</button>
      </view>


    </view>
  </view>
</template>

<script>
import BaseNavbar from '@/components/base/BaseNavbar.vue';
import { locationMixin } from "@/mixins/locationMixin";
import BaseSearch from "@/components/base/BaseSearch.vue";
export default {
  components: {
    BaseNavbar,
    BaseSearch
  },
  data() {
    return {
      title: "电子围栏",
      markers: [],
      polygons: [],
      polygonLabels: [], // 用于存储多边形中心的文字标注
      currentPolygonIndex: -1, // 当前正在创建的多边形索引
      isCreatingPolygon: false, // 用于判断是否在创建多边形
      isCreating: false,//是否可以创建多边形
      nearbyMachineList: [],
      longitude: 0,
      latitude: 0,
      searchQuery: "", // 搜索框的值

    }
  },
  methods: {
    searchAddress(val) {
      const key = '754BZ-2WFL3-PID3X-RHCYD-DDPCF-PCBQY'; // 替换为你的腾讯地图KEY
      const address = encodeURIComponent(val); // 对地址进行URL编码
      const province = encodeURIComponent(val); // 对省份进行URL编码
      const url = 'https://apis.map.qq.com/ws/place/v1/search';

      console.log("查询地址: ", address, val); // 调试信息
      console.log("请求URL: ", url); // 调试信息
      let that = this
      uni.request({
        url: url,
        method: 'GET', // 确保使用GET方法
        header: {
          'Content-Type': 'application/json' // 确保使用正确的Content-Type
        },
        data: {
          keyword: address, // 传递编码后的地址
          key: key,
          boundary: `region(${province}, 1)` // 传递编码后的省份
        },
        success: function (res) {
          console.log("返回结果: ", res); // 调试信息
          if (res.data.status === 0) {
            var pois = res.data.data;
            that.longitude = pois[0].location.lng
            that.latitude = pois[0].location.lat
            console.log(that.longitude)
            // that.initMarkers();
            // for (var i = 0; i < pois.length; i++) {
            //   console.log(pois[i].title);
            //   console.log(pois[i].location);

            // }
          } else {
            uni.showToast({
              title: '地址解析失败',
              icon: 'none'
            });
          }
        },
        fail: function (err) {
          console.error("请求错误: ", err);
          uni.showToast({
            title: '网络错误',
            icon: 'none'
          });
        }

        // url: url,
        // success: res => {
        //   if (res.data.status === 0) {
        //     const location = res.data.result.location;
        //     this.curLatitude = location.lat;
        //     this.curLongitude = location.lng;
        //     this.markers.push({
        //       id: this.markers.length,
        //       latitude: location.lat,
        //       longitude: location.lng,
        //       width: '24px',
        //       height: '34px',
        //       callout: {
        //         content: address,
        //         color: "#fff",
        //         fontSize: 12,
        //         borderRadius: 3,
        //         borderWidth: 1,
        //         borderColor: '',
        //         bgColor: "#31c27c",
        //         padding: 0.5,
        //         display: "ALWAYS",
        //         textAlign: "center"
        //       }
        //     });
        //   } else {
        //     uni.showToast({
        //       title: '地址解析失败',
        //       icon: 'none'
        //     });
        //   }
        // },
        // fail: err => {
        //   console.error(err);
        //   uni.showToast({
        //     title: '网络错误',
        //     icon: 'none'
        //   });
        // }
      });
    },
    addPolygons() {
      if (this.isCreatingPolygon) {
        console.log('上一个围栏还没结束');
        return;
      }
      this.isCreating = true
      this.currentPolygonIndex = this.polygons.length;
      this.markers = []; // 清空当前多边形的标记点
    },
    //添加可以画图
    creatPolygons() {
      //创建多边形围栏
      // 创建多边形围栏
      if (this.markers.length < 3) {
        this.isCreatingPolygon = true;
        console.log('索引', this.currentPolygonIndex)
        if (this.polygons[this.currentPolygonIndex]) {
          this.polygons.splice(this.currentPolygonIndex, 1);

        }
        return;
      }
      console.log('是不是这个map')
      let newArray = this.markers.map(marker => ({
        latitude: marker.latitude,
        longitude: marker.longitude
      }));

      let params = {
        id: this.currentPolygonIndex,
        fillColor: "#1791fc66",
        strokeColor: "#FFF",
        strokeWidth: 2,
        zIndex: 3
      };

      let newPolygon = Object.assign({ points: newArray }, params);
      this.polygons[this.currentPolygonIndex] = newPolygon;
      this.isCreatingPolygon = false;
      console.log('polygons', this.polygons);

    },

   
    bindtapMap(e) {
      if (!this.isCreating) {
        return;
      }

      let tapPoint = e.detail;
      let markers = this.markers;
      let existingMarkerIndex = this.findMarkerIndex(tapPoint.latitude, tapPoint.longitude);

      if (existingMarkerIndex !== -1) {
        markers.splice(existingMarkerIndex, 1); // 移除标记
      } else {
        let newContent = markers.length;
        let markerItem = {
          id: newContent,
          latitude: tapPoint.latitude,
          longitude: tapPoint.longitude,
          width: '24px',
          height: '34px',
          rotate: 0,
          alpha: 1,
          zIndex: 3,
        };
        markers.push(markerItem);
      }

      this.markers = markers;
      console.log('markers', this.markers);
      this.creatPolygons();
    },
    removePolygons() {
      // 删除所有围栏和标记
      this.markers = [];
      // console.log()

      console.log('删除', this.polygons[this.currentPolygonIndex])
      if (this.polygons[this.currentPolygonIndex]) {
        this.polygons.splice(this.currentPolygonIndex, 1);
      }
      this.isCreatingPolygon=false
      this.isCreating = false;
    },
    AllPolygons() {
      // 删除所有围栏和标记
      this.markers = [];
      this.polygons = [];
      this.isCreating = false;
    },
    successPolygons() {
      // 完成当前多边形的创建
      this.isCreating = false;
      this.markers = [];
    },
    findMarkerIndex(lat, lng) {
      const tolerance = 0.00005; // 公差范围，用于判断点击点是否接近现有标记
      return this.markers.findIndex(marker => {
        return Math.abs(marker.latitude - lat) < tolerance && Math.abs(marker.longitude - lng) < tolerance;
      });
    },

    //初始化地图
    initMarkers() {
      console.log('定位', this.latitude, this.longitude)
      let markerArr = {
        latitude: this.latitude,
        longitude: this.longitude,
        iconPath: this.icMarkerDefault,
        width: 40,
        height: 50,
      }
      console.log("🚀 ~ markerArr", markerArr)
      this.markers = markerArr;
    },
    //获取位置信息
    getLocPermission() {
      // #ifdef MP-WEIXIN || MP-TOUTIAO
      this.initLocPermission(() => {
        this.getCurrentLocation(() => {
          this.longitude = this.curLongitude
          this.latitude = this.curLatitude
          this.initMarkers();

        });
      });
      //#endif
      // #ifdef MP-ALIPAY
      uni.showModal({
        title: "温馨提示：",
        content: "需要授权您的位置信息,为您展示附近机器,是否授权?",
        success: ({ confirm }) => {
          if (confirm) {
            this.getCurrentLocation(() => {
              this.initMarkers();
            });
          }
        },
      });

      //#endif
      // #ifdef H5
      uni.showModal({
        title: "温馨提示：",
        content: "需要授权您的位置信息,为您展示附近机器,是否授权?",
        success: ({ confirm }) => {
          if (confirm) {
            this.getCurrentLocation(() => {
              this.initMarkers();
            });
          }
        },
      });
      //#endif
    },
    //获取定位
    async getLocation(hotel_id) {
      try {
        let data = {
          hotel_id: hotel_id
        }
        const res = await this.$u.api.getMyHotels(data);
        this.nearbyMachineList[0] = res.data.find(item => item.id == hotel_id)


        this.longitude = this.nearbyMachineList[0].lon
        this.latitude = this.nearbyMachineList[0].lat


        console.log('数据筛选', this.nearbyMachineList, this.longitude, this.latitude)

        if (this.longitude && this.latitude) {
          console.log('经纬度', this.longitud, this.latitude)
        } else {
          this.getLocPermission()
        }
      }
      catch (error) {
        console.log(error);
      }
    }

  },
  mixins: [locationMixin],

  onLoad(options) {
    this.mapCtx = uni.createMapContext("map"); // map为地图的id
    // this.getLocPermission()
    if (options.hotel_id) {
      this.getLocation(options.hotel_id)

    }


  },
  // mixins: [myPull()],
}
</script>

<style lang="scss" scoped>
.but_box {
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #fff;
  margin-top: 20rpx;
  padding: 20rpx;

}
</style>
