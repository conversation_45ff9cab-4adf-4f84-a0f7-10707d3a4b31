import Vue from 'vue'
import App from './App'


import uView from "uview-ui";
import httpInterceptor from '@/common/http/interceptor'
import httpApi from '@/common/http/api'
import store from '@/store';
let vuexStore = require("@/store/$u.mixin.js");

Vue.use(uView);

Vue.mixin(vuexStore);
Vue.config.productionTip = false

App.mpType = 'app'

const app = new Vue({
  store,
  ...App
})


// 这里需要写在最后，是为了等Vue创建对象完成，引入"app"对象(也即页面的"this"实例)
Vue.use(httpInterceptor, app)
Vue.use(httpApi, app)
app.$mount()
