<template>
  <view class="card" @click="goDetails">
    <view class="content">
      <BaseCheck v-if="isFromAdSelectWechat" />
      <view class="content-img" @click.stop="previewImage(info.wx_img)">
        <image class="img" :src="info.wx_img" />
      </view>
      <view class="content-info">
        <view class="content-info-box">
          <view class="title">公众号名称：</view>
          <view class="txt">
            <text user-select="true">
              {{ info.app_name }}
            </text>
          </view>
        </view>
        <view class="content-info-box">
          <view class="title">Appid：</view>
          <view class="txt">
            <text user-select="true">
              {{ info.app_id }}
            </text>
          </view>
        </view>
      </view>
    </view>
    <view class="btn">
      <BaseButton @onClick="isShowModal = true" type="default" shape="circle">
        删 除
      </BaseButton>
    </view>
    <BaseModal
      :show.sync="isShowModal"
      @confirm="del"
      :content="`该${typeName}将会删除，此操作不可恢复，是否继续删除？`"
    />
  </view>
</template>

<script>
import BaseButton from '@/components/base/BaseButton.vue'
import BaseModal from '@/components/base/BaseModal.vue'
import BaseCheck from '@/components/base/BaseCheck.vue'

export default {
  name: 'WechatCard',
  components: { BaseButton, BaseModal, BaseCheck },
  props: {
    info: { type: Object, default: {} },
    isFromAdSelectWechat: { type: Boolean, default: false },
  },
  computed: {
    typeName() {
      return this.info.type === 1 ? '公众号' : '小程序'
    },
  },
  data() {
    return {
      isShowModal: false,
    }
  },
  methods: {
    //预览图片
    previewImage(urls) {
      uni.previewImage({
        urls: [urls],
      })
    },
    del() {
      let data = {
        id: this.info.id,
      }
      this.$u.api
        .delWechat(data)
        .then((res) => {
          this.isShowSuccess('删除成功', 0, () => {
            this.$emit('refresh')
          })
        })
        .catch((err) => {
          console.log('错误信息', err)
        })
    },
    goDetails() {
      console.log('🚀 ~ isFromAdSelectWechat', this.isFromAdSelectWechat)
      if (this.isFromAdSelectWechat) {
        /*  #ifndef H5 */
        let pages = getCurrentPages()
        let currPage = pages[pages.length - 1] //当前页面
        let prevPage = pages[pages.length - 2] //上一个页面
        //直接调用上一个页面的setData()方法，把数据存到上一个页面中去
        prevPage.setData({
          item: this.info,
        })
        /*#endif */
        /*  #ifdef H5 */
        this.vCurrPage.item = this.info
        /*#endif */
        uni.navigateBack({ delta: 1 })
      } else {
        uni.navigateTo({
          url: `/pagesB/wechat/WechatAdd?from=edit&type=${this.info.type}&id=${this.info.id}`,
        })
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.card {
  position: relative;
  padding: 20rpx;
}
.content {
  display: flex;
  align-items: center;
  &-img {
    width: 200rpx;
    height: 200rpx;
    flex-shrink: 0;
    .img {
      width: 100%;
      height: 100%;
    }
  }
  &-info {
    flex: 1;
    margin-left: 10rpx;
    &-box {
      display: flex;
      font-size: $font-size-middle;
      line-height: 1.8;
      .title {
        color: $textDarkGray;
      }
      .txt {
        color: $textBlack;
      }
    }
  }
}
.btn {
  position: absolute;
  right: 10rpx;
  bottom: 10rpx;
}
</style>
