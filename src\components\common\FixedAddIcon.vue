<template>
  <view
    class="add-box"
    :style="{ bottom: 100 + bottom + vIphoneXBottomHeight + 'rpx' }"
  >
    <image
      v-if="isShowImport"
      @click="onImport"
      class="add-box-import img"
      src="@/pagesB/static/img/icon/goodsImport.png"
    />

    <image
      class="add-box-add img"
      src="@/pagesB/static/img/icon/goodsAdd.png"
      @click="onAdd"
    />
  </view>
</template>

<script>
export default {
  name: "FixedAddIcon",
  props: {
    bottom: { type: Number, default: 0 },
    isShowImport: { type: Boolean, default: false },
  },
  methods: {
    onAdd() {
      this.$emit("onAdd");
    },
    onImport() {
      this.$emit("onImport");
    },
  },
};
</script>

<style lang="scss" scoped>
.add-box {
  position: fixed;
  right: 30rpx;
  bottom: 100rpx;
  width: 100rpx;
  z-index: 888;
  .img {
    width: 100%;
    height: 100rpx;
  }
  &-import {
    margin-bottom: 40rpx;
  }
}
</style>