<template>
  <view>
    <BaseInput v-model="value" rightText="arrow" :disabled="true" :placeholder="placeholder" @onClick="show = true" />
    <u-picker mode="time" v-model="show" :params="params" :start-year="startYear" :default-time="defaultTime"
      :safe-area-inset-bottom="true" @confirm="confirm" class="fidex" />

  </view>
</template>

<script>
import BaseInput from "../base/BaseInput.vue";
export default {
  components: { BaseInput },
  name: "TimeSelect",
  props: {
    placeholder: { type: String, default: "请输入内容" },
    value: { type: String, default: "" },
    defaultTime: { type: String },
  },

  data() {
    return {
      show: false,
      startYear: "",
      params: {
        year: false,
        month: false,
        day: false,
        hour: true,
        minute: true,
        second: false,
      },
    };
  },
  methods: {
    confirm(e) {
      let { hour, minute } = e;
      this.$emit("input", `${hour}:${minute}`);
    },
  },
  onReady() { },
};
</script>

<style lang="scss" scoped>

</style>
