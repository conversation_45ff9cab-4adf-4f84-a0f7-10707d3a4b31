<template>
  <view class="type-select">
    <view class="type-select-top">
      <view class="type-select-top-title">{{ title }}</view>
      <view class="type-select-top-btn" @click="comfirm">确定</view>
    </view>
    <view class="type-select-main">
      <view class="type-select-main-item" :class="selectIndexList.includes(index) ? 'type-select-main-select-item' : ''
        " v-for="(item, index) in list" :key="index" @click="selectItem(index)">
        {{ item.title || item }}
      </view>
      <view class="type-select-main-item" :class="isSelectOther ? 'type-select-main-select-item' : ''"
        v-if="isShowOther" @click="isSelectOther = !isSelectOther">其他</view>
      <view class="type-select-main-item-seat" v-for="item in 2" :key="item"></view>
    </view>
    <!-- 其他 -->
    <view class="other" v-if="isSelectOther && isShowOther">
      <BaseInput placeholder="请输入其他内容，最多15个字" maxlength="15" v-model="other" />
    </view>
  </view>
</template>

<script>
import BaseInput from "../base/BaseInput.vue";
export default {
  components: { BaseInput },
  name: "TypeSelectPopup",
  props: {
    isShowOther: { type: String | Boolean, default: false }, //是否显示其他
    title: { type: String, default: "选择" }, //标题
    isOpt: { type: String | Boolean, default: false }, //是否多选
    list: {
      type: Array, default: function () {
        return [];
      }
    }, // list可以传字符串数组 ["怪兽"]  也可以传对象数组 [{title:'怪兽'}]
  },
  data() {
    return {
      // list可以传字符串数组  也可以传对象数组 {title:'怪兽'}
      //   list: ["怪兽", "街电", "来电", "小点", "美团"],
      //   list: [{title:"怪兽",id:0,}],

      selectIndexList: [],
      isSelectOther: false,
      other: "", //其他内容
    };
  },
  methods: {
    selectItem(i) {
      if (!this.isOpt) {
        this.selectIndexList = [];
        return this.selectIndexList.push(i);
      }
      if (this.selectIndexList.includes(i)) {
        this.selectIndexList = this.selectIndexList.filter(
          (item) => item !== i
        );
      } else {
        this.selectIndexList.push(i);
      }
    },
    comfirm() {
      let comfirmList = this.selectIndexList.map((item) => {
        return this.list[item];
      });
      if (this.isSelectOther && this.isShowOther && this.other) {
        comfirmList.push(this.other);
      }
      this.$emit("comfirm", comfirmList, this.selectIndexList);
      // .join("|")
      this.selectIndexList = [];
      this.isSelectOther = false;
      this.other = "";
    },
  },
};
</script>

<style lang="scss" scoped>
.type-select {
  &-top {
    display: flex;
    justify-content: space-between;
    padding: 20rpx;
    border-bottom: 2rpx solid $dividerColor;
    color: $textBlack;
    font-size: $font-size-middle;

    &-btn {
      color: $themeComColor;
      margin-right: 10rpx;
    }
  }

  &-main {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    padding: 20rpx;
    padding-bottom: 0;

    &-item {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 150rpx;
      height: 50rpx;
      background-color: $pageBgColor;
      margin-bottom: 20rpx;
    }

    &-item-seat {
      width: 150rpx;
    }

    &-select-item {
      color: $themeComColor;
      background: rgba($color: $themeComColor, $alpha: 0.2);
    }
  }

  .other {
    padding: 0 20rpx;
  }
}
</style>