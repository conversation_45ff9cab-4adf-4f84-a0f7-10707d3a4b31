<template>
  <view>
    <BaseNavbar :title="vSiteConfig.site_info.site_name + '云学院'" />

    <BaseSearch placeholder="搜索你感兴趣的内容" @search="search" listType="cloudcollege" />
    <!-- <BaseList listType="cloudcollege" @searchChange="searchChange" /> -->
    <view class="content">
      <view class="content_top">
        <view class="banner">
          <uni-swiper-dot :info="recomArticleList" :current="current" :dotsStyles="{
      backgroundColor: 'rgba(83, 200, 249, 0.3)',
      selectedBackgroundColor: 'rgba(83, 200, 249, 0.9)',
      bottom: '10',
    }">
            <swiper class="swiper-box" @change="change" :autoplay="true" :circular="true">
              <swiper-item v-for="(item, index) in recomArticleList" :key="index" @click="goDetails(item.url)">
                <image :src="item.thumbnail" class="banner_img" />
              </swiper-item>
            </swiper>
          </uni-swiper-dot>
        </view>
        <view class="lately_live">
          <view class="title_column flexRowBetween">
            <view class="title">最近指导文章</view>
            <view class="more" @click="goMore">
              更多
              <BaseIcon name="arrow-right" size="28" />
            </view>
          </view>
          <view class="lately_live_list flexRowBetween">
            <CloudCollegeCard v-for="item in latelyLiveList" :key="item.id" :info="item" />
          </view>
        </view>
      </view>
      <view class="tabs">
        <BaseTabs height="44" :list="tabList" :isShowBar="false" :isScroll="true" @change="tabChange"
          :current="curTabIndex" />
        <ComList class="tabs_list" :loading-type="loadingType">
          <CloudVideoCard v-for="item in listData" :key="item.id" :info="item" />
        </ComList>
      </view>
    </view>
    <BaseBackTop @onPageScroll="onPageScroll" :scrollTop="scrollTop"></BaseBackTop>
  </view>
</template>

<script>
import BaseNavbar from '@/components/base/BaseNavbar.vue'
import BaseSearch from '@/components/base/BaseSearch.vue'
import BaseIcon from '@/components/base/BaseIcon.vue'
import CloudCollegeCard from '../components/cards/CloudCollegeCard.vue'
import BaseTabs from '@/components/base/BaseTabs.vue'
import CloudVideoCard from '@/components/card/CloudVideoCard.vue'
import ComList from '../../components/list/ComList.vue'
import myPull from '@/mixins/myPull.js'
import UniSwiperDot from '../../components/uni-swiper-dot/uni-swiper-dot.vue'
// import BaseList from '@/components/base/BaseList.vue'
import { AddValueInObject } from '@/wxutil/list'
import BaseBackTop from '@/components/base/BaseBackTop.vue'
export default {
  components: {
    BaseNavbar,
    BaseSearch,
    BaseIcon,
    CloudCollegeCard,
    BaseTabs,
    CloudVideoCard,
    ComList,
    UniSwiperDot,
    // BaseList,
    BaseBackTop,
  },
  data() {
    return {
      scrollTop: 0,
      tabList: [],
      curTabIndex: 0,
      recomArticleList: [], //推荐文章list
      latelyLiveList: [], //最近直播list
      current: 0,
      hotelName: '',
    }
  },
  methods: {
    onPageScroll(e) {
      this.scrollTop = e.scrollTop
    },
    searchChange(val) {
      this.hotelName = val

      this.search(val)
    },
    search(e) {
      this.ad_desc = e
      let list = AddValueInObject(this.vServerList.cloudcollege, e)
      this.$u.vuex(`vServerList.cloudcollege`, list)
      this.refresh()
    },
    goDetails(url) {
      uni.navigateTo({
        url: '/pagesC/webView/WebView?url=' + encodeURIComponent(url),
      })
    },
    change(e) {
      this.current = e.detail.current
    },
    tabChange(e) {
      this.curTabIndex = e
      this.refresh()
    },
    goMore() {
      uni.navigateTo({ url: '/pagesB/cloud/LatelyLiveList' })
    },
    getArticleCat() {
      this.$u.api
        .articleCat()
        .then((res) => {
          this.tabList = res
          this.refresh()
        })
        .catch((err) => {
          console.log('错误信息', err)
        })
    },
    async getList(page, done) {
      try {
        let data = {
          recommended: false,
          page,
          limit: 10,
          cat_id: this.tabList?.[this.curTabIndex]?.id,
        }
        let res = await this.$u.api.articleList(data)
        done(res)

      } catch (error) {
        console.log('错误信息', error)
      }

    },
    async getRecomArticleList() {
      try {
        let data = {
          recommended: true,
          page: 1,
          limit: 5,
        }
        let res = await this.$u.api.articleList(data)
        this.recomArticleList = res
      } catch (error) {
        console.log('错误信息', error)
      }

    },
    async getLatelyLive() {
      try{
        let data = {
        recommended: false,
        page: 1,
        limit: 2,
        cat_id: 0,
        keyword: '直播',
      }
      let res = await this.$u.api.articleList(data)
      this.latelyLiveList = res?.slice(0, 2)
      }catch(error){
        console.log('错误信息', error)
      }
      
    },
  },
  onLoad(options) {
    this.getArticleCat()
    this.getRecomArticleList()
    this.getLatelyLive()
  },
  mixins: [myPull()],
}
</script>

<style lang="scss" scoped>
.content {
  &_top {
    padding: 30rpx;
    padding-top: 0;
  }

  .banner {
    width: 100%;
    height: 300rpx;

    &_img {
      width: 100%;
      height: 100%;
      background: #d5d5d5;
      border-radius: $cardRadius;
    }
  }

  .lately_live {
    .title_column {
      margin-top: 50rpx;
      margin-bottom: 30rpx;

      .title {
        color: $textBlack;
        font-weight: bold;
        font-size: $font-size-xlarge;
      }

      .more {
        font-size: $font-size-base;
        color: $textDarkGray;
      }
    }

    &_list {}
  }

  .tabs {
    margin: 60rpx 0 30rpx;
  }
}
</style>
