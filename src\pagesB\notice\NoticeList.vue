<template>
  <view>
    <view class="sticker">
      <BaseNavbar title="消息中心" />
      <BaseTabs :current="curTabIndex" :list="tabList" :isShowBar="false" @change="tabChange" />
    </view>

    <view class="news">
      <view class="news-num">共{{ unreadNum }}条未读消息</view>
      <view class="news-btn" @click="readMessage">一键已读</view>
    </view>
    <ComList :loading-type="loadingType">
      <NoticeListCard
        v-for="item in listData"
        :key="item.id"
        :info="item"
        @setStatus="setStatus(item)"
      />
    </ComList>
  </view>
</template>

<script>
import BaseNavbar from "@/components/base/BaseNavbar.vue";
import BaseTabs from "../../components/base/BaseTabs.vue";
import ComList from "@/components/list/ComList.vue";
import NoticeListCard from "../components/cards/NoticeListCard.vue";
import myPull from "@/mixins/myPull.js";
export default {
  components: { BaseTabs, BaseNavbar, ComList, NoticeListCard },
  data() {
    return {
      tabList: [
        {
          name: "全部",
          status: 0,
        },
        {
          name: "系统",
          status: 1,
        },
        {
          name: "活动",
          status: 2,
        },
        {
          name: "其他",
          status: 9,
        },
      ],
      curTabIndex: 0,
      unreadNum: 0, //未读数量
    };
  },
  methods: {
    setStatus(item) {
      this.$set(item, "status", 3);
    },
    tabChange(e) {
      this.curTabIndex = e;
      this.refresh();
    },
    getList(page, done) {
      let data = {
        type: this.tabList[this.curTabIndex].status,
        page,
      };
      this.$u.api.userMessage(data).then((res) => {
        this.unreadNum = res.data.count;
        done(res.data.list);
      });
    },
    readMessage() {
      let readListId = [];
      this.noticeList?.forEach((item) => {
        item.status != 3 && readListId.push(item.id);
      });
      if (readListId.length == 0) return;
      let data = {
        ids: readListId.join(","),
      };
      this.$u.api.readMessage(data).then((res) => {
        this.userMessage();
      });
    },
  },
  onLoad(opt) {
    this.refresh();
  },
  onShow() {},
  mixins: [myPull()],
};
</script>

<style lang="scss" scoped>
.news {
  display: flex;
  justify-content: space-between;
  padding: 20rpx 30rpx;
  &-num {
    font-size: $font-size-small;
    color: $themeComColor;
  }
  &-btn {
    color: $textBlack;
    font-size: $font-size-base;
  }
}
</style>