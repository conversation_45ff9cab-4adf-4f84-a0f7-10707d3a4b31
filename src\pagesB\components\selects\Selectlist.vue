<template>
    <view class="select_box" :style="{ height: `${height}rpx` }">
        <view class="select ">

            <view class="vpointName fontSize" @click="tabShow = true">


                <view class="fontSize">{{ options[tabValue].label }}</view>
                <view class="click">
                    <u-icon :name="!tabShow ? 'arrow-down' : 'arrow-up'" color="#333f" size="50"></u-icon>
                </view>
            </view>

            <view class="selectShow" v-if="tabShow">
                <view v-for="(item, i) in options" :key="i" @click="changeBtn(i)">{{ item.label }}</view>
            </view>
        </view>
        <view class="zhe" v-if="tabShow" @click="tabShow = false">

        </view>
    </view>

</template>

<script>

export default {
    props: {
        options: {
            type: Array,
            default:
                function () {
                    return [{ label: '0', value: 0 }, { label: '2', value: 2 }, { label: '3', value: 3 }, { label: '4', value: 4 }, { label: '5', value: 5 }];
                }

        },
        tabValue: {
            type: Number,
            default: 0
        },
        height: {
            type: Number,
            default: 50
        }
    },
    computed: {

    },


    data() {
        return {
            tabShow: false
        };
    },

    methods: {
        changeBtn(i) {
            this.tabShow = false
            this.$emit('changeBtn', i)
        }
    },
};
</script>

<style lang="scss" scoped>
.select_box {
    height: 100%;
    width: 100%;
    // border: 1px solid #000;

}

.vpointName {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 10rpx 20rpx;
}

.select {

    width: 100%;
    // padding: 0 15rpx;
    position: relative;
    font-size: 40rpx;
    margin-left: 5rpx;
    // text-align: center;
    // display: flex;

    border: 1px solid #999;
    // 
    // >view:nth-child(1) {
    //     // border: 1px solid #000;
    //     margin-left: 25rpx;
    //     margin-right: 25rpx;
    // }

    .click {
        // border: 1px solid #000;
        font-size: 25rpx;
        color: $font-size-middle;
        width: 40rpx;
        text-align: center;
        // display: flex;
        // align-items: center;
        height: 60rpx;
        // border: 1px solid #000;
        // position: absolute;
        right: -10rpx;
        // margin-left: 10rpx;
        // top: 0;
        // z-index: 666;

    }

    // display: flex;
    // align-items: /center;
}

.selectShow {

    // border: 1px solid #000;
    >view {
        padding: 10rpx 20rpx;
        border: 1rpx solid #000;
    }
}
</style>