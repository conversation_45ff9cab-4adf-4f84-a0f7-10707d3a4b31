<template>
  <view class="card" @click="click()">
    <view class="content">
      <image class="img" :src="info.original_img || vDefaultIcon" />
      <view class="imgdis" v-if="info.is_refund == 1">
        <image class="stock_icons" src="@/pagesB/static/img/icon/refund.png" />
      </view>
      <view class="info flexColumnBetween">
        <view class="goods_name flexRowBetween">
          <view class="name">{{ info.good_name }}</view>
          <view class="order-time">{{ info.add_time }}</view>
          <view class="status flexRowAllCenter" v-if="false">
            <block v-if="Math.random() > 0.5">
              <image class="status_icon" src="@/pagesB/static/img/icon/finish_icon.png" />
              <view>交易完成</view>
            </block>
            <block v-else>
              <image class="status_icon" src="@/pagesB/static/icon/refund_icon.png" />
              <view>退款成功</view>
            </block>
          </view>
        </view>
        <view class="flexRowBetween">
          <view>
            <text>{{ vUnit === 3 ? "时长：" : "数量：" }}</text><text class="num">{{ vUnit === 3 ?
    (info.outnum + "分钟") : info.outnum }}</text>
          </view>
          <view></view>
        </view>
        <view class="flexRowBetween">
          <view>
            <text>价格：</text>
            <text class="price">￥{{ info.order_amount }}</text>
          </view>
          <view class="status flexRowAllCenter">
            <image v-if="info.order_status == 1 && info.is_refund == 0" class="status_icon"
              src="@/pagesB/static/img/icon/finish_icon.png" />
            <view :style="{ color: info.order_status == 1 && info.is_refund == 0 ? '#0EADE2' : 'red' }">
              {{ orderStatus() }}
            </view>
            <!-- <view class="arrow" :class="show ? 'rotate' : ''">
              <BaseIcon name="arrow-right" :color="info.order_status == 1 ? '#0EADE2' : 'red'" size="28" />
            </view> -->
          </view>
        </view>
      </view>
    </view>
    <view class="flexButton" v-if="info.is_show_refund == 1 && info.is_refund == 0">
    <!-- <view class="flexButton"> -->
      <!-- <view class="returns" @click.stop="goHistory()">
        订单指令详情
      </view> -->
      <view class="returns" @click.stop="retunOrder">
        退款
      </view>
      <!-- <view class="returns" @click.stop="start(0)">
        远程启动
      </view>
      <view class="returns" @click.stop="deviceList()">
        启动其他
      </view> -->
      <view class="returns"
        :style="{ background: info.use_status == 1 ? 'linear-gradient(268deg, #ff5b5b 0%,  #e61f1f 99%)' : '' }"
        @click.stop="onOff">
        {{ info.use_status == 1 ? '禁用' : '启用' }}设备
      </view>
    </view>
    <view class="flexBottom border_top" @click.stop="show = !show">
      <view>

      </view>
      <view class="flexBottom">
        <view :style="{ color: '#0EADE2' }">
          查看详情
        </view>
        <view class="arrow" :class="show ? 'rotate' : ''">
          <BaseIcon color="#0EADE2" :name="!show ? 'arrow-down' : 'arrow-up'" size="28" />
        </view>
      </view>

    </view>
    <view v-if="show" class="details">
      <view>
        <view>{{ vPointName }}</view>
        <view>{{ info.hotel_name }}</view>
      </view>
      <view v-if="info.addressDetail">
        <view class="flex_white">详细位置</view>
        <view class="textMaxTwoLine">{{ info.addressDetail }}</view>
      </view>
      <!-- <view>
        <view>柜门</view>
        <view>A柜门</view>
      </view> -->
      <!-- <view>
        <view>{{ info.unit === 3 ? vCargoLanes : vCargoLanes }}</view>
        <view>{{ info.channel || "01" }}</view>
      </view> -->
      <view>
        <view>设备编号</view>
        <text user-select>{{ info.device_sn }}</text>
      </view>
      <view>
        <view>订单编号</view>
        <text user-select>{{ info.order_sn }}</text>
      </view>
      <view>
        <view>支付单号</view>
        <text user-select>{{ info.transaction_id }}</text>
      </view>
      <view v-if="info.pay_name == '汇付支付'">
        <view>商户单号</view>
        <text user-select>{{ info.party_order_id || '' }}</text>
      </view>
      <view v-if="info.pay_name == '汇付支付'">
        <view>交易单号</view>
        <text user-select>{{ info.out_trans_id || '' }}</text>
      </view>
      <view>
        <view>买家名称</view>
        <view>{{ info.buyer || info.openid }}</view>
      </view>
      <view>
        <view>支付方式</view>
        <view>{{ info.pay_name }}</view>
      </view>
      <view v-if="info.rake_back_platfrom">
        <view>返利平台</view>
        <view>
          {{
    info.rake_back_platfrom === "xh"
      ? vSiteConfig.site_info.site_name
      : info.rake_back_platfrom || ""
  }}
        </view>
      </view>
      <view>
        <view>订单类型</view>
        <view>{{ orderType() }}</view>
      </view>
      <view>
        <view>交易时间</view>
        <view>{{ info.add_time }}</view>
      </view>
      <block v-if="false">
        <view>
          <view>退款方式</view>
          <view>原路退回到微信</view>
        </view>
        <view>
          <view>到账时间</view>
          <view>2021-10-25 09:50:06</view>
        </view>
      </block>
      <block v-if="false">
        <view>
          <view class="flex_white">驳回原因</view>
          <view>什么原因描述什么原因描述什么原因描述什么
            原因描述什么原因描述什么原因描述什么原因 描述</view>
        </view>
      </block>
      <view class="btn flexRowBetween" v-if="false">

        <BaseButton type="default" width="310">驳回申请</BaseButton>
        <BaseButton type="primary" width="310">审核通过</BaseButton>
      </view>
      <view class="btn again-open" v-if="info.order_status == 2">
        <BaseButton type="primary" width="310" @onClick="openDoor">重新出货</BaseButton>
      </view>
    </view>
    <!-- <BaseModal :show.sync="isShowModal" @confirm="confirmModal" :content="modalContent">
      <template v-if="isDivide">
        <view class="input-percentage" slot="default">
          <BaseInput v-model="percentage" placeholder="请输入比例数值" rightText="%" />
        </view>
      </template>
<template v-if="isBindWx">
        <view v-if="isUnbindWx">确定解绑该用户的微信吗?</view>
        <view slot="default" v-else>
          1、关注“{{ vSiteConfig.site_info.site_name }}”公众号
          <br />
          2、登录“{{ vSiteConfig.site_info.site_name }}云平台”
          <br />
          3、绑定微信号
        </view>
      </template>
</BaseModal> -->
  </view>
</template>

<script>
import BaseButton from "@/components/base/BaseButton.vue";
import BaseIcon from "@/components/base/BaseIcon.vue";
import BaseModal from "@/components/base/BaseModal.vue";
import BaseInput from "@/components/base/BaseInput.vue";
import { navigateToReplenishList } from '@/wxutil/navgate.js'
export default {
  components: { BaseIcon, BaseButton, BaseModal, BaseInput },
  name: "OrderListCard",
  props: {
    info: { type: Object, default: {} },

  },
  computed: {

  },
  data() {
    return {
      show: false,

      isShowStartModal: false,
      length_time: 1, // 启动时长
      selectIndex: 0, //选中数据项
      modalContent: "是否禁用该用户？",
      isShowModal: false,
    

    };
  },
  methods: {
    goHistory() {
      //跳转到历史页添加信息
      uni.navigateTo({
        url:
          '/pagesD/History/OrderHistory?data=' +
          encodeURIComponent(JSON.stringify(this.info)),
      })
    },
    orderType() {
      switch (this.info.prom_type) {
        case 1:
          return "普通订单";
        case 2:
          return "免费订单";
        case 3:
          return "返佣订单";
        case 4:
          return "抖音订单";
        default:
          return "普通订单";
      }
    },
    orderStatus() {
      let status = "";
      if (this.info["is_refund"] == 1) {
        status = "已退款";
      }
      else if (this.info["order_status"] == 1) {
        status = "完成";
      } else if (this.info["order_status"] == 2) {
        status = "异常";
      } else if (this.info["order_status"] == 3) {
        status = "订单取消";
      } else if (this.info["order_status"] == 4) {
        status = "已处理";
      } else {
        if (this.info["pay_status"] == 0) {
          status = "待付款";
        } else if (this.info["pay_status"] == 1) {
          if (this.info["outstatus"] == 1) {
            status = "已出货";
          } else {
            if (this.info["unit"] == 3) {
              status = "启动失败";
            } else {
              status = "出货失败";
            }
          }
        }
      }
      return status;
    },
    //
    onOff() {
      this.$emit("onOff");
    },
    click() {
      this.$emit('onClick')
    },
    async openDoor() {
      try {
        let data = {
          order_id: this.info.order_id,
        };
        // 远程打开订单
        await this.$u.api.orderReOpen(data)
        this.isShowSuccess("执行成功");
      } catch (error) {
        console.log('错误信息', error)
      }

    },
    retunOrder() {
      this.$emit('retunOrder')
    },
    async deviceList() {
      try {
        await navigateToReplenishList(`/pagesB/device/DeviceList?from=go&dianwei=${this.info.dianweiid}&hotel_id=${this.info.hotelid}`);
        // 导航成功后的代码
      } catch (err) {
        // 导航失败后的代码
      }
    },
    // 游戏设备，启动游戏设备
    start(i) {
      this.$emit('start', i, this.info)
      // 补货管理才能有启动
      // this.selectIndex = i;
      // this.isShowStartModal = true;
    },
    // confirmStart() {
    //   // 开启游戏设备
    //   this.startGameDevice(this.selectIndex + 1, this.length_time);
    // },
    // startGameDevice(channel, time) {
    //   // 启动设备
    //   // 启动设备

    //   let data = {
    //     device_sn: this.info.device_sn,
    //     channel: channel, // 货道
    //     length_time: time,
    //   };
    //   this.$u.api.startUM(data).then((res) => {
    //     this.isShowSuccess("操作成功", 1, () => { }, true);
    //   });
    // },
    //确认 设置用户禁用和启用
    async enableUser(title) {
      try {
        let data = {
          device_sn: this.selectItem.device_sn,
          use_status: this.selectItem.use_status == 1 ? 0 : 1,
          reason: title,
        };
        console.log("禁用设备", data);
        await this.$u.api.setupdateMachineStatus(data)
        this.$emit('refresh')
      } catch (error) {
        console.log("失败返回", error);
      }

    },

    confirmModal(item) {

      this.enableUser(item)
    },
    // confirmDelReturn() {

    //   let data = {
    //     order_id: this.info.order_id,
    //     money: this.info.order_amount
    //     // money:'0.01'
    //   }
    //   if (this.value == '线下退款') {
    //     data['refund_type'] = 1
    //   } else {
    //     data['refund_type'] = 0
    //   }
    //   this.$u.api.refundOrder(data).then((res) => {
    //     this.isShowSuccess("执行成功");
    //     this.$emit('refresh');

    //   })
    //     .catch((err) => {
    //       console.log('退款提交失败', err)
    //     })
    // }

  },
};
</script>

<style lang="scss" scoped>
.card {
  padding: 20rpx 20rpx 0;

  .content {
    position: relative;
  }

  .border-bottom {
    border-bottom: 2rpx solid $dividerColor;
  }

  .border_top {
    border-top: 2rpx solid $dividerColor;
  }

  .flexBottom {

    padding: 3rpx 0;
    display: flex;
    justify-content: center;
    // border: 1px solid #000;
    padding-bottom: 10rpx;
  }

  .flexButton {
    border-top: 2rpx solid $dividerColor;
    padding: 5rpx 0;
    display: flex;
    height: 70rpx;
    // margin: 10rpx 0;
    // margin-bottom: 10rpx;
    font-size: 20rpx;
    justify-content: space-between;

    .returns {
      text-align: center;
      width: 160rpx;
      font-weight: bold;
      padding: 8rpx 5rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      // background: linear-gradient(268deg, #ff5b5b 0%,  #e61f1f 99%);

      background: linear-gradient(268deg, #1fbefd 0%, #46a1ff 99%);
      // margin-left: 95rpx;
      // border: 1px solid #000;
      color: #fff;
    }

  }

  .content {
    box-sizing: content-box;
    display: flex;
    height: 150rpx;
    transition: all 0.4s;
    // padding-bottom: 20rpx;

    .img {
      // border: 1px solid #000;
      width: 150rpx;
      height: 100%;
      // background: #bfbfbf;
    }

    .info {
      flex: 1;
      margin-left: 20rpx;
      height: 100%;
      font-size: $font-size-small;
      color: $textDarkGray;

      .goods_name {
        .name {
          color: $textBlack;
          font-size: $font-size-middle;
          font-weight: bold;
        }

        .status {
          color: $themeComColor;
          font-size: $font-size-xsmall;

          .status_icon {
            width: 30rpx;
            height: 30rpx;
            margin-right: 6rpx;
          }
        }
      }

      .num {
        color: $textBlack;
      }

      .price {
        color: #ef0000;
      }

      .status {
        color: $themeComColor;

        .arrow {
          transition: all 0.4s;
          margin-left: 6rpx;
        }

        .rotate {
          transform: rotate(90deg);
        }

        .status_icon {
          width: 30rpx;
          height: 30rpx;
          margin-right: 6rpx;
        }
      }
    }
  }



  .red {
    text-align: center;
    color: orangered;
    font-size: 22rpx;
  }

  .details {
    padding-bottom: 30rpx;

    >view {
      margin-top: 30rpx;
      display: flex;
      justify-content: space-between;
      font-size: $font-size-base;

      >view {
        &:first-child {
          color: $textDarkGray;
        }

        &:last-child {
          color: $textBlack;
        }
      }

      .flex_white {
        white-space: nowrap;
        margin-right: 20rpx;
      }
    }

    >view>text {
      font-size: 22rpx;
      // padding: 0 10rpx;
      // text-align: right;
      // border: 1px solid #000;
      // width: 500rpx;
      // overflow: hidden;
      // white-space: nowrap;
      /* 防止文本换行 */
      // text-overflow: ellipsis;
      /* 显示省略号 */
    }
  }
}

.imgdis {
  position: absolute;
  right: -9px;
  bottom: 0px;
  width: 214rpx;
  height: 166rpx;

  // border: 1px solid #000;
  image {
    width: 100%;
    height: 100%;
  }
}

.again-open {
  justify-content: flex-end !important;
}
</style>