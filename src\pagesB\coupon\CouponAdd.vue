<template>
  <view>
    <BaseNavbar title="添加优惠券" />
    <view class="form">
      <view class="form-item">
        <view>名称</view>
        <BaseInput
          bgColor="#fff"
          v-model="form.name"
          placeholder="请输入优惠券名称"
        />
      </view>
      <view class="form-item">
        <view>数量</view>
        <BaseInput
          bgColor="#fff"
          v-model="form.total"
          placeholder="请输入优惠券发布数量"
        />
      </view>
      <view class="form-item">
        <view>面额</view>
        <BaseInput
          bgColor="#fff"
          v-model="form.money"
          placeholder="请输入单张优惠券面额"
        />
      </view>
      <view class="form-item">
        <view>备注</view>
        <view class="form-item-textarea">
          <BaseInput
            bgColor="#fff"
            v-model="form.descibe"
            type="textarea"
            placeholder="请输入优惠券备注"
            :border="true"
          />
        </view>
      </view>
      <view class="tips">
        <BaseIcon name="error-circle" color="#0039f3" />
        <view class="tips-text">当前可发布优惠券数量 （0） 张优惠券</view>
      </view>
      <view class="btn">
        <BaseButton @onClick="addHandle">立即发布</BaseButton>
      </view>
    </view>
  </view>
</template>
<script>
import BaseButton from "../../components/base/BaseButton.vue";
import BaseIcon from "../../components/base/BaseIcon.vue";
import BaseInput from "../../components/base/BaseInput.vue";
import BaseNavbar from "@/components/base/BaseNavbar.vue";
export default {
  components: { BaseNavbar, BaseInput, BaseIcon, BaseButton },
  data() {
    return {
      form: {
        name: undefined, //优惠券名称
        money: undefined, //优惠券金额
        total: undefined, //优惠券总数
        descibe: undefined, //优惠券备注
      },
    };
  },

  methods: {
    addHandle() {
      let data = {
        ...this.form,
      };
      this.$u.api.createCouponHotel(data).then(() => {
        this.isShowSuccess("创建成功");
      });
    },
    async getCouponHotelNumHandle() {
      await this.$u.api.getCouponHotelNum();
    },
  },
  onLoad() {
    this.getCouponHotelNumHandle();
  },
};
</script>
<style lang="scss">
page {
  background-color: $pageBgColor;
}
</style>

<style scoped  lang='scss'>
.form {
  padding: 50rpx;
  background-color: #fff;
  &-item {
    display: flex;
    align-items: center;
    border-bottom: 2rpx solid #bdc0c4;
    padding: 10rpx 4rpx;
    color: #333;
    font-size: 32rpx;
    &-textarea {
      flex: 1;
      border: 2rpx solid #bdc0c4;
      border-radius: 20rpx;
      margin-left: 10rpx;
      overflow: hidden;
    }
  }
  .tips {
    display: flex;
    align-items: center;
    color: 3333333;
    font-size: 22rpx;
    margin: 30rpx 0;
    &-text {
      margin-left: 10rpx;
    }
  }
}
</style>