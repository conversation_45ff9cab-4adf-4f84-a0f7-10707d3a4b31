<template>
  <view>
    <BaseNavbar :title="title" />
    <view><video class="video" src=""></video></view>
    <view class="title">
      <view class="title-txt">课程标题</view>
      <view class="title-content"
        >课程介绍课程介绍课程介绍课程介绍课程介绍课程介绍课程介绍课程介绍课程介绍课程介绍课程介绍课程介绍课程介绍课程介绍课程介绍课程介绍课程介绍课程介绍课程介绍课程介绍课程介绍课程介绍</view
      >
    </view>
    <view class="relevant">
      <view class="relevant-title">相关视频</view>
      <view class="relevant-list">
        <CloudVideoCard v-for="item in 5" :key="item" />
      </view>
    </view>
    <SafeBlock />
  </view>
</template>

<script>
import BaseNavbar from "@/components/base/BaseNavbar.vue";
import CloudVideoCard from "@/components/card/CloudVideoCard.vue";
import SafeBlock from "@/components/common/SafeBlock.vue";
export default {
  components: { BaseNavbar, CloudVideoCard, SafeBlock },
  data() {
    return {
      title: "课程详情",
    };
  },
};
</script>

<style lang="scss" scoped>
.video {
  width: 100%;
  height: 380rpx;
  background-color: $textDarkGray;
}
.title {
  padding: 60rpx 30rpx;
  color: $textBlack;
  &-txt {
    font-weight: bold;
    font-size: $font-size-xlarge;
  }
  &-content {
    font-size: $font-size-base;
    margin-top: 20rpx;
  }
}
.relevant {
  padding: 0 30rpx;
  &-title {
    font-weight: bold;
    font-size: $font-size-xlarge;
  }
}
</style>