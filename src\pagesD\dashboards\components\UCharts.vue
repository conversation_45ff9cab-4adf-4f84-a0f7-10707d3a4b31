<template>
  <view class="card-charts">

    <canvas :canvas-id="id" :id="id" class="charts" @touchend="touchend" @touchstart="touchstart" :canvas2d="true"
      type="2d" @touchmove="touchmove" />
  </view>
</template>
<script>
//文档
// https://www.ucharts.cn/v2/#/
import uCharts from "@qiun/ucharts";

var uChartsInstance = {};
export default {
  name: "UCharts",
  props: {
    lineData: { type: Array, require: true,  default: function(){
      return []
    } },
    chartsName: { type: String, default: "我的销售额" },
  },
  watch: {
    lineData: {
      handler: function (val) {
        if (val?.length == 0) return;
        this.getServerData();
      },
    },
  },
  computed: {
    totalNum() {
      return (
        this.lineData
          ?.reduce(
            (total, nowVal) =>
              (parseFloat(total) * 1000 + parseFloat(nowVal.amount) * 1000) /
              1000,
            0
          )
          ?.toFixed(2) || "0.00"
      );
    },
  },
  data() {
    return {
      cWidth: 750,
      cHeight: 500,
      id: "ImmkADWetCJGHQRAYJhkqtmpFCKLoeWW",
      pixelRatio: 2,
    };
  },
  methods: {
    getServerData() {
      let res = {
        categories: [],
        series: [{ name: this.chartsName, data: [] }],
      };
      this.lineData?.forEach((el) => {
        res.categories.push(el.time_point);
        res.series[0]?.data.push(el.amount);
      });
      this.drawCharts(this.id, res);
    },
    /**
     *
     * @param {*} id
     * @param {*} data
     *  此方法解决层级过高问题
     *  此方法需要开启canvas2d="true" type="2d"  2d模式，必须要执行此方法
     *  此方法模拟器层级会过高
     *  无法真机调试
     *  必须真机预览才是最终正常结果
     *  https://www.ucharts.cn/v2/#/help/index，详情可以查看文档
     */
    drawCharts(id, data) {
      const query = uni.createSelectorQuery().in(this);
      query
        .select("#" + id)
        .fields({ node: true, size: true })
        .exec((res) => {
          if (res[0]) {
            const canvas = res[0].node;
            const ctx = canvas.getContext("2d");
            canvas.width = res[0].width * this.pixelRatio;
            canvas.height = res[0].height * this.pixelRatio;
            uChartsInstance[id] = new uCharts({
              type: "area",
              context: ctx,
              width: this.cWidth * this.pixelRatio,
              height: this.cHeight * this.pixelRatio,
              categories: data.categories,
              series: data.series,
              pixelRatio: this.pixelRatio,
              animation: true,
              background: "#FFFFFF",
              color: [
                "#1a76b3",
                "#91CB74",
                "#FAC858",
                "#EE6666",
                "#73C0DE",
                "#3CA272",
                "#FC8452",
                "#9A60B4",
                "#ea7ccc",
              ],
              padding: [15, 0, 0, 10],
              legend: {},
              enableScroll: true,

              xAxis: {
                disableGrid: true,
                itemCount: 3,
                scrollShow: true,
                scrollAlign: "right",

              },
            
              yAxis: {

                gridType: "dash",
                dashLength: 2,
              },
              extra: {
                // line: {
                //     type: "curve",
                //     width: 2,
                // },
                area: {
                  type: "curve",
                  opacity: 0.6,
                  addLine: true,
                  width: 2,
                  gradient: true,
                },
              },
            });
          } else {
            console.error("[uCharts]: 未获取到 context");
          }
        });
    },

    touchstart(e) {
      uChartsInstance[e.target.id].scrollStart(e);
    },
    touchmove(e) {
      uChartsInstance[e.target.id].scroll(e);
    },
    touchend(e) {
      uChartsInstance[e.target.id].scrollEnd(e);
      uChartsInstance[e.target.id].touchLegend(e);
      uChartsInstance[e.target.id].showToolTip(e);
    },
  },
  created() {
    //这里的 750 对应 css .charts 的 width
    this.cWidth = uni.upx2px(650);

    //这里的 500 对应 css .charts 的 height
    this.cHeight = uni.upx2px(400);
    this.pixelRatio = uni.getSystemInfoSync().pixelRatio;
  },
};
</script>


<style scoped  lang='scss'>
.card-charts {
  background-color: #fff;
}

.total {

  font-size: 18rpx;
  font-weight: 650;
  color: $textBlack;
}

.charts {
  width: 650rpx;
  height: 500rpx;
}
</style>