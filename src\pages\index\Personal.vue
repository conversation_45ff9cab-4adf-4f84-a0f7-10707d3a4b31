<template>
  <view>
    <BaseNavbar
      title="我的"
    />
    <view class="my-personal">
      <view class="user">
        <image class="user_bg" src="@/static/img/personal-bg.jpg" />
        <view class="user_info">
          <view class="user_info_box flexColumnAllCenter">
            <!--   #ifndef H5 -->
            <open-data class="user_info_icon" type="userAvatarUrl"></open-data>
              
            <open-data
              class="user_info_username"
              type="userNickName"
            ></open-data>
            <!-- #endif -->
              <!--   #ifdef H5 -->
            <view class="user_info_icon">
              <image src="@/static/img/icon/logo.png"></image>
            </view>
            <view class="user_info_username">
              
                {{ vUserInfo.userName }}
              
            </view>
               <!-- #endif -->
            <view class="ser_info_id" v-if="vUserInfo.user">账号:{{ vUserInfo.user.user_login }}({{vUserInfo.role_name||""}}) </view>
          </view>
          <view class="user_info_notice" @click="goNotice">
            <image
              class="notice-icon"
              src="@/static/img/icon/notice-icon-personal.png"
            />
            <view v-if="vNoticeNum > 0" class="circle"></view>
          </view>
        </view>
      </view>
      <!-- <view class="balance flexRowBetween">
        <view class="balance_txt">
          余额<text class="money">{{ vUserInfo.user.cash }}</text> 元
        </view>
        <view class="balance_btn flexRowAllCenter" @click="goWithdrawal"
          v-if="vUserInfo.user.cash > 0">提现</view
        >
      </view> -->
      <view class="cell">
        <u-cell-group :border="false">
          <u-cell-item
            v-for="(item, i) in cellList"
            :key="i"
            :title="item.title"
            :value="item.value"
            :border-bottom="false"
            :border-top="false"
            :title-style="titleStyle"
            :value-style="titleStyle"
            :arrow="item.isShowArrow"
            @click="onClickCell(item.path)"
          >
            <image class="cell_left_img" slot="icon" :src="item.src" />
          </u-cell-item>
        </u-cell-group>
      </view>
    </view>
    <MyTabbar />
  </view>
</template>

<script>
import BaseNavbar from "@/components/base/BaseNavbar.vue";
import MyTabbar from "./MyTabbar.vue";
export default {
  components: { MyTabbar, BaseNavbar },
  data() {
    return {
      titleStyle: {
        color: "#333",
        fontSize: "28rpx",
        fontWeight: "bold",
      },
      cellList: [],
    };
  },
  methods: {
    onClickCell(url) {
      uni.navigateTo({ url });
    },
    goWithdrawal() {
      uni.navigateTo({ url: "/pagesB/finance/Withdrawal" });
    },
    goNotice() {
      uni.navigateTo({
        url: "/pagesB/notice/NoticeList",
      });
    },
  },
  onLoad(options) {
    this.cellList = [
      {
        src: require("@/static/img/icon/help-icon.png"),
        title: "帮助中心",
        value: "",
        path: "/pagesC/help/HelpCenter",
        isShowArrow: true,
      },
      {
        src: require("@/static/img/icon/version-icon.png"),
        title: "版本",
        value: this.vSiteConfig.site_info.site_version,
        path: "",
        isShowArrow: false,
      },
      {
        src: require("@/static/img/icon/setuo-icon.png"),
        title: "设置",
        value: "",
        path: "/pagesC/set/SetUp",
        isShowArrow: true,
      },
      {
        src: require("@/static/img/icon/about-we-icon.png"),
        title: "关于我们",
        value: "",
        path: "/pagesC/about/AboutUs",
        isShowArrow: true,
      },
    ];
  },
};
</script>

<style lang="scss" scoped>
.my-personal {
  .user {
    position: relative;
    height: 434rpx;
    &_bg {
      width: 100%;
      height: 100%;
    }
    &_info {
      position: absolute;
      width: 100%;
      height: 100%;
      top: 0;
      left: 0;
      color: $textWhite;
      &_box {
        position: absolute;
        bottom: 48rpx;
        left: 50%;
        transform: translateX(-50%);
      }
      &_icon {
        width: 120rpx;
        height: 120rpx;
        border-radius: 50%;
        overflow: hidden;
      }
      &_username {
        font-size: $font-size-middle;
        font-weight: bold;
        margin: 20rpx 0;
      }
      &_id {
        font-size: $font-size-small;
      }

      &_notice {
        position: absolute;
        right: 38rpx;
        bottom: 206rpx;
        z-index: 999;
        .notice-icon {
          width: 40rpx;
          height: 45rpx;
        }
        .circle {
          position: absolute;
          right: 0;
          top: 0;
          width: 12rpx;
          height: 12rpx;
          border-radius: 50%;
          background-color: $textWarn;
        }
      }
    }
  }
  .balance {
    height: 100rpx;
    background: #f4fcff;
    padding: 0 60rpx 0 90rpx;
    &_txt {
      color: $textBlack;
      font-size: $font-size-base;
      .money {
        font-size: 48rpx;
        font-weight: bold;
        margin: 0 6rpx 0 20rpx;
      }
    }
    &_btn {
      width: 200rpx;
      height: 70rpx;
      background: #0eade2;
      border-radius: 35rpx;
      color: $textWhite;
      font-size: $font-size-middle;
    }
  }
  .cell {
    padding: 60rpx 30rpx 0;
    &_left_img {
      width: 50rpx;
      height: 50rpx;
      margin-right: 26rpx;
    }
  }
}
.user_info_icon image{
  width: 100%;
  height: 100%;
}
</style>