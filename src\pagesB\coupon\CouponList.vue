<template>
  <view>
    <BaseNavbar title="优惠券列表" />
    <BaseTabs
      :list="tabList"
      @change="tabChange"
      height="90"
      :isShowBar="true"
      :current="curTabIndex"
    />
    <ComList :loadingType="loadingType">
      <CouponListCard v-for="item in listData" :key="item.id" :info="item" />
    </ComList>
    <view class="btn" :style="{ bottom: vIphoneXBottomHeight + 20 + 'rpx' }">
      <BaseButton shape="circle" @onClick="goAdd">添加优惠券</BaseButton>
    </view>
  </view>
</template>
<script>
import BaseNavbar from "@/components/base/BaseNavbar.vue";
import ComList from "@/components/list/ComList.vue";
import CouponListCard from "@/pagesB/coupon/components/CouponListCard.vue";
import myPull from "@/mixins/myPull";
import BaseTabs from "../../components/base/BaseTabs.vue";
import BaseButton from "../../components/base/BaseButton.vue";
export default {
  components: { BaseNavbar, ComList, CouponListCard, BaseTabs, BaseButton },
  mixins: [myPull()],
  data() {
    return {
      tabList: [
        {
          name: "全部",
          status: "",
        },
        {
          name: "未领完",
          status: 0,
        },
        {
          name: "已领完",
          status: 1,
        },
      ],
      curTabIndex: 0,
    };
  },

  methods: {
    tabChange(e) {
      this.curTabIndex = e;
      this.refresh();
    },
    getList(page, done) {
      let data = {
        page,
        limit: 10,
      };
      this.$u.api.getCouponHotelList(data).then((res) => {
        done(res);
      })
      .catch((err=>{
        console.log('错误信息',err)
      }))
    },
    goAdd() {
      uni.navigateTo({ url: "/pagesB/coupon/CouponAdd" });
    },
  },
  onLoad() {
    this.refresh();
  },
};
</script>

<style lang="scss">
page {
  background-color: $pageBgColor;
}
</style>
<style scoped  lang='scss'>
.btn {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 20rpx;
  padding: 0 34rpx;
}
</style>