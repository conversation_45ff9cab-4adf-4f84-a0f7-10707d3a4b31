<template>
  <view>
    <BaseNavbar :title="title" />
    <view class="title">
      <text>清除账号登录记录</text>
    </view>

    <view class="list">
      <view class="list-li" v-for="(item, i) in vUserList"
        :key="i">
         <view class="list-pie" v-show="i == 0">
          <text>当前</text>
        </view>
        <img :src="logoImg" alt="" />
        <text>{{item.userName}}</text>
        <button class="list-btn" @click="delitem(item,i)">清除</button>
      </view>
    </view>
  </view>
</template>

<script>
import BaseNavbar from "@/components/base/BaseNavbar.vue";
import BaseIcon from "@/components/base/BaseIcon.vue";
import BaseButton from "@/components/base/BaseButton.vue";
import { mapState, mapMutations } from "vuex";
export default {
  components: { BaseNavbar, BaseIcon, BaseButton },
  data() {
    return {
      title: "切换账号",
      list: [
        {
          img_url: "",
          phone: "13838681507",
          password: "123456",
        },
        {
          img_url: "",
          phone: "13838681507",
          password: "123456",
        },
        {
          img_url: "",
          phone: "13838681507",
          password: "123456",
        },
        {
          img_url: "",
          phone: "13838681507",
          password: "123456",
        },
        {
          img_url: "",
          phone: "13838681507",
          password: "123456",
        },
      ],
    };
  },
  computed: {
    ...mapState(["vUserList"]),
    logoImg() {
      return this.vSiteConfig?.site_info?.site_logo || "";
    
  },
  },
  methods: {
    ...mapMutations(["deleteItem"]),
    goPage(url) {
      uni.navigateTo({ url });
    },
    delitem(item,i){
      if (i==0) {
         this.$u.toast("不能删除当前用户~");
      }else{
        this.deleteItem(item)
      }
    },
    
  },
};
</script>

<style lang="scss">
page {
  background-color: $pageBgColor;
}
</style>

<style lang="scss" scoped>
.title {
  text-align: center;
  // border: 1px solid #000;
  margin: 40rpx 0;
  font-size: 50rpx;
  color: #555555;
}
.list {
  // border: 1px solid #000;
  .list-li {
    height: 150rpx;
    margin: 25rpx 35rpx;
    background-color: white;
    border-radius: 15rpx;
    display: flex;
    align-items: center;
    position: relative;
    // border: 1px solid #000;
    img {
      margin: 0 20rpx;
      vertical-align: middle;
      width: 90rpx;
      height: 90rpx;
    }
    >text {
      width: 300rpx;
      font-size: 35rpx;
      font-weight: bold;

      vertical-align: middle;
    }
    .list-pie {
      position: absolute;
      top: 0;
      right: 0;
      font-size: 24rpx;
      padding: 5rpx 20rpx;
      border-top-right-radius: 10rpx;
      border-bottom-left-radius: 10rpx;
      color: white;
      background: #206cc5;
       background-image: linear-gradient(90deg, #7fbad1,#4da2e7,#0473f1);
    }
    .list-btn{
      background-color: #FF2121;;
      color: white;
      font-size: 23rpx;
    }
  }
}
.buttom {
  margin-top: 50rpx;
  font-size: 30rpx;
  text-align: center;
  color: #206cc5;
}
</style>