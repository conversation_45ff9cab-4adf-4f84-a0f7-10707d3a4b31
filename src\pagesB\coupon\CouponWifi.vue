<template>
  <view>
    <BaseNavbar title="创建WiFi" />
    <view class="top">
      <view class="top-item">
        <BaseIcon name="wifi" color="#0039f3" />
        <view class="top-item-text">一键快速链接</view>
      </view>
      <view class="top-item">
        <BaseIcon name="lock" color="#0039f3" />
        <view class="top-item-text">无需告知密码</view>
      </view>
    </view>
    <view class="form">
      <view class="form-item">
        <view>{{vPointName}}名称</view>
        <BaseInput
          placeholder="请输入名称"
          v-model="form.hotelName"
          bgColor="#fff"
        />
      </view>
      <view class="form-item">
        <view>WiFi名称</view>
        <BaseInput
          placeholder="请输入WiFi名称"
          v-model="form.wifi_name"
          bgColor="#fff"
        />
      </view>
      <view class="form-item">
        <view>WiFi密码</view>
        <BaseInput
          placeholder="请输入WiFi密码"
          v-model="form.wifi_psd"
          bgColor="#fff"
        />
      </view>
      <view class="form-tips">
        <BaseIcon name="error-circle" color="#0039f3" />
        <view class="form-tips-text">WiFi密码不会被共享，请放心填写</view>
      </view>
    </view>
    <view class="btn" hover-class="btn-active" @click="editWifiHandle">
      一键修改WiFi密码
    </view>
  </view>
</template>
<script>
import BaseButton from "../../components/base/BaseButton.vue";
import BaseIcon from "../../components/base/BaseIcon.vue";
import BaseInput from "../../components/base/BaseInput.vue";
import BaseNavbar from "@/components/base/BaseNavbar.vue";
export default {
  components: { BaseNavbar, BaseIcon, BaseInput, BaseButton },
  data() {
    return {
      form: {
        hotelName: undefined,
        wifi_name: undefined,
        wifi_psd: undefined,
        id: undefined,
      },
    };
  },

  methods: {
    async editWifiHandle() {
      let data = {
        ...this.form,
      };
      await this.$u.api.editHotel(data);
      this.isShowSuccess("修改成功", 1, null, true);
    },
  },
  onLoad(opt) {
    const {
      hotelName = undefined,
      wifi_name = undefined,
      wifi_psd = undefined,
      id,
    } = opt;
    this.form = {
      hotelName,
      wifi_name,
      wifi_psd,
      id,
    };
  },
};
</script>


<style lang="scss">
page {
  background-color: $pageBgColor;
}
</style>
<style scoped  lang='scss'>
.top {
  display: flex;
  justify-content: center;
  &-item {
    display: flex;
    justify-content: center;
    align-items: center;
    color: #666666;
    font-size: 24rpx;
    padding: 30rpx 0;
    &-text {
      margin-left: 16rpx;
    }
    &:nth-child(2) {
      margin-left: 96rpx;
    }
  }
}
.form {
  padding: 0 50rpx;
  background-color: #fff;
  &-item {
    display: flex;
    align-items: center;
    color: #333333;
    font-size: 32rpx;
    padding: 6rpx;
    border-bottom: 2rpx solid #bdc0c4;
  }
  &-tips {
    display: flex;
    align-items: center;
    padding: 34rpx 0;
    &-text {
      color: #21b53d;
      font-size: 22rpx;
      margin-left: 18rpx;
    }
  }
}
.btn {
  margin: 30rpx 50rpx;
  padding: 26rpx 0;
  text-align: center;
  color: #fff;
  font-size: 32rpx;
  background-color: #0039f3;
  transition: all 0.3s;
}
.btn-active {
  opacity: 0.8;
}
</style>