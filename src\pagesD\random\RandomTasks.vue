<template>
  <view>
    <BaseNavbar :title="title" />  
    <ComList :loading-type="loadingType">
      <RandomTasksCard
        v-for="(item,i) in listData"
        :key="item.id"
        :info="item"
        :i="i"
        @setDivide="popShow(item,'setDivide')"
        @editDivide="editDivide(item)"
        @deletDivide="popShow(item,'deletDivide')"
      />
    </ComList>
   
    <FixedAddIcon @onAdd="goAddUser" />
    <BaseModal
        :show.sync="isShowModal"
        @confirm="confirmModal"
        :content="modalContent"
      >
        <template v-if="isDeletDivide">
          <view class="text_center">确定删除该任务吗?</view>
        </template>
        <template v-if="isSetDivide">
          <view class="text_center">确定{{selectItem
          .status==1?'暂停':'启动'}}该任务吗?</view>
        </template>
      </BaseModal>
  </view>
</template>

<script>
import BaseNavbar from "@/components/base/BaseNavbar.vue";
import BaseSearch from "@/components/base/BaseSearch.vue";
import myPull from "@/mixins/myPull.js";
import ComList from "@/components/list/ComList.vue";

import RandomTasksCard from "../components/Card/RandomTasksCard.vue";
import FixedAddIcon from "@/components/common/FixedAddIcon.vue";
import BaseModal from "@/components/base/BaseModal.vue";
import BaseInput from "@/components/base/BaseInput.vue";
export default {
  components: {
    BaseNavbar,
    BaseSearch,
    ComList,
    RandomTasksCard,
    FixedAddIcon,
    BaseModal,
    BaseInput,
  },
  data() {
    return {
      title: "定时出泡泡任务管理",
      isShowModal: false,
      isSetDivide: false,
      isDeletDivide: false,
      searchValue: "", //搜索框value
 
      selectItem: {},
      hotel_id:''
    };
  },
  methods: {
    goAddUser() {
      if (this.listData.length>2) {
        return this.isShowErr("最多可设置三个定时出泡泡任务");
      }
      uni.navigateTo({ url: `/pagesD/random/RandomTasksAdd?from=add&hotel_id=${this.hotel_id}` });
    },

    //确认设置分成
    async setDivide() {
      try{
      let data = {
        id:this.selectItem.id,
        status:this.selectItem.status==1?0:1
      };
      await this.$u.api.setUpdateRand(data)
      this.refresh();
      }catch(error){
        console.log(error)
      }
      
    },
    //确认 设置用户禁用和启用
    editDivide(item) {
      uni.navigateTo({ url: '/pagesD/random/RandomTasksAdd?from=edit&data='+encodeURIComponent(JSON.stringify(item)) });
      
    },

    async getList(page, done) {
      try{
        let data = {
        hotel_id:this.hotel_id
      };
      let res=await this.$u.api.getRandList(data);
        done(res.data);
      }catch(error){
        console.log(error);
      }
     
    },
    search(val) {
      this.searchValue = val;
      this.refresh();
    },

    popShow(item, from) {
      this.selectItem = item;
      // this.modalContent = "";

      if (from == "setDivide") {
        this.isSetDivide = true;

        this.isDeletDivide = false;
        this.isShowModal=true
      } else if(from=="deletDivide"){
        this.isDeletDivide = true;
        this.isSetDivide = false;
        this.isShowModal=true
      } 
    
    },
    confirmModal() {
      if (this.isSetDivide) {
        this.setDivide();
      } else if (this.isDeletDivide) {
        this.del(this.selectItem);
      } 
    },
    
    async del(item) {
      try{
        let data={
        id:item.id
      }
      await this.$u.api.setDeletRand(data)
      this.refresh();
      }catch(error){
        console.log(error)
      }
      
    },
  },
  onLoad(options) {
    console.log('传递的数局',options)
    this.hotel_id=options.hotel_id
    this.refresh();
  },
  onShow() {
   /*  #ifndef H5 */
    let pages = getCurrentPages();
    let currPage = pages[pages.length - 1]; // 当前页
    if (currPage.data.isDoRefresh == true) {
      // 是否刷新
      currPage.data.isDoRefresh = false;
      this.refresh();
    }
    /*#endif */
    /*  #ifdef H5 */
    if (this.vCurrPage.isDoRefresh == true) {
      // 是否刷新
      this.vCurrPage.isDoRefresh = false;
      this.refresh();
    }
    /*#endif */
  },
  mixins: [myPull()],
};
</script>

<style lang="scss">
page {
  background-color: $pageBgColor;
}
</style>

<style lang="scss" scoped>
.text_center{
  text-align: center;
}
</style>