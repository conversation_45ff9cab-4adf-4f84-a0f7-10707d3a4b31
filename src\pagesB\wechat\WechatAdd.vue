<template>
  <view>
    <BaseNavbar :title="title" />
    <view class="content">
      <view class="content-box">
        <view class="title">公众号名称</view>
        <view class="txt">
          <BaseInput placeholder="请输入公众号名称" v-model="formInfo.app_name" />
        </view>
      </view>
      <view class="content-box">
        <view class="title">AppId</view>
        <view class="txt">
          <BaseInput placeholder="请输入AppId" v-model="formInfo.app_id" />
        </view>
      </view>

      <view class="content-box">
        <view class="title">AppSecret</view>
        <view class="txt">
          <BaseInput placeholder="请输入AppSecret" v-model="formInfo.app_secret" />
        </view>
      </view>

      <view class="content-box">
        <view class="title">MchId</view>
        <view class="txt">
          <BaseInput placeholder="请输入MchId" v-model="formInfo.mch_id" />
        </view>
      </view>

      <view class="content-box">
        <view class="title">MchKey</view>
        <view class="txt">
          <BaseInput placeholder="请输入MchKey" v-model="formInfo.mch_key" />
        </view>
      </view>
      <view class="content-box">
        <view class="title">{{ typeInfo.name }}图片</view>
        <view class="txt">
          <BaseUpload :maxCount="1" :auto="true" :fileList="defaultImgList" @onUpload="onUploadImg" />
        </view>
      </view>
      <bloack v-if="typeInfo.type == 1">
        <view class="content-box">
          <view class="title">Token</view>
          <view class="txt">
            <BaseInput placeholder="请输入Token" v-model="formInfo.token" />
          </view>
        </view>

        <view class="content-box">
          <view class="title">Encodingaeskey</view>
          <view class="txt">
            <BaseInput placeholder="请输入Encodingaeskey" v-model="formInfo.encodingaeskey" />
          </view>
        </view>

        <view class="content-box">
          <view class="title">回调地址</view>
          <view class="txt">
            <BaseInput placeholder="请输入回调地址" v-model="formInfo.cb_addr" />
          </view>
        </view>
      </bloack>
      <block v-if="typeInfo.type == 2">
        <view class="content-box">
          <view class="title">AppGh</view>
          <view class="txt">
            <BaseInput placeholder="请输入AppGh" v-model="formInfo.app_gh" />
          </view>
        </view>

        <view class="content-box">
          <view class="title">推送的标题</view>
          <view class="txt">
            <BaseInput placeholder="请输入推送的标题" v-model="formInfo.send_title" />
          </view>
        </view>

        <view class="content-box">
          <view class="title">推送的跳转Page地址</view>
          <view class="txt">
            <BaseInput placeholder="请输入推送的跳转Page地址" v-model="formInfo.send_page" />
          </view>
        </view>

        <view class="content-box">
          <view class="title">推送的图片</view>
          <view class="txt">
            <BaseUpload :maxCount="1" :auto="true" :fileList="defaultSendList" @onUpload="onUploadSend" />
          </view>
        </view>
      </block>
    </view>
    <!-- 保存编辑按钮 -->
    <view class="btn-box" :style="{ paddingBottom: 20 + vIphoneXBottomHeight + 'rpx' }">
      <BaseButton type="primary" @onClick="save">保 存</BaseButton>
    </view>
    <SafeBlock :height="120" />
  </view>
</template>

<script>
import BaseNavbar from "@/components/base/BaseNavbar.vue";
import BaseInput from "../../components/base/BaseInput.vue";
import BaseUpload from "../../components/base/BaseUpload.vue";
import SafeBlock from "../../components/common/SafeBlock.vue";
import BaseButton from "../../components/base/BaseButton.vue";
export default {
  components: { BaseNavbar, BaseInput, BaseUpload, SafeBlock, BaseButton },
  data() {
    return {
      title: "新增公众号",
      fromData: "",
      isFromAdd: false,
      isFromEdit: false,
      defaultImgList: [],
      defaultSendList: [],
      typeInfo: {
        type: 1,
        name: "公众号",
      },
      formInfo: {},
      id: 0, //编辑ID
      typeInfoDefault: {
        official: [
          "app_name",
          "app_id",
          "app_secret",
          "mch_id",
          "mch_key",
          "wx_img",
          "token",
          "encodingaeskey",
          "cb_addr",
        ],

        applet: [
          "app_name",
          "app_id",
          "app_secret",
          "mch_id",
          "mch_key",
          "wx_img",
          "app_gh",
          "send_title",
          "send_page",
          "send_img",
        ],
      },
    };
  },
  methods: {
    onUploadSend(e) {
      this.formInfo.send_img = e?.[0] || "";
    },
    onUploadImg(e) {
      this.formInfo.wx_img = e?.[0] || "";
    },
    async save() {
      if (this.typeInfo.type == 1 && Object.keys(this.formInfo).length < 9)
        return this.isShowErr("请填写完成的信息~");

      if (this.typeInfo.type == 2 && Object.keys(this.formInfo).length < 10)
        return this.isShowErr("请填写完成的信息~");

      for (let i in this.formInfo) {
        if (!this.formInfo[i]) return this.isShowErr("请填写完成的信息~");
      }

      let data = {
        ...this.formInfo,
        type: this.typeInfo.type,
      },
        rtn = null;
      if (this.typeInfo.type == 1) data["is_enter_defult"] = 0;
      if (this.isFromAdd) {
        rtn = await this.$u.api.addWechat(data);
      } else if (this.isFromEdit) {
        data["id"] = this.id;
        rtn = await this.$u.api.editWechat(data);
      }
      rtn && this.isShowSuccess("保存成功", 1, null, true);
    },
    //编辑获取公众号信息
    getWechatOne() {
      let data = {
        id: this.id,
      };
      this.$u.api.getWechatOne(data).then((res) => {
        this.typeInfoDefault[
          this.typeInfo.type == 1 ? "official" : "applet"
        ].forEach((item) => {
          res[item] && (this.formInfo[item] = res[item]);
        });
        res?.wx_img && (this.defaultImgList = [{ url: res.wx_img }]);
        res?.send_img && (this.defaultSendList = [{ url: res.send_img }]);
      });
    },
  },
  onLoad(opt) {
    this.fromData = opt?.from;
    this.typeInfo.name = opt?.type == 1 ? "公众号" : "小程序";
    this.typeInfo.type = opt?.type || 1;
    if (opt?.from === "add") {
      this.isFromAdd = true;
      this.title = `新增${this.typeInfo.name}`;
    } else if (opt?.from === "edit") {
      this.isFromEdit = true;
      this.id = opt?.id;

      this.title = `编辑${this.typeInfo.name}`;
      this.getWechatOne();
    }
  },
};
</script>

<style lang="scss" scoped>
.content {
  padding: 20rpx;

  &-box {
    margin-bottom: 30rpx;

    &:last-child {
      margin-bottom: 0;
    }

    .title {
      color: $textBlack;
      font-size: $font-size-middle;
      font-weight: 700;
      margin-bottom: 20rpx;
    }
  }
}

.btn-box {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 20rpx;
  background-color: #fff;
  z-index: 9999;
}
</style>