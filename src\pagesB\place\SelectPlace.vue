<template>
  <view class="selectHotel">
    <BaseNavbar
      :title="`选择${from == 'home_placeAdd' || from == 'dataAnalsis' ? '账号' : from == 'remittance' ? '总行编码' : from == 'branch' ? '开户行名称' : vPointName}`" />
    <BaseSearch
      :placeholder="'请输入' + `${from == 'home_placeAdd' || from == 'dataAnalsis' ? '账号' : from == 'remittance' ? '总行名称' : from == 'branch' ? '开户行名称' : vPointName}`"
      @search="search" listType="selectplace" :isShowSerch="false" />
    <!-- <BaseList listType="selectplace" @searchChange="searchChange" /> -->
    <view class="hotelItemContent">
      <ComList :loading-type="loadingType">
        <view v-for="(item, index) in listData" :key="index" class="hotelItem">
          <radio :checked="selectIndex == index" color="#2979ff" style="transform: scale(0.7);" />
          <block v-if="from == 'remittance'">
            <view class="hotelItemDesc" @click="select(item, index)">
              <view class="hotelItemDescContent">
                <view>
                  <view>
                    总行：{{ item.bank_name ? item.bank_name : '' }}
                  </view>
                </view>
                <view>
                  <view>
                    总行编码：
                    {{ item.bank_code ? item.bank_code : '' }}
                  </view>
                </view>
              </view>
            </view>
          </block>
          <block v-else-if="from == 'branch'">
            <view class="hotelItemDescContent" @click="select(item, index)">
              <view>
                <view>
                  开户行：{{ item.bank_name ? item.bank_name : '' }}
                </view>
              </view>
              <view>
                <view>
                  开户行编码：
                  {{ item.bank_code ? item.bank_code : '' }}
                </view>
              </view>
            </view>
          </block>
          <block v-else-if="from == 'home_placeAdd' || from == 'dataAnalsis'">
            <view class="hotelItemDesc" @click="select(item, index)">
              <view class="hotelItemDescContent">
                <view>
                  <view class="hotelName">
                    {{ item.user_login ? item.user_login : '' }}
                  </view>
                </view>
                <view>
                  <view class="hotelName">
                    {{ item.roleName ? item.roleName : '' }}
                  </view>
                </view>
              </view>

              <view class="linkContent">
                <view>
                  <view class="hotelName">
                    分成比例：{{
        item && item.per ? `${(item.per * 1).toFixed(0)}%` : '0%'
      }}
                  </view>
                </view>
              </view>
            </view>
          </block>
          <block v-else>
            <view class="hotelItemDesc" @click="select(item, index)">
              <view class="hotelItemDescContent">
                <view>
                  <view class="hotelName">
                    {{ item.hotelName ? item.hotelName : '' }}
                  </view>
                </view>
                <view>
                  <view class="address">
                    {{ item.address ? item.address : '' }}
                  </view>
                </view>
              </view>
              <view class="linkContent">
                <view>
                  <view class="linkman">
                    {{ item.linkman ? item.linkman : '' }}
                  </view>
                  <view class="phoneNumber">
                    {{ item.linkmanPhone ? item.linkmanPhone : '' }}
                  </view>
                </view>
              </view>
            </view>

          </block>

        </view>
      </ComList>
    </view>
    <!-- <view @click="ok()">
      确认
    </view> -->
  </view>
</template>

<script>
import myPull from '@/mixins/myPull.js'
import BaseSearch from '@/components/base/BaseSearch.vue'
import BaseNavbar from '@/components/base/BaseNavbar.vue'
import ComList from '@/components/list/ComList.vue'
// import BaseList from '@/components/base/BaseList.vue'
import { AddValueInObject } from '@/wxutil/list'
export default {
  data() {
    return {
      code: 0,
      isFoucs: false,
      isConfirm: false,
      from: '',
      hotelName: '',
      selectIndex: -1,
      hotelName: '', //搜索
      bank_code: '',
      type: 1,
    }
  },
  components: {
    BaseNavbar,
    BaseSearch,
    ComList,
    // BaseList
  },

  methods: {
    ok() {
      let data = this.listData.filter((item) => item.checked == true)
    },
    async getList(page, done) {
      // 获取数据
      let rtnData
      if (this.from == 'home_placeAdd') {
        let data = {
          user_login: this.hotelName,
          select_role_id: 6,
          page: page,
        }
        rtnData = await this.$u.api.getMyUserList(data)
      } else if (this.from == 'dataAnalsis') {
        let data = {
          user_login: this.hotelName,
          select_role_id: '3,4,5',
          page: page,
        }
        rtnData = await this.$u.api.getMyUserList(data)
      } else if (this.from == 'remittance') {
        let data = {
          bank_name: this.hotelName,
          page: page,
          type: this.type,
          limit: 10,
        }
        // rtnData = {data:[{ ods_name: '中国银行', code: '10025' }, { ods_name: '建设银行', code: '10028' }]}
        rtnData = await this.$u.api.getBankList(data)
      }
      else if (this.from == 'branch') {
        let data = {
          bank_name: this.hotelName,
          bank_code: this.bank_code,
          page: page,
          type: this.type,
          limit: 10,
        }
        // rtnData = {data:[{ ods_name: '中国银行', code: '100285', ranch_name: '中国银行四海支行', }, { ods_name: '建设银行', ranch_name: '建设银行东北支行', code: '100286' }]}
        rtnData = await this.$u.api.getBankList(data)
      } else {
        let data = {
          hotel_name: this.hotelName,
          page: page,
          limit: 10,
        }
        rtnData = await this.$u.api.getMyHotels(data)
      }
      if (rtnData.data) {
        done(rtnData.data)
      } else if (rtnData.length > 0) {
        done(rtnData)
      } else {
        done([])
      }

    },
    select(item, index) {
      this.selectIndex = index
      if (this.from &&
        ['home_device_bind_hotel', 'home_order', 'home_device_or_rep', 'home_user_infor',
          'home_pur', 'home_device_detail', 'home_device_bind_place', 'home_placeAdd',
          'dataAnalsis', 'remittance', 'branch'].includes(this.from)
      ) {
        // 设备商品列表
        // 修改指定位置的数据
        /*  #ifndef H5 */
        var pages = getCurrentPages()
        var currPage = pages[pages.length - 1] //当前页面
        var prevPage = pages[pages.length - 2] //上一个页面
        //直接调用上一个页面的setData()方法，把数据存到上一个页面中去
        prevPage.setData({
          item: item,
        })
        /*#endif */
        /*  #ifdef H5 */
        this.vCurrPage.item = item
        /*#endif */
        uni.navigateBack({
          delta: 1,
        })

        return
      }
    },
    searchChange(val) {
      this.hotelName = val
      this.search(val)
    },
    search(text) {
      this.hotelName = text
      let list = AddValueInObject(this.vServerList.selectplace, text)
      this.$u.vuex(`vServerList.selectplace`, list)
      this.refresh()
    },
  },
  onLoad(options) {
    /*  #ifndef H5 */
    var pages = getCurrentPages()
    var currPage = pages[pages.length - 1] //当前页面
    var prevPage = pages[pages.length - 2] //上一个页面
    //直接调用上一个页面的setData()方法，把数据存到上一个页面中去
    prevPage.setData({
      item: {},
    })
    /*#endif */
    /*  #ifdef H5 */
    this.vCurrPage.item = {}
    /*#endif */

    this.from = options.from || ''
    this.bank_code = options.bank_code || '',
      this.type = options.type || '1'
    this.refresh()
  },

  mixins: [myPull({})],
}
</script>

<style lang="scss">
page {
  background-color: $pageBgColor;
}
</style>

<style lang="scss" scoped>
$margin-buttom-item: 10rpx;

.hotelItem {
  width: 100%;
  background: rgba(255, 255, 255, 1);
  display: flex;
  align-items: center;
  border-bottom: 1rpx solid rgba(229, 229, 229, 1);
  border-radius: $cardRadius;
  box-sizing: border-box;
  padding: 20rpx 30rpx;
  margin-bottom: 20rpx;

  .hotelItemDesc {
    width: 100%;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;

    .hotelName {
      font-size: $font-size-middle;
      color: rgba(40, 40, 40, 1);
      width: 250rpx;

      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }

    .address {
      width: 250rpx;

      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      color: rgba(104, 104, 104, 1);
    }

    .address,
    .linkman {
      color: rgba(104, 104, 104, 1);
    }

    view:last-child {
      font-size: 16rpx;
    }

    .hotelItemDescContent view {
      &:first-child {
        font-size: $font-size-middle;
        margin-bottom: $margin-buttom-item;
      }

      &:last-child {
        font-size: $font-size-base;
      }
    }

    .linkContent view {
      text-align: right;
      color: rgba(104, 104, 104, 1);

      &:first-child {
        font-size: $font-size-middle;
        margin-bottom: $margin-buttom-item;
      }

      &:last-child {
        font-size: $font-size-base;
      }
    }
  }
}
</style>
