<template>
  <view class="card" @click="editDivide">
    <view class="card_top">
      <view>定时出泡泡任务{{ i+1 }}</view>
      <view class="textMaxTwoLine">{{ vPointName }}名称: {{ info.hotelName||'' }}</view>
      <view>
        <view>
          <text>随机时间：</text>
          <text class="textBlack">{{ info.begin_time }}-{{ info.end_time }}</text>
        </view>
      </view>
      <view>
        <view>
          <text>开启周期：</text>
          <text class="textBlack" v-for="(item, i) in info.day_list" :key="i">周{{ week(item) }}</text>
        </view>
      </view>
      <view>
        <view>
          <text>状态：</text>
          <text :style="{color:info.status==1?'#0EADE2':'#333'}" class="textBlack">{{ info.status==1?'启用':'暂停' }}</text>
        </view>

      </view>
    </view>
    <view class="card_btn flexRowBetween">
      <BaseButton type="default" width="190" shape="circle" @onClick.stop="editDivide">
        编辑任务
      </BaseButton>
      <BaseButton type="default" width="190" shape="circle" @onClick.stop="setDivide">{{ info.status==1?'暂停':'启用' }}任务</BaseButton>
      <BaseButton type="default" width="190" shape="circle" @onClick.stop="deletDivide">删除任务</BaseButton>
    </view>
  </view>
</template>
  
<script>
import BaseButton from "@/components/base/BaseButton.vue";
export default {
  components: { BaseButton },
  name: "RandomTasksCard",
  props: {
    info: { type: Object, default: {} },
    i:{
      type:Number,default:0
    }
  },
  computed: {

   
  },
  methods: {
    week(i) {
      switch (i*1) {
        case 1:
          return '一'
        case 2:
          return '二'
        case 3:
          return '三'
          case 4:
          return '四'
          case 5:
          return '五'
          case 6:
          return '六'
          case 7:
          return '日'
        default:
          return '一'
      }
    },
    setDivide() {
      this.$emit("setDivide");
    },
    editDivide() {
      this.$emit("editDivide");
    },
    deletDivide() {
      this.$emit("deletDivide");
    },
  },
};
</script>
  
<style lang="scss" scoped>
.textMaxTwoLine {
  overflow-wrap: normal;
}
.card {
  &_top {
    >view {
      margin-bottom: 24rpx;
      display: flex;
      justify-content: space-between;

      &:first-child {
        color: $textBlack;
        font-size: $font-size-middle;
        font-weight: bold;
      }

      .textBlack {
        margin: 0 5rpx;
        color: $textBlack;
      }
    }
  }
}
</style>