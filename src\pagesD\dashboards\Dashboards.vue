<template>
  <view>
    <BaseNavbar title="数据看板" />
    <BaseSearch :placeholder="`请输入用户账号`" @search="search" listType="dashboardsTwo" />
    <!-- <BaseList listType="dashboardsTwo" @searchChange="searchChange" /> -->
    <DashboardsCard :show="!isEchartShow" :userLogin="searchVal" @isShowParmes="isShowParmes" title="订单收益数据"
      :titleLIst="['订单交易总金额', '本人收益金额']" taberType="order" prompt="时间内的订单收益数据" :params="params" @changeShow="changeShow"
      @NavgetTo="NavgetTo"></DashboardsCard>
    <DashboardsCard :userLogin="searchVal" :show="!isEchartShow" :num="5" @isShowParmes="isShowParmes"
      :title="`${vPointName}数据`" prompt="时间内销量最高的前5名" taberType="pointName" @busNum="busNum" @changeShow="changeShow"
      @NavgetTo="NavgetTo"></DashboardsCard>

    <DashboardsCard title="设备数据" :show="!isEchartShow" taberType="devesn" prompt="近期的设备数据" @isShowParmes="isShowParmes"
      :userLogin="searchVal" @busNum="busNum" @changeShow="changeShow" @NavgetTo="NavgetTo"></DashboardsCard>
    <BaseModal :show.sync="isShowInfoCircle" :isShowCancel="false" title="数据说明">
      <view slot="default" class="noun">
        <view class="noun-item" v-for="(item, i) in orderParmes" :key="i">
          <view class="noun-item-title">{{ item.title }}</view>
          <view>{{ item.text }}</view>
        </view>
      </view>
    </BaseModal>
  </view>
</template>
<script>
import BaseNavbar from '@/components/base/BaseNavbar.vue'
import BaseSearch from '../../components/base/BaseSearch.vue'
import myPull from '../../mixins/myPull'
import BaseModal from '@/components/base/BaseModal.vue'

import DashboardsCard from '@/pagesD/components/Card/DashboardsCard.vue'
// import BaseList from '@/components/base/BaseList.vue'
import { AddValueInObject } from '@/wxutil/list'
export default {
  components: {
    BaseNavbar,
    BaseSearch,
    DashboardsCard,
    BaseModal,
    //  BaseList
  },
  mixins: [myPull()],
  data() {
    return {
      searchVal: '',
      orderParmes: [],
      type: '',
      isShowInfoCircle: false,
      params: {},
      isEchartShow: false,
      hotelName: '', //搜索
    }
  },
  methods: {
    changeShow(show) {
      this.isEchartShow = show
    },
    searchChange(val) {
      this.hotelName = val
      this.search(val)
    },
    search(val) {
      this.params = {}
      this.searchVal = val
      // console.log('搜索',this.searchVal)
      // this.getindex(0);
      let list = AddValueInObject(this.vServerList.dashboardsTwo, val)
      this.$u.vuex(`vServerList.dashboardsTwo`, list)
    },
    isShowParmes(type, arr) {
      this.orderParmes = arr
      this.type = type
      this.isShowInfoCircle = true
    },
    busNum(pames) {
      this.params = Object.assign(pames, this.params)
      // console.log('params', this.params,pames)
    },
    NavgetTo() {
      // uni.navigateTo({
      //   url,
      //   success: function (res) {
      //     that.offBtn = false
      //   },
      //   fail: function (err) {
      //     that.offBtn = false
      //   },
      // })
    }
  },
  onLoad(opt) {
    // this.handleData(1, '', '', 0);
  },
  onPullDownRefresh() {
    uni.$emit('updata')
  },
  watch: {
    isShowInfoCircle: {
      handler: function (val) {
        this.isEchartShow = val
      },
    },
  },
}
</script>

<style lang="scss">
page {
  background-color: $pageBgColor;
}
</style>
<style scoped lang="scss">
.subsection {
  // border: 1px solid #000;
  background-color: #fff;
  padding: 20rpx;
  border-radius: 15rpx;
  margin: 20rpx;
}

.profit {
  background-color: #fff;

  &-title {
    padding: 20rpx 0;
    font-size: $font-size-middle;
    color: $textDarkGray;
  }
}

.profit_item_box {
  // border: 1px solid #000;
  display: flex;
  flex-wrap: wrap;

  .profit_item {
    border: 2rpx solid rgb(207, 205, 205);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 10rpx 20rpx;
    width: 165rpx;
  }

  .profit_text {
    font-size: 25rpx;
    color: #666;
    margin: 10rpx 0;
    width: 100%;
    text-align: center;
    // border: 1px solid #000;
  }
}

.title_right {
  width: 30rpx;
  height: 30rpx;
  margin-left: 5rpx;
  // border: 1px solid #000;
}

.noun {
  font-size: $font-size-base;
  color: $textDarkGray;

  &-item {
    margin-top: 30rpx;

    &-title {
      font-weight: 700;
      color: $textBlack;
      margin-bottom: 30rpx;
    }
  }

  &-info {
    >view {
      margin-bottom: 10rpx;
    }
  }
}

.subsetion_title {
  margin: 15rpx 0;
  display: flex;
  // border: 1px solid #000;
  align-items: center;
  // background-color: #333;
  // height: 120rpx;
}

.info-card {
  &-title {
    padding: 20rpx;
    padding-bottom: 0;
    font-size: $font-size-middle;
    color: $textBlack;
    font-weight: 700;
  }

  &-box {
    padding: 20rpx;
    background-color: #fff;
    background-color: #1a76b3;
  }
}
</style>
