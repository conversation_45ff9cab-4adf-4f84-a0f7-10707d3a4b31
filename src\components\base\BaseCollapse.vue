<template>
  <u-collapse class="collapse" :head-style="headStyle" :body-style="bodyStyle" :item-style="itemStyle">
    <u-collapse-item :title="item.head" v-for="(item, index) in list" :key="index">
      {{ item.body }}
    </u-collapse-item>
  </u-collapse>
</template>

<script>
export default {
  props: { list: { type: Array, default: function () {
                return [];
            } } },
  data() {
    return {
      //   itemList: [
      //     {
      //       head: "赏识在于角度的转换",
      //       body: "只要我们正确择取一个合适的参照物乃至稍降一格去看待他人，值得赏识的东西便会扑面而来",
      //       open: true,
      //       disabled: true,
      //     },
      //   ],
      headStyle: {
        color: "#333",
        fontSize: "32rpx",
      },
      bodyStyle: { color: "#333", fontSize: "28rpx" },
    };
  },
  options: { styleIsolation: "shared" }, //组件必须加,才能修改内部样式
};
</script>

<style lang="scss" scoped>
.collapse {
  ::v-deep .u-collapse-item {
    .u-line-1 {
      white-space: normal;
      line-height: 1.6;
    }
  }
}
</style>