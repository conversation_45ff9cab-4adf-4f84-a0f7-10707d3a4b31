<template>
  <view
    class="fixed-btn flexRowBetween"
    :style="{ paddingBottom: 20 + vIphoneXBottomHeight + 'rpx' }"
  >
    <BaseCheck
      :checked.sync="info.isAllCheck"
      :title="'全选(' + info.selectNum + ')'"
      @changeCheck="changeCheckAll"
    />
    <view class="total" v-if="isShowTotal">
      <view class="total-box">
        <view>合计：</view>
        <view class="total-box-mark">￥</view>
        <view class="total-box-price">
          {{ info.totalPrice.toFixed(2) }}
        </view>
      </view>
      <view class="total-num"> 共计{{ info.totalNum }}件 </view>
    </view>

    <BaseButton width="200" type="primary" @onClick="confirmOrder">
      {{ btnTitle }}
    </BaseButton>
  </view>
</template>

<script>
import BaseButton from "../base/BaseButton.vue";
import BaseCheck from "../base/BaseCheck.vue";
export default {
  name: "FixedSubButton",
  components: { BaseButton, BaseCheck },
  props: {
    info: {
      type: Object,
      default: {
        isAllCheck: false, //是否全选
        selectNum: 0, //选中数量
        totalPrice: 0.0, //总价
        totalNum: 0, //总数量
      },
    },
    btnTitle: { type: String, default: "提交订单" },
    isShowTotal: { type: Boolean, default: true },
  },
  methods: {
    changeCheckAll() {
      this.$emit("changeCheckAll", !this.info.isAllCheck);
    },
    confirmOrder() {
      if (this.info.selectNum == 0) return;

      this.$emit("confirm");
    },
  },
};
</script>


<style lang="scss" scoped>
.fixed-btn {
  position: fixed;
  left: 0;
  bottom: 0;
  width: 100%;
  padding: 20rpx 30rpx;
  box-sizing: border-box;
  background-color: $uni-bg-color;
  z-index: 999999;
  .total {
    &-box {
      display: flex;
      align-items: flex-end;
      color: $textBlack;
      font-size: $font-size-base;
      &-mark {
        color: red;
        font-size: $font-size-xsmall;
      }
      &-price {
        color: red;
        font-size: $font-size-middle;
      }
    }
    &-num {
      color: $textDarkGray;
      font-size: $font-size-small;
    }
  }
}
</style>