// $u.mixin.js

import { mapState } from 'vuex'
import store from '@/store'

// 尝试将用户在根目录中的store/index.js的vuex的state变量，全部加载到全局变量中
let $uStoreKey = []
try {
  $uStoreKey = store.state ? Object.keys(store.state) : []
} catch (e) {}

module.exports = {
  created() {
    // 将vuex方法挂在到$u中
    // 使用方法为：如果要修改vuex的state中的user.name变量为"史诗" => this.$u.vuex('user.name', '史诗')
    // 如果要修改vuex的state的version变量为1.0.1 => this.$u.vuex('version', '1.0.1')
    this.$u.vuex = (name, value) => {
      this.$store.commit('$uStore', {
        name,
        value,
      })
    }
  },
  computed: {
    // 将vuex的state中的所有变量，解构到全局混入的mixin中
    ...mapState($uStoreKey),
  },
  methods: {
    isShowSuccess(
      title = '操作成功',
      delta = 0,
      callback = undefined,
      backRefresh = false,
    ) {
      //title 提示信息
      //delta 返回 n级页面
      //callback 定时结束回调
      //backRefresh 返回上一级 是否返回刷新数据
      uni.showToast({
        title,
        icon: 'success',
        mask: true,
      })
      if (delta || callback) {
        setTimeout(() => {
          if (delta) {
            uni.navigateBack({ delta })
          }
          if (callback) callback()
          if (backRefresh) {
            /*  #ifndef H5*/
            let pages = getCurrentPages()
            // let currPage = pages[pages.length - 1] //当前页面
            let prevPage = pages[pages.length - 2] //上一个页面
            //直接调用上一个页面的setData()方法，把数据存到上一个页面中去
            prevPage.setData({
              isDoRefresh: true,
            })
            /* #endif */
            /*  #ifdef H5*/
            this.vCurrPage.isDoRefresh = true
            /* #endif */
          }
        }, 1800)
      }
    },
    isShowErr(
      title = '操作失败~',
      delta = 0,
      callback = undefined,
      backRefresh = false,
    ) {
      uni.showToast({
        title,
        icon: 'none',
        mask: true,
      })
      if (delta || callback) {
        setTimeout(() => {
          if (delta) {
            uni.navigateBack({ delta })
          }
          if (callback) callback()
          if (backRefresh) {
            
              /*  #ifndef H5*/
              let pages = getCurrentPages()
              // let currPage = pages[pages.length - 1] //当前页面
              let prevPage = pages[pages.length - 2] //上一个页面
              //直接调用上一个页面的setData()方法，把数据存到上一个页面中去
              prevPage.setData({
                isDoRefresh: true,
              })
              /* #endif */
              /*  #ifdef H5*/
              this.vCurrPage.isDoRefresh = true
              /* #endif */
          }
        }, 1800)
      }
    },
    isShowTwo(title = '操作成功', delta = 0) {
      //title 提示信息
      //delta 返回 n级页面
      //callback 定时结束回调
      //backRefresh 返回上一级 是否返回刷新数据
      if (title) {
        uni.showToast({
          title,
          icon: 'none',
          mask: true,
        })
      }
      if (delta) {
        setTimeout(() => {
             /*  #ifndef H5*/
             let pages = getCurrentPages()
            //  let currPage = pages[pages.length - 1] //当前页面
             let prevPage = pages[pages.length - 2] //上一个页面
             //直接调用上一个页面的setData()方法，把数据存到上一个页面中去
             prevPage.setData({
               isDoRefresh: true,
             })
             /* #endif */
             /*  #ifdef H5*/
             this.vCurrPage.isDoRefresh = true
             /* #endif */
        }, 1800)
      }
    },
  },
}
