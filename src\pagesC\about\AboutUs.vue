<template>
  <view class="content">
    <BaseNavbar :title="title" />
    <view class="infoWrap flexColumnHorzCenter">
      <image class="logo" :src="vSiteConfig.site_info.site_logo"></image>
      <view class="xhkj">{{ vSiteConfig.site_info.site_name }}</view>
      <view class="app">{{ vSiteConfig.site_info.site_version }}</view>
    </view>
    <view class="section">
      {{ vSiteConfig.site_info.site_seo_description }}
    </view>
    <view
      class="protocol"
      :style="{ bottom: vIphoneXBottomHeight + 20 + 'rpx' }"
      @click="onClickProtocol"
      >《用户服务协议》</view
    >
  </view>
</template>

<script>
import BaseNavbar from "@/components/base/BaseNavbar.vue";
export default {
  components: { BaseNavbar },
  data() {
    return {
      title: "关于我们",
    };
  },
  methods: {
    onClickProtocol() {
      uni.navigateTo({
        url:
          "/pagesC/webView/WebView?url=" +
          encodeURIComponent(this.vSiteConfig.site_info.user_agreement_url),
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.content {
  position: relative;

  .infoWrap {
    padding-top: 126rpx;

    .logo {
      width: 210rpx;
      height: 210rpx;
    }

    .xhkj {
      margin-top: 32rpx;
      color: $textBlack;
      font-weight: bold;
      font-size: $font-size-xlarge;
    }

    .app {
      margin-top: 20rpx;
      font-size: $font-size-xsmall;
      color: $textGray;
    }
  }
  .section {
    margin-top: 78rpx;
    padding: 0 35rpx;
    color: $textBlack;
    font-size: $font-size-base;
    line-height: 1.6;
  }
  .protocol {
    position: fixed;
    left: 50%;
    transform: translateX(-50%);
    font-size: $font-size-small;
    color: $themeColor;
  }
}
</style>