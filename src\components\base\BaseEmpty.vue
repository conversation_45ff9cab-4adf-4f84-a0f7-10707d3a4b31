<template>
  <u-empty :src="src" :text="text" :show="show" color="#333333" font-size="28" icon-size="270" :margin-top="top">
  </u-empty>
</template>

<script>
export default {
  name: "BaseEmpty",
  props: {
    text: { type: String, default: "暂无数据" },
    show: { type: Boolean, default: true },
    top: { type: [Number,String], default: 300 }
  },
  data() {
    return {
      src: require("@/static/img/notYet.png"),
    };
  },
};
</script>

<style lang="scss" scoped>
</style>