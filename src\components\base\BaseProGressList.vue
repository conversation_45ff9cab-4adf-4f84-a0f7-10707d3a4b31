<template>
    <BasePopup :show="isShowProgress" @close="close" :maskClose="false" mode="center" height="450" :closeable="cancel"
        width="690" radius="20">

        <view class="cent_progres">
            <view class="center_pro_title">

                {{ titles }}进度
            </view>
            <view class="center_pro_top">

                <view><text>{{ isOpen ? '出货片数：' : '总设备数：' }}</text>{{ total }}</view>
                <view><text>已完成：</text>{{ finishNum }}</view>
                <view v-if="!ok">正在操作：{{ device_sn }}</view>
            </view>
            <view class="center_pro">
                <u-line-progress active-color="#2979ff" :percent="ProgressNum"></u-line-progress>
            </view>
            <view class="center_pro_bottom">

                <text>总数量: {{ total }} </text>
                <text>已完成: {{ finishNum }} </text>
                <text>未完成: {{ total - finishNum }} </text>
                <text>失败: {{ failNum }}</text>
            </view>
            <view class="center_pro_btn">
                <BaseButton type="primary" @onClick="timeOtClick">{{ timeOut || ok ? '取消' : '暂停' }}
                </BaseButton>
            </view>
        </view>

    </BasePopup>
</template>

<script>
import BasePopup from "./BasePopup.vue";
import BaseButton from "./BaseButton.vue";
import ble from '@/wxutil/ble.js'
export default {
    name: "BaseProGress",
    components: {
        BaseButton,
        BasePopup,
    },
    props: {
        total: {
            type: [Number, String], //按钮宽度
            default: 0,
        },
        titles: {
            type: String,
            default: '批量启动'
        },
        stops: {
            type: Boolean,
            default: false,
        }

    },
    // 在子组件中声明 message 属性，用于接收父组件传递的参数
    mounted() {
        this.isShowProgress = false
        this.timeOut=false
        // 监听来自父组件的名为 "customEvent" 的事件
        uni.$off('customEvent');
        uni.$off('customEventfalse');
        uni.$off('customTimOutfalse');
        uni.$on('customEvent', this.handleCustomEvent);
        uni.$on('customEventfalse', this.customEventfalse);
        uni.$on('customTimOutfalse', this.customTimOutfalse);
    },
    beforeDestroy() {
        // 在组件销毁前，移除事件监听
        this.isShowProgress = false
        // this.timeOut=false
    },
    data() {
        return {
            finishNum: 0,//已经完成的设备数
            device_sn: '',//正在操作的设备编号
            cancel: true,//控制取消按钮显示
            failNum: 0,//失败的数
            ProgressNum: 0,//进度
            timeOut: false,//暂停
            ok: false,//完成
            isShowProgress: false,
            isBind: false,
            errorList: [],
            isOpen: false,
            isDesable: true,//没有完成不可以取消
            type: '',

        };
    },
    methods: {
        customTimOutfalse(val){
            this.timeOut=val
            console.log('暂停事件',val)
            if(this.type == '0x82'){
                this.isShowTwo("操作完成");
            }
        },
        customEventfalse() {
            this.isShowProgress = false
            this.timeOut=true
        },
        close() {
            this.$emit('errorChange', this.errorList);
            this.isShowProgress = false
            this.clear()
            if (this.isBind) {

                this.isShowErr("操作完成", 1, '', true);
            } else {
                this.isShowTwo("", 1);
            }

        },
        timeOtClick() {
            if (this.timeOut || this.ok) {
                this.close()
            } else {
                if (this.type == '0x83') {
                    ble.stop()
                }
                this.timeOut = true
                this.ok = true
                // this.cancel = true
            }


        },
        /* 批量管理 */
        async sendRequests(dataList, datas, interval, requestCallback, parame, requestCallbackTwo) {
            this.clear()
            this.timeOut=false
            for (const url of dataList) {
                console.log('触发请求')
                if(this.timeOut){
                    return
                }
                if (!this.timeOut) {
                    this.device_sn = url.device_sn

                    let data = {
                        ...datas,
                        device_sn: this.device_sn,

                    }
                    let parames = {
                        ...parame,
                        device_sn: this.device_sn,
                    }
                    if (url.mid) {
                        data['mid'] = url.mid
                        parames['mid'] = url.mid
                    } else if (url.id) {
                        parames['mid'] = url.id
                    }
                    if (url.room_num) {
                        data['room_num'] = url.room_num
                        parames['room_num'] = url.room_num
                    }

                    try {
                        // 在这里处理请求的响
                        // const response = await this.$u.api.startUM(data)
                        await requestCallback(data);
                        if (parame) {
                            await requestCallbackTwo(parames);
                        }
                        this.finishNum++

                    } catch (error) {
                        // 在这里处理请求的错误
                        // console.error(`请求 ${url} 失败，错误信息:`, error);
                        this.failNum++
                        this.errorList.push(url)
                    }
                    let time = Math.ceil(((this.finishNum + this.failNum) / this.total) * 100)
                    if (time >= 100) {
                        this.ProgressNum = 100
                        // this.cancel = true
                        this.ok = true
                    } else {
                        this.ProgressNum = time * 1
                    }
                    // 在每个请求之后等待一段时间
                    await this.sleep(interval);

                }
            }

        },
        //持续出货处理
        async open(index, interval, type) {
            this.type = type
            //
            this.timeOut=false
            this.clear()
            //循环index

            let arr = []
            for (let i = 0; i < index; i++) {
                arr.push(i)
            }

            console.log('this.timeOut.type', type)
            if (type == '0x83') {
                console.log('发送几次几次', index)
                // this.isDesable = false
                ble.openVendingLock(this.openCallback, index)
            } else {
                for (const i of arr) {
                    //每次成功  
                    //失败this.failNum++
                    console.log('进入循环', this.timeOut)
                    if (this.timeOut) {
                        return
                    }
                    if (!this.timeOut) {
                        ble.openVendingLock(this.openCallback)
                        //每次休眠
                        await this.sleep(interval)
                    }
                }

            }


        },
        //出货回调
        openCallback(isOpen, text) {
            // 开锁回调
            // console.log('出后回调信息', isOpen, text)
            if (isOpen) {
                // 开门成功
                uni.showToast({
                    title: text||'出货成功',
                    icon: 'success',
                })
                if (this.finishNum < this.total&&!text) {
                    this.finishNum++
                }
                //判断是否需要继续打开其他锁
            } else {
                // 开门失败
                uni.showToast({
                    title: text,
                    icon: 'error',
                })
                this.failNum++
            }
            let time = Math.ceil(((this.finishNum + this.failNum) / this.total) * 100)
            if (time >= 100) {
                this.ProgressNum = 100
                // this.cancel = true
                this.ok = true
                // this.isDesable = true
            } else {
                this.ProgressNum = time * 1
            }

        },
        // 辅助函数：等待指定的时间
        sleep(ms) {
            return new Promise(resolve => setTimeout(resolve, ms));
        },
        clear() {
            this.finishNum = 0
            this.ProgressNum = 0
            this.failNum = 0
            // this.cancel = false
            // this.timeOut = false
            this.ok = false
            this.errorList = []
        },
        handleCustomEvent(ataList, data, insave, requestCallback, isBind, parames, requestCallbackTwo) {
            this.isShowProgress = true
            this.timeOut=false
            // console.log('接受事件', ataList)
            if (ataList) {
                this.isBind = isBind
                this.sendRequests(ataList, data, insave, requestCallback, parames, requestCallbackTwo);
            } else {
                console.log('出货事件', data, insave, requestCallback)
                this.isOpen = true
                this.open(data, insave, requestCallback)
            }
        },
    },
 
    options: { styleIsolation: "shared" }, //组件必须加,才能修改内部样式
};
</script>

<style lang="scss" scoped>
.cent_progres {
    padding: 0 30rpx;
    margin-top: 28rpx;

    .center_pro_title {
        text-align: center;
    }

    .center_pro_top {
        // display: flex;
        width: 100%;

        // flex-wrap: wrap;
        view {
            margin: 5rpx 0;

            // flex: 1;
            // border: 1px solid #000;
            // width:100%;
            text {
                width: 100rpx;
            }
        }
    }

    .center_pro_bottom {
        display: flex;

        >text {
            // border: 1px solid #000;
            margin-right: 25rpx;
        }
    }

    .center_pro {
        // height: 18rpx;
        // border: 1px solid #000;
        // display: flex;
        // justify-content: center;
        margin-bottom: 10rpx;
    }

    .center_pro_btn {
        // border: 1px solid #000;
        margin: 20rpx 0;
    }
}


.defaultStyle {
    ::v-deep .u-btn {
        height: 88rpx;
        font-size: $font-size-xlarge;

        &::after {
            border: none;
        }
    }
}

.primary {
    ::v-deep .u-btn {
        color: $textWhite;
        background: linear-gradient(268deg, #206bc5 0%, #0eade2 99%);
    }
}

.eixt {
    ::v-deep .u-btn {
        color: $textWhite;
        background: linear-gradient(268deg, #ff2919 0%, #ff5223 99%);
    }
}

.default {
    ::v-deep .u-btn {
        color: $textBlack;
        background: $uni-bg-color;
        border: 2rpx solid #c8c8c8;
    }
}

.theme {
    ::v-deep .u-btn {
        color: $textWhite;
        background: $themeComColor;
        font-size: $font-size-middle;
        height: 78rpx;
    }
}
</style>