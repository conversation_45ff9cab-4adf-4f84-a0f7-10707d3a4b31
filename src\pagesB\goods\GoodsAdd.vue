<template>
  <view>
    <BaseNavbar :title="title" />
    <view class="content">
      <view>
        <view class="title">添加图片</view>
        <BaseUpload
          width="150"
          :auto="true"
          maxCount="1"
          @onUpload="onUpload"
        />
      </view>
      <view>
        <view class="title">商品名称</view>
        <BaseInput placeholder="请输入商品名称" v-model="goodsName" />
      </view>
      <view>
        <view class="title">进货价</view>
        <BaseInput
          placeholder="请输入进货价格"
          rightText="元"
          v-model="cost_price"
        />
      </view>
      <view>
        <view class="title">市场价</view>
        <BaseInput
          placeholder="请输入市场价价格"
          rightText="元"
          v-model="market_price"
        />
      </view>
      <view class="btn">
        <BaseButton type="primary" @onClick="confirm">确 认</BaseButton>
      </view>
    </view>
  </view>
</template>

<script>
import BaseButton from "@/components/base/BaseButton.vue";
import BaseInput from "@/components/base/BaseInput.vue";
import BaseNavbar from "@/components/base/BaseNavbar.vue";
import BaseUpload from "@/components/base/BaseUpload.vue";
export default {
  components: { BaseNavbar, BaseInput, BaseUpload, BaseButton },
  data() {
    return {
      title: "新增商品",
      uploadList: [],
      goodsName: "", //商品名称
      market_price: "", //市场价
      storage_type: "", //商品类型
      cost_price: "", //进货价
    };
  },
  methods: {
    onUpload(item) {
      this.uploadList = item;
    },
    confirm() {
      if (this.goodsName == "")
        return uni.showToast({
          title: "请填写商品名称",
          icon: "none",
          mask: true,
        });
      if (this.cost_price == "")
        return uni.showToast({
          title: "请填写进货价",
          icon: "none",
          mask: true,
        });
      if (this.market_price == "")
        return uni.showToast({
          title: "请填写市场价",
          icon: "none",
          mask: true,
        });
      let original_img = this.uploadList.length > 0 ? this.uploadList[0] : "";

      let data = {
        goods_name: this.goodsName,
        shop_price: this.market_price,
        market_price: this.market_price,
        cost_price: this.cost_price,
        goods_content: "",
        goods_remark: "",
        storage_type: this.storage_type,
        original_img,
      };
      this.$u.api.addProducts(data).then((res) => {
        this.isShowSuccess("添加成功", 1, () => {}, true);
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.content {
  padding: 30rpx;
  > view {
    margin-bottom: 50rpx;
  }
  .title {
    color: $textBlack;
    font-size: $font-size-middle;
    font-weight: bold;
    margin-bottom: 20rpx;
  }
}
</style>