<template>
    <view class="bindHotelCard">
        <view class="locationRoom">
            <view class="location-left">坑位楼层</view>
            <view class="rideo-input">
                <input type="text" v-model="floor" />
            </view>
            <view class="right-txt">
                楼
            </view>

        </view>
        <view class="locationRoom">
            <view class="location-left">厕所类型</view>
            <view class="ridio">
                <BaseRadio :radioIndex.sync="radioIndex" width="33" :list="reasonList" />
            </view>
        </view>
        <view class="locationRoom">
            <view class="location-left">坑位编号</view>
            <view class="rideo-input">
                <input type="text" v-model="number" />
            </view>
            <view class="right-txt">
                号
            </view>

        </view>
        <view class="locationRoom">
            <view class="location-left">坑位类型</view>
            <view class="ridio">
                <BaseRadio :radioIndex.sync="typeIndex" width="33" :list="typeList" />
            </view>
        </view>
        <view class="locationRoom">
            <view class="location-left">坑位地址</view>
            <view class="rideo-chunk"> {{ floor }}</view>
            楼--
            <view class="rideo-chunk"> {{ reasonList[radioIndex].title }}</view>
            --
            <view class="rideo-chunk"> {{ number }}</view>
            号--
            <view class="rideo-chunk"> {{ typeList[typeIndex].title }}</view>

        </view>
        <view class="locationRoom">
            <input type="text" placeholder="请输入坑位其他信息" v-model="room_num" />
        </view>
    </view>

</template>

<script>

import BaseRadio from "@/components/base/BaseRadio.vue";


export default {
    components: { BaseRadio },
    name: "LoctionType",
    props: {
        str: {
            type: String,
            default: ''

        },
        floors: {
            type: [String, Number],
            default: 1,


        },
        numbers: {
            type: [String, Number],
            default: 1,

        }

    },


    data() {
        return {
            reasonList: [{
                title: "女厕",
                name: "1",
                disabled: false,
                selectIndex: 0,

            },
            {

                title: "男厕",
                name: "2",
                disabled: false,
                selectIndex: 1,

            },
            {

                title: "母婴",
                name: "3",
                disabled: false,
                selectIndex: 2,

            },
            {

                title: "无障碍",
                name: "4",
                disabled: false,
                selectIndex: 2,

            },
            {

                title: "共用",
                name: "5",
                disabled: false,
                selectIndex: 2,

            },


            ],
            radioIndex: 0,
            typeList: [{
                title: "蹲坑",
                name: "1",
                disabled: false,
                selectIndex: 0,

            },
            {

                title: "马桶",
                name: "2",
                disabled: false,
                selectIndex: 1,

            },

            ],
            typeIndex: 0,
            floor: 1,
            number: 1,
            room_num: ''
        }
    },
    //方法
    computed: {
        location_str() {
            if (this.room_num) {

                return this.floor + '楼-' + this.reasonList[this.radioIndex].title + '-' + this.number + '号-' + this.typeList[this.typeIndex].title + '-' + this.room_num.replace(/-/g, '')
            } else {
                return this.floor + '楼-' + this.reasonList[this.radioIndex].title + '-' + this.number + '号-' + this.typeList[this.typeIndex].title
            }

        },

    },
    watch: {
        location_str: {
            handler(newVal) {
                this.$emit('locationStr', newVal, this.floor, this.number);
            },
            immediate: true
        },
        floors: {
            handler(newVal) {
                this.floor = newVal

            },
            immediate: true
        },
        numbers: {
            handler(newVal) {
                this.number = newVal

            },
            immediate: true
        },
        str: {
            handler(newVal) {
                console.log('传递的值', newVal)
                this.updata(newVal)

            },
            immediate: true
        }

    },
    //组件挂载
    mounted() {
        // const str=this.location_str
        // console.log('str',str)
        // setTimeout(()=>{
        //     this.$emit('locationStr', str, this.floor, this.number);
        // })
        console.log('str', this.str)

      


    },
    methods: {
        updata(str) {

            let arr = str.split('-')
            if (arr.length > 1) {
                this.floor = arr[0].split('楼')[0] || 1
                this.reasonList.forEach((item, index) => {
                    if (item.title == arr[1]) {
                        this.radioIndex = index || 0
                    }
                })
                this.number = arr[2].split('号')[0] || 1
                this.typeList.forEach((item, index) => {
                    if (item.title == arr[3]) {
                        this.typeIndex = index || 0
                    }
                })
                this.room_num = arr[4] || ''
            } else {

                if (this.floors && this.numbers) {
                    this.floor = this.floors
                    this.number = this.numbers
                }
            }
        }
    }
};
</script>

<style lang="scss" scoped>
.ridio {
    width: 480rpx;
}

.location-left {
    margin-right: 25rpx;
}

.hotelName {
    // height: 100rpx;
    padding: 25rpx 0;
    display: flex;
    flex-direction: row;
    align-content: center;
    align-items: center;
    margin-left: 35rpx;
    border-bottom: 1rpx solid #e5e5e5;
}

.rideo-chunk {
    width: 100rpx;
    text-align: center;
    border: 1rpx solid #cbcbcb;
    padding: 10rpx;
    margin-right: 10rpx;
    height: 60rpx;

}

.rideo-input {
    height: 60rpx;
    width: 100rpx;
    border: 1rpx solid #cbcbcb;
    padding: 10rpx;
    text-align: center;
    margin-right: 20rpx;
    display: flex;
    align-items: center;

}

.bindHotelCard {
    // margin-top: 20rpx;
    // height: 300rpx;
    width: 100%;
    background: white;
    font-size: 26rpx;
    color: $textBlack;
}

.locationRoom {
    height: 100rpx;
    // height: 150rpx;
    // padding: 25rpx 0;
    display: flex;
    flex-direction: row;
    align-content: center;
    align-items: center;
    margin-left: 35rpx;
    border-bottom: 1rpx solid #e5e5e5;
    // border-bottom: 0;
}
</style>