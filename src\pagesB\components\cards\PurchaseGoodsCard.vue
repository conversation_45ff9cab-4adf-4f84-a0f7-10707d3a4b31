<template>
  <view class="goods card" @click="onClick">
    <BaseCheck v-if="isShow" :checked.sync="info.isCheck" />

    <view class="goods-box">
      <view class="goods-img"
        ><image class="img" :src="info.original_img || vDefaultIcon"
      /></view>
      <view class="goods-content">
        <view class="goods-content-name">{{ info.goods_name }}</view>
        <view class="goods-content-num">x{{ info.isAmount }}</view>
        <view class="goods-content-box">
          <view class="goods-content-box-price">
            <text>￥</text>
            <text class="money">{{ info.shop_price }}</text>
          </view>
          <view
            v-if="isShow"
            class="goods-content-box-number"
            @click.stop="() => {}"
          >
            <u-number-box
              @change="changeNumberBox"
              :long-press="false"
              :index="index"
            ></u-number-box>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import BaseCheck from "@/components/base/BaseCheck.vue";
export default {
  components: { BaseCheck },
  name: "PurchaseGoodsCard",
  props: {
    info: { type: Object, default: {} },
    index: { type: [Number,String], default: 0 },
    isShow: { type: Boolean, default: true },
  },
  data() {
    return {
      changeInfo: {},
      debounceTimer: null,
    };
  },
  methods: {
    changeNumberBox(e) {
      this.changeInfo = e;
      this.debounce(this.valChange); //防抖
    },
    debounce(fn) {
      clearTimeout(this.debounceTimer);
      this.debounceTimer = setTimeout(() => {
        fn();
      }, 500);
    },
    valChange() {
      this.$emit("checkValue", this.changeInfo);
    },
    //改变选中状态
    onClick() {
      this.$emit("onCheck");
    },
  },
};
</script>

<style lang="scss" scoped>
.goods {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
  padding: 20rpx;
  .goods-box {
    flex: 1;
    display: flex;
    align-items: center;
  }
  &-img {
    width: 160rpx;
    height: 160rpx;
    flex-shrink: 0;
    .img {
      width: 100%;
      height: 100%;
    }
  }
  &-content {
    flex: 1;
    margin-left: 20rpx;
    height: 160rpx;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    &-name {
      color: $textBlack;
      font-weight: 700;
      font-size: $font-size-middle;
    }
    &-specs {
      color: $textDarkGray;
      font-size: $font-size-base;
      margin: 16rpx 0;
    }
    &-box {
      display: flex;
      justify-content: space-between;
      font-weight: 700;
      &-price {
        color: $textWarn;
        font-size: $font-size-xsmall;
        .money {
          font-size: $font-size-middle;
        }
      }
    }
  }
}
</style>