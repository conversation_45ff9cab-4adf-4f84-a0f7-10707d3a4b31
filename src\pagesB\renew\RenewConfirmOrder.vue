<template>
  <view>
    <BaseNavbar title="确认订单" />
    <view class="order">
      <view class="title">订单信息</view>
      <view class="info">
        <view class="info-item">
          <view class="info-item-left">设备编号</view>
          <view class="info-item-right">
            <view v-for="(item, index) in deviceList" :key="index">
              {{ item }}
            </view>
          </view>
        </view>
        <view class="info-item">
          <view class="info-item-left">续费时长</view>
          <view class="info-item-right">1年</view>
        </view>
        <view class="info-item">
          <view class="info-item-left">年费总金额</view>
          <view class="info-item-right">{{ orderAmount }}</view>
        </view>
        <view class="info-item">
          <view class="info-item-left">续费设备数量</view>
          <view class="info-item-right">{{ totalNum }}</view>
        </view>
      </view>
    </view>
    <PayWay @confirmPay="confirmPay" :orderMoney="orderAmount" />
  </view>
</template>

<script>
import BaseNavbar from "@/components/base/BaseNavbar.vue";
import PayWay from "../../components/common/PayWay.vue";
export default {
  components: { BaseNavbar, PayWay },
  data() {
    return {
      order_sn: "",
      orderAmount: "",
      deviceList: [],
      totalNum: 0,
    };
  },
  methods: {
    confirmPay(type, pay_type) {
      let data = {
        pay_type,
        order_sn: this.order_sn, //订单编号
        openid: this.vPayOpenid,
      };
      this.$u.api.subRenewPrepay(data).then((res) => {
        if (type === 0) {
          let newUserInfo = this.vUserInfo;

          newUserInfo.user.cash =
            (parseFloat(newUserInfo.user.cash) * 1000 -
              parseFloat(this.orderAmount) * 1000) /
            1000;

          this.$u.vuex("vUserInfo", newUserInfo);
          this.isShowSuccess("支付成功~");
        } else if (type === 1) {
          this.requestWxPay(res.data);
        }
      });
    },
    requestWxPay(payInfo) {
      let that = this;
      //请求微信支付
      uni.requestPayment({
        /* #ifdef MP-WEIXIN */
        provider: "wxpay",
        appId: payInfo.appId,
        timeStamp: payInfo.timeStamp,
        nonceStr: payInfo.nonceStr,
        package: payInfo.package,
        signType: payInfo.signType,
        paySign: payInfo.paySign,
        /* #endif */
        success(res) {
          console.log("微信支付订单成功", res);
          that.isShowSuccess("支付成功~");
        },
        fail(res) {
          console.log("支付失败", res);
          that.isShowErr("支付失败~");
        },
      });
    },
  },
  onLoad(opt) {
    this.order_sn = opt?.order_sn || "";
    this.orderAmount = opt?.orderAmount || "";
    let getStorageData = uni.getStorageSync(this.order_sn);
    this.deviceList = getStorageData.deviceList;
    this.totalNum = getStorageData.totalNum;
  },
};
</script>

<style lang="scss" scoped>
.order {
  padding: 30rpx;

  .title {
    font-size: $font-size-xlarge;
    color: $textBlack;
    font-weight: 700;
    margin-bottom: 40rpx;
  }

  .info {
    &-item {
      display: flex;
      justify-content: space-between;
      font-size: $font-size-base;
      margin-bottom: 30rpx;

      &:last-child {
        margin-bottom: 0;
      }

      &-left {
        color: $textDarkGray;
      }

      &-right {
        color: $textBlack;
      }
    }
  }
}
</style>