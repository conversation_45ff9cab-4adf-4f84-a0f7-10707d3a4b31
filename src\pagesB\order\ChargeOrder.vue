<template>
  <view>
    <BaseNavbar title="充电订单" />

    <BaseSearch @click.stop="onClickIcon(0)" typeImg="screen" placeholder="请输入订单编号/设备编号" :disabled="true"
      @onClickIcon="onClickIcon" @search="search" listType="ChargeOrder" />
    <!-- <BaseList listType="ChargeOrder" @searchChange="searchChange" /> -->
    <BaseDropdown :options-list="optionsList" :num="orderTotal" @change="changeDropdown"></BaseDropdown>

    <ComList :loadingType="loadingType">
      <ChargeOrderCard v-for="item in listData" :key="item.id" :info="item" />
    </ComList>
    <BasePopup :show.sync="isShowPopup" mode="top" :customStyle="customStyle">
      <OrderScreener @confirm="confirmQuery" />
    </BasePopup>
    <BaseBackTop @onPageScroll="onPageScroll" :scrollTop="scrollTop"></BaseBackTop>
  </view>
</template>
<script>
import BaseNavbar from '@/components/base/BaseNavbar.vue'
import ComList from '@/components/list/ComList.vue'
import myPull from '../../mixins/myPull'
import OrderScreener from './components/OrderScreener.vue'
import BaseSearch from '../../components/base/BaseSearch.vue'
import BasePopup from '../../components/base/BasePopup.vue'
import BaseDropdown from '../../components/base/BaseDropdown.vue'
import ChargeOrderCard from '../components/cards/ChargeOrderCard.vue'
// import BaseList from '@/components/base/BaseList.vue'
import { AddValueInObject } from '@/wxutil/list'
import BaseBackTop from '@/components/base/BaseBackTop.vue'
export default {
  components: {
    BaseNavbar,
    ComList,
    ChargeOrderCard,
    OrderScreener,
    BaseSearch,
    BasePopup,
    BaseDropdown,
    // BaseList,
    BaseBackTop,
  },
  mixins: [myPull()],
  data() {
    return {
      scrollTop: 0,
      isShowPopup: false,
      screenInfo: {}, //筛选数据
      orderTotal: 0,
      optionsList: [
        {
          title: '全部',
          options: [
            { label: '全部', value: 0, status: 0 },
            {
              label: '待开启',
              value: 1,
              status: 1,
            },
            {
              label: '充电中',
              value: 2,
              status: 2,
            },
            {
              label: '充电结束',
              value: 3,
              status: 3,
            },
          ],
          value: 0,
        },
      ],
      hotelName: '', //搜索
      customStyle: {
        top: 110 + this.vStatusBarHeight + this.vNavBarHeight + 'rpx',
      }
    }
  },
  methods: {
    onPageScroll(e) {
      this.scrollTop = e.scrollTop
    },
    searchChange(val) {
      this.hotelName = val
      this.search(val)
    },
    search(val) {
      let list = AddValueInObject(this.vServerList.ChargeOrder, val)
      this.$u.vuex(`vServerList.ChargeOrder`, list)

      this.refresh()
    },
    getList(page, done) {
      let data = {
        page,
        limit: 10,
        hotel_name: this.screenInfo.dianwei,
        device_sn: this.screenInfo.device_sn,
        charge_sn: this.screenInfo.order_sn,
        charge_status: this.order_status,
        start_time: this.screenInfo.start_time,
        end_time: this.screenInfo.end_time,
      }
      this.$u.api.getChargeOrderList(data).then((res) => {
        if (page == 1) {
          this.orderTotal = res.total
        }
        done(res.list)
      })
    },
    onClickIcon(e) {
      if (e == 0) {
        this.isShowPopup = !this.isShowPopup
      }
    },
    confirmQuery(el) {
      this.screenInfo = el
      this.isShowPopup = false
      this.refresh()
    },
    changeDropdown(item) {
      this.optionsList = item
      this.order_status = item[0].options[item[0].value].status
      this.refresh()
    },
  },

  onLoad() {
    this.customStyle = {
      top: 110 + this.vStatusBarHeight + this.vNavBarHeight + 'rpx',
    }
    this.refresh()
  },
}
</script>
<style lang="scss">
page {
  background-color: $pageBgColor;
}
</style>
<style scoped lang="scss"></style>
