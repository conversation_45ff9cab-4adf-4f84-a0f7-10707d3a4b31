<template>
  <view>
    <BaseNavbar title="确认订单" />
    <view class="content">
      <view class="content-box">
        <view class="title">
          <view>用户昵称</view>
          <view class="necessary">*</view>
        </view>
        <BaseInput v-model="vUserInfo.userName" :disabled="true" />
      </view>
      <view class="content-box">
        <view class="title">
          <view>{{ vPointName }}</view>
          <view class="necessary">*</view>
        </view>
        <BaseInput
          v-model="checkHotel.hotelName"
          :disabled="true"
          :placeholder="'请选择' + vPointName"
          rightText="arrow"
          @onClick="selectPlace"
        />
      </view>
      <view class="content-box">
        <view class="title">
          <view>收货人</view>
          <view class="necessary">*</view>
        </view>
        <BaseInput v-model="checkHotel.linkman" placeholder="请输入收货人" />
      </view>
      <view class="content-box">
        <view class="title">
          <view>手机号</view>
          <view class="necessary">*</view>
        </view>
        <BaseInput
          v-model="checkHotel.linkmanPhone"
          placeholder="请输入手机号"
        />
      </view>
      <view class="content-box">
        <view class="title">
          <view>收货地址</view>
          <view class="necessary">*</view>
        </view>
        <BaseInput v-model="checkHotel.address" placeholder="请输入收货地址" />
      </view>
    </view>
    <view class="goods-card">
      <PurchaseGoodsCard
        :isShow="false"
        v-for="item in goodsList"
        :key="item.goods_id"
        :info="item"
      />
    </view>

    <!-- 占位safe -->
    <view :style="{ height: 120 + vIphoneXBottomHeight + 'rpx' }"></view>
    <view
      class="fixed-btn flexRowBetween"
      :style="{ paddingBottom: 20 + vIphoneXBottomHeight + 'rpx' }"
    >
      <view class="total">
        <view class="total-box">
          <view>合计：</view>
          <view class="total-box-mark">￥</view>
          <view class="total-box-price">{{ goodsTotalPrice.toFixed(2) }}</view>
        </view>
        <view class="total-num">共计{{ goodsTotalNum }}件</view>
      </view>
      <BaseButton width="200" type="primary" @onClick="confirmOrder">
        确认提交
      </BaseButton>
    </view>
  </view>
</template>

<script>
import BaseButton from '../../components/base/BaseButton.vue'
import BaseInput from '../../components/base/BaseInput.vue'
import BaseNavbar from '@/components/base/BaseNavbar.vue'
import PurchaseGoodsCard from '../components/cards/PurchaseGoodsCard.vue'
export default {
  components: { BaseNavbar, BaseInput, PurchaseGoodsCard, BaseButton },
  data() {
    return {
      checkHotel: {}, //选择的点位信息
      goodsList: [],
      goodsTotalPrice: 0,
      goodsTotalNum: 0,
    }
  },
  methods: {
    selectPlace() {
      uni.navigateTo({ url: '/pagesB/place/SelectPlace?from=home_order' })
    },
    confirmOrder() {
      if (
        !this.checkHotel.hotelName ||
        !this.checkHotel.linkman ||
        !this.checkHotel.linkmanPhone ||
        !this.checkHotel.address
      ) {
        return this.isShowErr('请填写完整信息~')
      }
      let goodsInfo = []
      this.goodsList.forEach((item) => {
        goodsInfo.push({
          goods_id: item.goods_id,
          num: item.isAmount,
        })
      })
      let data = {
        hotelid: this.checkHotel.id,
        address: this.checkHotel.address,
        hotelName: this.checkHotel.hotelName,
        receiverPhone: this.checkHotel.linkmanPhone,
        receiver: this.checkHotel.linkman,
        zongjia: this.goodsTotalPrice,
        goodinfo: goodsInfo,
      }
      this.$u.api.addPurchaseOrder(data).then((res) => {
        this.isShowSuccess('确认订单成功', 1, () => {}, false)
      })
    },
  },
  onLoad(opt) {
    let goodsData = uni.getStorageSync('select_purchase_goods')
    this.goodsList = goodsData?.list || []
    this.goodsTotalPrice = goodsData?.totalPrice || 0
    this.goodsTotalNum = goodsData?.totalNum || 0
  },
  onShow(e) {
    /*  #ifndef H5 */
    let pages = getCurrentPages()
    let currPage = pages[pages.length - 1] // 当前页
    if (currPage.data.item) {
      // 有值
      // 修改listData中值
      this.checkHotel = currPage.data.item
      this.hotelName = this.checkHotel.hotelName
      this.hotel_id = this.checkHotel.id
    }
    /*#endif */
    /*  #ifdef H5 */
    this.checkHotel = this.vCurrPage.item
    this.hotelName = this.checkHotel.hotelName
    this.hotel_id = this.checkHotel.id
    /*#endif */
  },
}
</script>
<style lang="scss">
page {
  background-color: $pageBgColor;
}
</style>
<style lang="scss" scoped>
.content {
  background-color: $uni-bg-color;
  padding: 20rpx 30rpx;
  &-box {
    margin-bottom: 20rpx;
    .title {
      display: flex;
      align-items: center;
      color: $textBlack;
      font-size: $font-size-middle;
      font-weight: bold;
      margin-bottom: 20rpx;
      .necessary {
        color: red;
        margin-left: 4rpx;
      }
    }
  }
}
.goods-card {
  padding: 20rpx 30rpx;
}
.fixed-btn {
  position: fixed;
  left: 0;
  bottom: 0;
  width: 100%;
  padding: 20rpx 30rpx;
  box-sizing: border-box;
  background-color: $uni-bg-color;
  z-index: 999999;
  .total {
    &-box {
      display: flex;
      align-items: flex-end;
      color: $textBlack;
      font-size: $font-size-base;
      &-mark {
        color: red;
        font-size: $font-size-xsmall;
      }
      &-price {
        color: red;
        font-size: $font-size-middle;
      }
    }
    &-num {
      color: $textDarkGray;
      font-size: $font-size-small;
    }
  }
}
</style>
