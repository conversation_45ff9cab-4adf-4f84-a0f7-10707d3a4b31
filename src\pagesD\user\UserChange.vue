<template>
  <view>
    <BaseNavbar :title="title" />
    <view class="title">
      <text>轻触头像以切换账号</text>
    </view>

    <view class="list">
      <view
        class="list-li"
        v-for="(item, i) in vUserList"
        :key="i"
        @click="upClick(item, i)"
      >
        <view class="list-pie" v-show="i == 0">
          <text>当前</text>
        </view>
        <image :src="logoImg" alt="" />
        <text>{{ item.userName }}</text>
      </view>
      <view class="list-li" @click="addClick">
        <image class="big-img" src="@/pagesD/static/img/add.png" alt="" />
        <text>添加账号</text>
      </view>
    </view>
    <view class="buttom">
      <text @click="ondeleUser">管理账号</text>
    </view>
  </view>
</template>

<script>
import BaseNavbar from "@/components/base/BaseNavbar.vue";
import { mapState, mapMutations } from "vuex";
export default {
  components: { BaseNavbar },
  data() {
    return {
      title: "切换账号",
    };
  },
  computed: {
    ...mapState(["vUserList"]),

    logoImg() {
      return this.vSiteConfig?.site_info?.site_logo || "";
    
  },
  },
  created() {
    if (!this.vUserList.lenght) {
      if (this.vUserInfo) {
        // console.log(this.vUserInfo, "tokenddd");
        this.addItem(this.vUserInfo);
      }
    }
  },
  methods: {
    ...mapMutations(["setList", "addItem", "updateItem", "deleteItem"]),
    goPage(url) {
      uni.navigateTo({ url });
    },
    upClick(item, i) {
      if (i == 0) {
         this.$u.toast("已登录当前账号~");
      } else {
        this.updateItem(item);
        this.$u.vuex("vToken", item.token);
        this.$u.vuex("vUserInfo", item);
        uni.navigateTo({ url: "/pages/login/Login?from=userUpdata" });
      }
    },
    addClick() {
      uni.navigateTo({ url: "/pages/login/Login?from=userAdd" });
    },
    ondeleUser() {
      uni.navigateTo({ url: "/pagesD/user/UserList" });
    },
  },
};
</script>

<style lang="scss">
page {
  background-color: $pageBgColor;
}
</style>

<style lang="scss" scoped>
.title {
  text-align: center;
  // border: 1px solid #000;
  margin: 40rpx 0;
  font-size: 50rpx;
  color: #555555;
}
.list {
  // border: 1px solid #000;
  .list-li {
    height: 150rpx;
    margin: 25rpx 35rpx;
    background-color: white;
    border-radius: 15rpx;
    display: flex;
    align-items: center;
    position: relative;
    // border: 1px solid #000;
    image {
      margin: 0 20rpx;
      vertical-align: middle;
      width: 90rpx;
      height: 90rpx;
    }
    .big-img{
      width: 60rpx;
      height: 60rpx;
    }
    > text {
      font-size: 35rpx;
      font-weight: bold;

      vertical-align: middle;
    }
    .list-pie {
      position: absolute;
      top: 0;
      right: 0;
      font-size: 24rpx;
      padding: 5rpx 20rpx;
      border-top-right-radius: 10rpx;
      border-bottom-left-radius: 10rpx;
      color: white;
      background: #206cc5;
      background-image: linear-gradient(90deg, #7fbad1, #4da2e7, #0473f1);
    }
  }
}
.buttom {
  margin-top: 50rpx;
  font-size: 30rpx;
  text-align: center;
  color: #206cc5;
}
</style>