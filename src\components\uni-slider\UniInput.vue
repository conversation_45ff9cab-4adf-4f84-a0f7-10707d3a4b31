<template>
    <u-input :type="typeU" :boder="boder" v-model="adjust_time" :placeholder="placeholder"/>
</template>

<script>
    export default {
        props:{
            typeU:{
                type:String,
                default:"number"
            }
        },
        props:{
            border:{
                type:Boolean, // 是否显示边框
                default:true
            }
        },
        props:{
           placeholder:{
            type:String,
            default:""
           } 
        },
        data() {
            return {
                adjust_time:""
            }
        },
    }
</script>

<style lang="scss" scoped>

</style>