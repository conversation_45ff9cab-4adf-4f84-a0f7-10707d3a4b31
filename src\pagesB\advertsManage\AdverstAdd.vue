<template>
  <view>
    <BaseNavbar :title="title" />
    <view class="content">
      <view class="content-box">
        <view class="content-box-title">排序</view>
        <view class="content-box-txt">
          <BaseInput v-model="ad.sort" placeholder="请输入排序序号" />
        </view>
      </view>
      <view class="content-box">
        <view class="content-box-title">广告名称</view>
        <view class="content-box-txt">
          <BaseInput v-model="ad.ad_desc" placeholder="请输入广告名称" />
        </view>
      </view>
      <view class="content-box">
        <view class="content-box-title">广告时间</view>
        <view class="content-box-txt">
          <BaseInput
            v-model="ad.time"
            rightText="秒"
            placeholder="请输入广告时间"
          />
        </view>
      </view>
      <view class="content-box">
        <view class="content-box-title">广告链接</view>
        <view class="content-box-txt">
          <BaseInput v-model="ad.img_href" placeholder="请输入广告链接" />
        </view>
      </view>
      <view class="content-box">
        <view class="content-box-title">链接类型</view>
        <view class="content-box-txt">
          <BaseInput
            @onClick="showLinkType = true"
            :disabled="true"
            placeholder="请选择链接类型"
            v-model="link_title"
          />
        </view>
      </view>

      <view class="content-box">
        <view class="content-box-title">文字内容</view>
        <view class="content-box-txt">
          <BaseInput v-model="ad.content_text" placeholder="请输入文字内容" />
        </view>
      </view>
      <view class="content-box">
        <view class="content-box-title flexRowBetween">
          <view>是否在小程序显示</view>
          <u-switch v-model="ad.is_show_wechatmini"></u-switch>
        </view>
      </view>
      <view class="content-box">
        <view class="content-box-title">文件类型</view>
        <view class="content-box-txt">
          <BaseInput
            @onClick="showFileType = true"
            :disabled="true"
            placeholder="请选择文件类型"
            v-model="file_title"
          />
        </view>
      </view>
      <view class="content-box">
        <view class="content-box-title">
          广告{{ file_title ? file_title : "图片" }}
        </view>
        <view class="content-box-txt">
          <view class="url-txt">
            <text user-select>
              {{ ad.img_url }}
            </text>
          </view>
          <!-- <BaseUpload
            maxCount="1"
            width="200"
            :fileList="defaultFileList"
            :auto="true"
            @onUpload="onUpload"
          /> -->
          <UploadAll
            :defaultImgList="defaultImgList"
            :fileType="fileType"
            @upload="upload"
          />

          <video
            class="video"
            v-if="ad.file_type === 2 && ad.img_url"
            :src="ad.img_url"
          ></video>
        </view>
      </view>
    </view>

    <!--popup 链接类型-->
    <BasePopup :show.sync="showLinkType" :safeArea="true" zIndex="999999">
      <TypeSelectPopup
        title="选择链接类型"
        :list="linkTypeList"
        @comfirm="comfirmLinkType"
      />
    </BasePopup>
    <!--popup 文件类型-->
    <BasePopup :show.sync="showFileType" :safeArea="true" zIndex="999999">
      <TypeSelectPopup
        title="选择链接类型"
        :list="fileTypeList"
        @comfirm="comfirmFileType"
      />
    </BasePopup>
    <!-- 保存编辑按钮 -->
    <view
      class="btn-box"
      :style="{ paddingBottom: 20 + vIphoneXBottomHeight + 'rpx' }"
    >
      <BaseButton type="primary" @onClick="save">{{ btnTitle }}</BaseButton>
    </view>
    <SafeBlock :height="120" />
  </view>
</template>

<script>
import BaseNavbar from "@/components/base/BaseNavbar.vue";
import BaseInput from "../../components/base/BaseInput.vue";
import BaseUpload from "../../components/base/BaseUpload.vue";
import BasePopup from "../../components/base/BasePopup.vue";
import TypeSelectPopup from "../../components/common/TypeSelectPopup.vue";
import UploadAll from "@/pagesB/advertsManage/components/UploadAll.vue";
import BaseButton from "../../components/base/BaseButton.vue";
import SafeBlock from "../../components/common/SafeBlock.vue";

export default {
  components: {
    BaseNavbar,
    BaseInput,
    BaseUpload,
    BasePopup,
    TypeSelectPopup,
    UploadAll,
    BaseButton,
    SafeBlock,
  },
  data() {
    return {
      title: "添加广告",
      id: "",
      fromData: "",
      ad: {
        ad_desc: "", //广告名称
        time: null, //广告时间
        img_href: "", //广告链接
        link_type: 0, //广告类型
        sort: "", //排序序号
        content_text: "", //文字内容
        img_url: "", //广告图片链接
        is_show_wechatmini: true, //是否在微信小程序显示
        file_type: 1, //文件类型 1是图片 2是视频
      },
      link_title: "", //链接类型名称
      file_title: "", //文件类型名称
      defaultFileList: [], //{ url: "" }默认图片
      showLinkType: false,
      showFileType: false,
      linkTypeList: [
        { title: "图片", type: 0 },
        { title: "视频", type: 1 },
        { title: "音频", type: 2 },
      ],
      fileTypeList: [
        { title: "图片", type: 1 },
        { title: "视频", type: 2 },
      ],
      btnTitle: "添加",
      defaultImgList: [], //默认显示
    };
  },
  computed: {
    fileType() {
      if (this.ad.file_type === 1) {
        return "image";
      } else if (this.ad.file_type === 2) {
        return "video";
      } else {
        return "image";
      }
    },
  },
  methods: {
    upload(e) {
      this.ad.img_url = e?.url || "";
    },
    comfirmLinkType(e) {
      this.ad.link_type = e[0]?.type || 0;
      this.link_title = e[0]?.title || "";
      this.showLinkType = false;
    },
    comfirmFileType(e) {
      this.ad.file_type = e[0]?.type || 1;
      this.file_title = e[0]?.title || "";
      this.showFileType = false;
    },
    save() {
      this.ad.is_show_wechatmini = this.ad.is_show_wechatmini ? 1 : 0;
      let data = {
        ...this.ad,
      };

      if (this.fromData === "add") {
        this.$u.api.addAdvert(data).then((res) => {
          this.isShowSuccess(`${this.btnTitle}成功`, 1, null, true);
        })
        .catch((err=>{
        console.log('错误信息',err)
      }))
      } else if (this.fromData === "edit") {
        data["id"] = this.id;
        this.$u.api.editAdvert(data).then((res) => {
          this.isShowSuccess(`${this.btnTitle}成功`, 1, null, true);
        })
        .catch((err=>{
        console.log('错误信息',err)
      }))
      }
    },
    //获取编辑广告信息
    getAdvertOne() {
      let data = {
        id: this.id,
      };
      this.$u.api.getAdvertOne(data).then((res) => {
        for (let key in this.ad) {
          this.ad[key] = res[key];
        }
        this.link_title =
          this.linkTypeList.find((item) => item.type === this.ad.link_type)
            ?.title || "";

        this.file_title =
          this.fileTypeList.find((item) => item.type === this.ad.file_type)
            ?.title || "";
        if (this.ad.img_url) this.defaultImgList = [{ url: this.ad.img_url }];
      })
      .catch((err=>{
        console.log('错误信息',err)
      }))
    },
  },
  onLoad(opt) {
    this.fromData = opt?.from || "";
    if (this.fromData === "add") {
      this.title = "添加广告";
      this.btnTitle = "添加";
    } else if (this.fromData === "edit") {
      this.title = "编辑广告";
      this.btnTitle = "编辑";
      this.id = opt?.id || null;
      this.getAdvertOne();
    }
  },
};
</script>

<style lang="scss" scoped>
.content {
  padding: 30rpx;
  &-box {
    margin-bottom: 30rpx;
    &-title {
      margin-bottom: 20rpx;
      color: $textBlack;
      font-size: $font-size-middle;
      font-weight: 700;
    }
    .url-txt {
      word-break: break-all;
    }
  }
}
.video {
  width: 100%;
}
.btn-box {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  padding: 20rpx 30rpx;
  background-color: #fff;
  z-index: 99999;
}
</style>