<template>
  <u-count-to
    :startVal="startVal"
    :endVal="value || 0"
    :bold="bold"
    :color="color"
    :fontSize="size"
    :useEasing="true"
    :decimals="decimals"
  ></u-count-to>
</template>

<script>
export default {
  name: "BaseCountTo",
  props: {
    value: {
      type: [Number,String], //数值
      default: 0,
    },
    bold: { type: Boolean, default: true }, //加粗
    color: { type: String, default: "#fff" }, //颜色
    size: { type: [Number,String], default: "30" }, //字体
    decimals: { type: [Number,String], default: 0 }, //显示小数
  },
  data() {
    return {
      startVal: 0, //开始值。默认0
    };
  },
};
</script>

<style lang="scss" scoped>
</style>