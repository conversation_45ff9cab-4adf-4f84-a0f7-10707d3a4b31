<template>
    <view class="box">
        <view class="center" @click.stop="onClick" :style="{ height: `${size}rpx`, width: `${size}rpx` }">
            <u-icon :name="name" :size="size" :color="color"></u-icon>
            <!-- <u-popup v-model="newShow" :mode="mode" :mask="mask" :mask-close-able="maskClose" :height="height"
                :negativeTop="negativeTop" :custom-style="customStyle" :safe-area-inset-bottom="safeArea" :closeable="closeable"
                :width="width" :border-radius="radius" @close="close" :z-index="zIndex">
                <view class="title">{{ title }}</view>
                <view class="text">
                    <slot />
                </view>
    
            </u-popup> -->
            <view :class="mode=='top'?'triangle':'triangle_bt'" :style="type" v-if="show">

            </view>
            <view class="tip-top" :style="typeTop" v-if="show">

                <slot />
            </view>

        </view>
        <view v-if="show" class="zhe" @click.stop="show = false">


        </view>
        <!-- <view> 可玩时</view> -->
    </view>
</template>
  
<script>
import BaseModal from "@/components/base/BaseModal.vue";
export default {
    name: "BaseIconModal",
    components: {
        BaseModal,
    },
    props: {
        name: { type: String },
        size: { type: [Number,String], default: 46 },
        color: {
            type: String,
            default: "#666",
        },
        mode: { type: String, default: "top" }, //top / right / bottom / center
        mask: { type: Boolean, default: true }, //是否显示遮罩
        length: { type: [Number,String], default: "auto" }, //mode=left 有效
        maskClose: { type: Boolean, default: true }, //点击遮罩是否可以关闭弹出层
        height: { type: [Number,String], default: "50" },
        width: { type: [Number,String], default: "600" },
        negativeTop: { type: [Number,String], default: 0 }, //往上偏移的值，单位任意，数值则默认为rpx单位
        customStyle: {
            type: Object,
            default: function () {
                return {};
            }
        },
        safeArea: { type: Boolean, default: false }, //是否开启安全区域
        closeable: { type: String | Boolean, default: false },
        radius: { type: [Number,String], default: "auto" }, //弹窗圆角值
        zIndex: { type: [Number,String], default: 999999 },
        title: {
            type: String, default: '功能提示'
        },
    },
    data() {
        return {
            show: false,
        }
    },
    computed: {
        newShow: {
            get: function () {
                return this.show;
            },
            set: function (val) {
                return this.$emit("update:show", val);
            },
        },
        type(){
            let size 
            if (this.mode=='top') {
                
                size=this.size * 1 +'rpx'
                return `bottom:${size}`
            }else if(this.mode=='bottom'){
                size=(this.size*1)+'rpx'
                return `top:${size}`
            }
            // return {bottom: `${this.mode=='bottom'?:this.size * (-1)}rpx`}
        },
        typeTop(){
            let size 
            if (this.mode=='top') {
                
                size=this.size * 1+15+'rpx'
                return `bottom:${size};width: ${this.width}rpx`
            }else if(this.mode=='bottom'){
                size=(this.size*1)+15+'rpx'
                return `top:${size};width: ${this.width}rpx`
            }
         
        }
    },
    methods: {
        onClick() {
            this.show = true
        },

    },
};
</script>
  
<style lang="scss" scoped>
.box {
    position: relative;

    .center {
        // border: 1px solid #000;
        position: absolute;
        z-index: 900;
        top: 0;
        left: 0;

        u-icon {
            height: 100%;
            display: block !important;

            ::v-deep view {
                // border: 1px solid #000;
                display: block !important;
            }

        }


    }
}


.zhe {
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    position: fixed;
    z-index: 9999;
    // background-color: #000;
}

.text {
    margin-top: 20rpx;
    padding: 0 20rpx;
}

/*提示框容器-上三角形*/
.tip-top {
    width: 500rpx;
    padding: 10rpx;
    background-color: #999;
    position: absolute;
    left: -100rpx;
    // top: 0;
    color: white;
    /*设置圆角*/
    -webkit-border-radius: 5rpx;
    -moz-border-radius: 5rpx;
    border-radius: 5rpx;
}

/*生成2个叠加的三角形*/
.triangle {
    position: absolute;
    // top: 35rpx;

    left: 0rpx;
    width: 0;
    height: 0;
    border-right: 20rpx solid transparent;
    border-left: 20rpx solid transparent;
    border-top: 20rpx solid #999;
    /* 设置三角形的颜色 */
}

.triangle_bt {
    position: absolute;
    // top: 35rpx;

    left: 0rpx;
    width: 0;
    height: 0;
    border-right: 20rpx solid transparent;
    border-left: 20rpx solid transparent;
    border-bottom: 20rpx solid #999;
    /* 设置三角形的颜色 */
}
/*将上面的三角形颜色设置和容器背景色相同*/
.tip-top:after {
    top: -27rpx;
    // border-color: transparent transparent #FFF transparent;
}

/*下三角*/
.tip-bottom {
    margin: 20rpx;
    padding: 5rpx;
    width: 300rpx;
    height: 60rpx;
    border: 2rpx solid #f99;
    position: relative;
    background-color: #0FF;
    /*设置圆角*/
    -webkit-border-radius: 5rpx;
    -moz-border-radius: 5rpx;
    border-radius: 5rpx;
}

.tip-bottom:before,
.tip-bottom:after {
    content: "";
    display: block;
    border-width: 15rpx;
    position: absolute;
    bottom: -30rpx;
    left: 100rpx;
    border-style: solid dashed dashed solid;
    border-color: #f99 transparent transparent transparent;
    font-size: 0;
    line-height: 0;
}

.tip-bottom:after {
    bottom: -27rpx;
    border-color: #0FF transparent transparent transparent;
}

/*左三角*/
.tip-left {
    margin: 20rpx;
    padding: 5rpx;
    width: 300rpx;
    height: 60rpx;
    border: 2rpx solid #f99;
    position: relative;
    background-color: #FFF;
    /*设置圆角*/
    -webkit-border-radius: 5rpx;
    -moz-border-radius: 5rpx;
    border-radius: 5rpx;
}

.tip-left:before,
.tip-left:after {
    content: "";
    display: block;
    border-width: 15rpx;
    position: absolute;
    left: -30rpx;
    top: 20rpx;
    border-style: dashed solid solid dashed;
    border-color: transparent #f99 transparent transparent;
    font-size: 0;
    line-height: 0;
}

.tip-left:after {
    left: -27rpx;
    border-color: transparent #FFF transparent transparent;
}

/*右三角*/
.tip-right {
    margin: 20rpx;
    padding: 5rpx;
    width: 300rpx;
    height: 60rpx;
    border: 2rpx solid #f99;
    position: relative;
    background-color: #FFF;
    /*设置圆角*/
    -webkit-border-radius: 5rpx;
    -moz-border-radius: 5rpx;
    border-radius: 5rpx;
}

.tip-right:before,
.tip-right:after {
    content: "";
    display: block;
    border-width: 15rpx;
    position: absolute;
    right: -30rpx;
    top: 20rpx;
    border-style: dashed solid solid dashed;
    border-color: transparent transparent transparent #f99;
    font-size: 0;
    line-height: 0;
}

.tip-right:after {
    right: -27rpx;
    border-color: transparent tr
}
</style>