<template>
  <view class="EquipmentSelect">
    <BaseNavbar title="设备选择"> </BaseNavbar>
    <BaseSearch placeholder="请输入设备编号" @search="search" listType="equipmentsSelect" />
    <!-- <BaseList listType="equipmentsSelect" @searchChange="searchChange" /> -->
    <view class="equipment_content">
      <ComList :loading-type="loadingType">
        <view v-for="(item, index) in listData" :key="index" class="hotelItem">
          <radio
            :checked="selectIndex == index"
            color="#2979ff"
            style="transform: scale(0.7)"
          />

          <view class="hotelItemDesc" @click="select(item, index)">
            <view class="hotelItemDescContent">
              <view class="hotelName">{{vPointName}}名称:{{
                item.hotelName ? item.hotelName : ""
              }}</view>
            
              <text class="address" user-select>系统码:{{ info.vCode != 0 && info.vCode ? info.vCode : '未绑定' }}</text>
            </view>
          </view>
        </view>
      </ComList>
    </view>
  </view>
</template>

<script>
import BaseNavbar from "@/components/base/BaseNavbar.vue";
import myPull from "@/mixins/myPull.js";
import ComList from "@/components/list/ComList.vue";
import BaseSearch from "@/components/base/BaseSearch.vue";
// import BaseList from '@/components/base/BaseList.vue'
import { AddValueInObject } from '@/wxutil/list'
export default {
  components: {
    BaseNavbar,
    ComList,
    BaseSearch,
    // BaseList
  },
  data() {
    return {
      hotel_id: 0,
      dianwei: 0,
      room_num: Number,
      status: Number,
      device_sn: "",
      owner: "", //上级账号
      selectIndex: -1,
      hotelName: '', //搜索
    };
  },
  methods: {
    async getList(page,done) {
      let data = {
        device_sn: this.device_sn,
        room_num: this.room_num,
        status: this.status, // 是否在线
        dianweiid: this.dianwei,
        hotel_id: this.hotel_id,
        page: page,
        owner: this.owner, //上级账号
      };
      // 获取数据

      let rtnData = await this.$u.api.getUserUMs(data);
      rtnData.data = rtnData.data.filter((item) => item.vCode != null);
      done(rtnData.data)
    },
    searchChange(val) {
      this.hotelName = val
      this.search(val)
    },
    search(val) {
      this.device_sn = val;
      let list = AddValueInObject(this.vServerList.equipmentsSelect, val)
      this.$u.vuex(`vServerList.equipmentsSelect`, list)
      this.refresh();
    },
    select(item, index) {
      this.selectIndex = index;
    /*  #ifndef H5 */
    var pages = getCurrentPages();
      var currPage = pages[pages.length - 1]; //当前页面
      var prevPage = pages[pages.length - 2]; //上一个页面
      //直接调用上一个页面的setData()方法，把数据存到上一个页面中去
      prevPage.setData({
        item: item,
      });
    /*#endif */
     /*  #ifdef H5 */
     this.vCurrPage.item=item
    /*#endif */
      uni.navigateBack({
        delta: 1,
      });

      return;
    },
  },
  onLoad() {
    this.refresh();
  },
  onShow(e) {
   /*  #ifndef H5 */
    let pages = getCurrentPages();
    let currPage = pages[pages.length - 1]; // 当前页
    if (currPage.data.isDoRefresh == true) {
      // 是否刷新
      currPage.data.isDoRefresh = false;
      this.refresh();
    }
    /*#endif */
    /*  #ifdef H5 */
    if (this.vCurrPage.isDoRefresh == true) {
      // 是否刷新
      this.vCurrPage.isDoRefresh = false;
      this.refresh();
    }
    /*#endif */
  },

  mixins: [myPull({})],
};
</script>
<style lang="scss">
page {
  background-color: $pageBgColor;
}
</style>
<style lang="scss" scoped>
.hotelName{
  overflow-wrap: normal;
}
.hotelItem {
  width: 100%;
  background: rgba(255, 255, 255, 1);
  display: flex;
  align-items: center;
  border-bottom: 1rpx solid rgba(229, 229, 229, 1);
  border-radius: $cardRadius;
  box-sizing: border-box;
  padding: 30rpx;
  margin-bottom: 20rpx;
  .hotelItemDesc {
    width: 100%;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;

    .hotelName {
      font-size: 25rpx;
      margin-bottom: 10rpx;
      color: rgba(40, 40, 40, 1);
    }

    .address,
    .linkman {
      font-size: 25rpx;
      color: rgba(40, 40, 40, 1);
    }
  }
}
</style>