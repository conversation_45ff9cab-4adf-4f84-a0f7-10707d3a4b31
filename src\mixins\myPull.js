/**
 * @name 封装下拉
 * @params getList 刷新数据的函数
 * @params listData 存放数据的变量名
 * @params page 页数变量名
 */
export default ({ getList = "getList", listData = "listData", page = "page" } = {}) => {
    return {
        data() {
            return {
                [listData]: [],
                pagesData: [], // 存放分页数据的二维数组
                [page]: 1,
                pageSize: 10,
                loadingType: -1,
            };
        },
        onPullDownRefresh() {
            this.onTop();
        },
        onReachBottom() {
            this.onBottom();
        },
        methods: {
            refresh() {
                this[page] = 1;
                this[listData] = [];
                this.pagesData = [];
                this.loadingType = -1;
                this[getList](this[page], this.__pulldone);
            },
            __pulldone(data) {
                if (!data || data.length === 0) {
                    this.loadingType = this[listData].length === 0 ? 3 : 2;
                    uni.stopPullDownRefresh();
                    return;
                }
        
                if (this[page] === 1) {
                    this.pagesData = [data];
                } else {
                    this.pagesData.push(data);
                }
        
                this[listData] = [].concat(...this.pagesData);
                // console.log('listData', this[listData], this.pagesData);
                this.loadingType = data.length < this.pageSize ? 2 : 0;
                uni.stopPullDownRefresh();
                this[page]++;
            },
            onBottom() {
                if (this.loadingType !== 0 || this.isScrolling) return;
                this.loadingType = 1;
                this[getList](this[page], this.__pulldone);
            
            },
            onTop() {
                if (this.loadingType === 1) return;
                this.refresh();
            }
        }
    };
};
