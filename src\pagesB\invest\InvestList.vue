<template>
  <view>
    <BaseNavbar title="认养设备" />
    <BaseTabs :current="curTabIndex" :list="tabList" @change="tabChange" :isShowBar="false" />
    <ComList :loadingType="loadingType">
      <InvestListCard
        v-for="item in listData"
        :key="item.id"
        :info="item"
        @refresh="refresh"
      />
    </ComList>
  </view>
</template>
<script>
import BaseNavbar from "@/components/base/BaseNavbar.vue";
import ComList from "@/components/list/ComList.vue";
import myPull from "../../mixins/myPull";
import InvestListCard from "../components/cards/InvestListCard";
import BaseTabs from "../../components/base/BaseTabs.vue";
export default {
  components: { BaseNavbar, ComList, InvestListCard, BaseTabs },
  mixins: [myPull()],
  data() {
    return {
      curTabIndex: 0,
      tabList: [
        {
          name: "全部",
          status: -1,
        },

        {
          name: "认养中",
          status: 2,
        },
        {
          name: "认养结束",
          status: 3,
        },
      ],
    };
  },
  methods: {
    getList(page, done) {
      let data = {
        page,
        limit: 10,
        status: this.tabList[this.curTabIndex]?.status ?? 0,
      };
      //   this.$u.api.getInvestOrderList(data).then((res) => {
      //     done(res.list);
      //   });

      this.$u.api.getInvestUMList(data).then((res) => {
        done(res.list);
      });
    },
    tabChange(e) {
      this.curTabIndex = e;
      this.refresh();
    },
  },
  onLoad() {
    this.refresh();
  },
};
</script>

<style lang="scss">
page {
  background-color: $pageBgColor;
}
</style>
<style scoped  lang='scss'>
</style>