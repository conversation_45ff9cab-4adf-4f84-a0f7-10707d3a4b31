<template>
  <view class="lately_live_card" @click="goDetails">
    <image class="top_img" :src="info.thumbnail" />
    <view class="bt_info">
      <view class="title textMaxTwoLine">{{ info.title }}</view>
      <view class="time_box flexRowBetween">
        <!-- <view class="not_yet">{{ info.post_keywords }}</view> -->
        <view>{{ getTime }}</view>
        <!-- <view>时长 2:30:56</view> -->
      </view>
    </view>
  </view>
</template>

<script>
import { timestampToTime } from "@/common/tools/utils.js";
export default {
  name: "CloudCollegeCard",
  props: {
    info: { type: Object, default: {} },
  },
  computed: {
    getTime() {
      return timestampToTime(this.info.time * 1000, true);
    },
  },
  methods: {
    goDetails() {
      uni.navigateTo({
        url: "/pagesC/webView/WebView?url=" + encodeURIComponent(this.info.url),
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.lately_live_card {
  width: 330rpx;
}
.top_img {
  width: 100%;
  height: 200rpx;
  background: #c2c2c2;
  border-radius: $imgRadius;
}
.bt_info {
  width: 100%;
  .title {
    color: $textBlack;
    font-size: $font-size-base;
    font-weight: bold;
    margin-bottom: 20rpx;
  }
  .time_box {
    height: 40rpx;
    color: $textDarkGray;
    font-size: $font-size-xsmall;
    .not_yet {
      height: 50rpx;
      width: 130rpx;
      display: flex;
      justify-content: center;
      align-items: center;
      background: #ff8e49;
      border-radius: 4px;
      color: $textWhite;
    }
  }
}
</style>