<template>
  <view class="card" @click="onClick">
    <view class="top card_top">
      <view class="top_item">
        <view class="text-just">{{ vPointName }}：</view>
        <view class="childTwo">
          <text @click.stop="navigator(info)">{{ info.hotelName || '' }}</text>
        </view>
      </view>
      <view class="top_item">
        <view class="text-just">{{ vPointName }}账号：</view>
        <view class="childTwo">
          <text @click.stop="navigator()" user-select>{{ info.dw.user_login }}</text>
        </view>
      </view>
      <view class="top_item">
        <view class="text-just">联系人：</view>
        <view>{{ info.linkman || '' }} {{ info.linkmanPhone }}</view>
      </view>
      <view class="top_item">
        <view class="text-just">详细地址：</view>
        <view class="textMaxTwoLine">{{ info.address }}</view>
      </view>
      <!-- <view class="top_item">
        <view class="text-just">免费功能：</view>
        <view class="textMaxTwoLine" :style="{ color: info.is_all_free == 0 ? '' : 'red' }">{{ info.is_all_free == 1 ?
          '开启' : '关闭'
        }}</view>
      </view> -->
      <!-- <view class="top_items" v-if="vButtonPermis">
        <view>
          <view>
            <view class="btnTitle">
              <view>

                自动待机
              </view>
              <view class="title_right">
                <BaseIconModal name="question-circle" size="30" v-if="prompt">
                  {{ prompt }}
                </BaseIconModal>
              </view>
            </view>
          </view>
          <view :style="{ color: info.is_auto_on_off ? '#0EADE2' : 'red' }">{{ info.is_auto_on_off ? "开启" : "关闭" }}
          </view>
          <view :style="{ margin: '0 10rpx' }" v-if="info.is_auto_on_off">
            {{ startTime }}-{{ endTime }}
          </view>

        </view>
        <view v-if="vButtonPermissions && vButtonPermisAides">
          <view>合作方式：</view>
          <view class="textMaxTwoLine">{{ info.divide_type == 1 ? '租赁' : info.divide_type == 2 ? `${vPointName}分成` :
            '账号分成' }}
          </view>
        </view>

      </view> -->


      <view class="top_item" v-if="vButtonPermis">
        <view class="text-just">租赁剩余时间：</view>
        <view :style="{ color: clserTime > 30 || info.rent_end_time == 0 ? '' : 'red' }">{{
          info.rent_end_time == 0 ? '未设置' : clserTime < 0 ? '已到期' : formatDaysToYearsMonthsDays(clserTime) }} </view>
        </view>

        <view class="top_item">
          <view class="text-just">拥有者：</view>
          <view>
            <text user-select>
              {{ info.to_user.user_login }}
            </text>
          </view>
        </view>
        <view class="divede flexRowBetween" v-if="info.divide_type != 1 && vButtonPermissions && vButtonPermisAides">
          <view class="flexColumnAllBetween">
            <view class="percentage">{{ info.divide_type == 2 ? info.divide_per || '0' : info.per ?
              info.per : "0" }}%</view>
            <view class="name">{{ info.divide_type == 1 ? '租赁' : info.divide_type == 2 ? `${vPointName}分成` : '账号分成' }}
            </view>
          </view>
          <view v-if="info.divide_type == 0" class="modify" @click.stop="modify">修 改</view>
        </view>
        <view class="divede flexRowBetween" v-if="info.divide_type == 1 && vButtonPermissions && vButtonPermisAides">
          <view class="flexColumnAllBetween">
            <view class="percentage">{{ info.divide_per ? info.divide_per : "0" }}/{{ info.rent_unit == 0 ? '月' : '年' }}
            </view>
            <view class="name">租赁套餐</view>
          </view>
        </view>
      </view>
      <view class="card_btn flexRowBetween">
        <view v-if="vButtonPermis" class="card_btn_box" type="default" shape="circle" @click.stop="deviceList">设备列表{{
          info.machine_num ? `(${info.machine_num})` : ""
        }}</view>

        <view class="card_btn_box" @click.stop="BulkManagement">
          批量管理
        </view>

        <block v-if="vButtonPermissions && vButtonPermisAides">
          <!-- <view class="card_btn_box" @click.stop="goodsTemp">
            {{
              vCargoLanes }}模板
          </view> -->
          <!-- <view class="card_btn_box" @click.stop="free">
            {{ info.is_all_free ? "关闭免费" : "开启免费" }}
          </view> -->
          <!-- <view class="card_btn_box" @click.stop="ckLotter">
            定时出泡泡
          </view> -->
          <!-- <view class="card_btn_box" @click.stop="autoOnOff">
            <view class="btnTitle">
              {{ info.is_auto_on_off ? "关闭自动待机" : "开启自动待机" }}
            </view>
          </view> -->
          <view class="card_btn_box" @click.stop="more">
            更多
          </view>
        </block>
      </view>
    </view>
</template>

<script>
import BaseButton from "@/components/base/BaseButton.vue";
import BaseIconModal from "@/components/base/BaseIconModal.vue";
export default {
  components: { BaseButton, BaseIconModal },
  name: "PlaceListCard",
  props: {
    info: {
      type: Object,
      default: function () {
        return {};
      }
    },
  },
  data() {
    return {
      prompt: '开启自动待机将会在营业时间内自动开启灯光与音乐',
      // showTaskType:false,
      // taskTypeList:[],
      // unBindItem: {},
    }
  },
  computed: {
   
    startTime() {
      // 状态
      if (this.info?.opening_hours) {
        return this.info?.opening_hours?.split("_")[0];
      } else {
        return '09:00'
      }
    },
    endTime() {
      if (this.info?.opening_hours) {
        return this.info?.opening_hours?.split("_")[1];
      } else {
        return '22:00'
      }

    },
    clserTime() {
      return ((this.info.rent_end_time * 1000 - new Date().valueOf()) / 1000 / 60 / 60 / 24).toFixed(0)

    },
    role_id() {
      if (this.vUserInfo.role_id < 8) {
        return true
      } else {
        return false
      }
    }
  },

  methods: {
    navigator(item) {
      console.log('跳转信息', item)
      if (item&&item.dianweiid && item.hotel_id)
      uni.navigateTo({
        url: `/pagesB/device/DeviceList?from=place&dianwei=${item.dianweiid}&hotel_id=${item.hotel_id}`,
      })
    },
    goodsTemp() {
      this.$emit("goodsTemp");
    },
    modify() {
      this.$emit("modify");
    },
    onClick() {
      if (!this.vButtonPermis) {
        return
      }
      this.$emit("click");
    },
    deviceList() {
      this.$emit("deviceList");
    },
    saleGoods() {
      this.$emit("saleGoods");
    },
    chargeTemp() {
      this.$emit("chargeTemp");
    },
    more() {
      this.$emit("more");
    },
    autoOnOff() {
      this.$emit("autoOnOff");
    },
    ckLotter() {
      this.$emit("ckLotter");
    },
    BulkManagement() {
      this.$emit('BulkManagement')
    },



    formatDaysToYearsMonthsDays(days) {
      if (days < 0) {
        return "Invalid input: days must be a non-negative number.";
      }

      const daysInYear = 365;
      const daysInMonth = 30;

      const years = Math.floor(days / daysInYear);
      const remainingDaysAfterYears = days % daysInYear;

      const months = Math.floor(remainingDaysAfterYears / daysInMonth);
      const remainingDays = remainingDaysAfterYears % daysInMonth;

      let result = "";
      if (years > 0) {
        result += `${years}年 `;
      }
      if (months > 0) {
        result += `${months}月 `;
      }
      if (remainingDays > 0) {
        result += `${remainingDays}天 `;
      }

      return result.trim();
    }
  }
};
</script>

<style lang="scss">
page {
  background-color: $pageBgColor;
}
</style>
<style lang="scss" scoped>
.title_right {
  margin-left: 2rpx;
}

.btnTitle {
  display: flex;

  // align-items: center;
  >view:nth-child(2) {
    // border: 1px solid #000;
    width: 40rpx;
  }
}


.card_btn {
  padding: 0rpx;
  flex-wrap: wrap;
  justify-content: space-between;

  align-items: center;

  .card_btn_box {
    box-sizing: border-box;
    width: 190rpx;
    display: flex;
    justify-content: center;
    align-items: center;
    // width: 30%;
    border-radius: 50rpx;
    margin: 10rpx;
    padding: 20rpx 0rpx;
    border: 2rpx solid rgb(212, 212, 212);
    // &:nth-child(n + 4) {
    //   margin-top: 20rpx;
    // }
  }
}

.card {
  .top {
    position: relative;
    color: $textDarkGray;
    font-size: $font-size-base;

    &_item {
      display: flex;
      margin-bottom: 20rpx;

      >view:last-child {
        color: $textBlack;
      }

      .textMaxTwoLine {
        width: 400rpx;
        font-size: 22rpx;
        display: flex;
        align-items: center;
      }
    }

    .divede {
      position: absolute;
      right: 20rpx;
      bottom: 30rpx;

      .modify {
        color: $themeComColor;
        font-size: $font-size-base;
        margin-left: 30rpx;
      }

      .percentage {
        color: $themeComColor;
        font-size: $font-size-middle;
        font-weight: bold;
        margin-bottom: 16rpx;
      }

      .name {
        color: $textDarkGray;
        font-size: $font-size-small;
      }
    }
  }

  .text-just {
    width: 200rpx;
    text-align: justify;
    text-align-last: justify;
  }

  .top_items {
    display: flex;
    justify-content: space-between;
    margin: 25rpx 0;

    // border: 1px solid #000;
    >view {
      display: flex;
      // margin-right: 60rpx;
    }


  }

  .card_btn {
    padding: 20rpx !important;
    flex-wrap: wrap;

    .card_btn_box {
      box-sizing: border-box;
      width: 190rpx;
      display: flex;
      justify-content: center;
      align-items: center;
      // width: 30%;
      border-radius: 50rpx;
      margin: 10rpx;
      padding: 20rpx 0rpx;
      border: 2rpx solid rgb(212, 212, 212);
      // &:nth-child(n + 4) {
      //   margin-top: 20rpx;
      // }
    }
  }

  .bottom {
    padding: 20rpx 20rpx;
    box-sizing: border-box;
  }
}
.childTwo {
  width: 480rpx;
}

</style>
