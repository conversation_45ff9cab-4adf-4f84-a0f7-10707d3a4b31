<template>
  <view class="RelevanceEquipment">
    <BaseNavbar :title="title"></BaseNavbar>
    <!-- 内容区域 -->
    <view class="relevance">
      <!-- 扫描系统码区域 -->
      <view class="relevance_top">
        <view>
          设备码：
          <input
            style="width: 240rpx;"
            v-model="systematicCode"
            placeholder="请输入"
          />
        </view>
        <img
          src="./../../static/img/icon/relevan-scan.png"
          alt=""
          @click="getSanArguments()"
        />
        <view class="top_2">扫描系统码</view>
      </view>
      <view class="relevance_center relevance_top" @click="selectDeviceCode">
        <text user-select>
          系统码：
          <span style="width: 240rpx;">
            {{ deviceCode ? deviceCode : '未绑定' }}
          </span>
        </text>
        <img src="./../../static/img/icon/relevan-scan.png" alt="" />
        <view class="top_2">扫描设备码</view>
      </view>
      <view class="relevance_bottom">关联设备</view>
    </view>
  </view>
</template>

<script>
import BaseNavbar from '@/components/base/BaseNavbar.vue'
import BaseInput from '@/components/base/BaseInput.vue'
import { getUrlParams, getUrlDynamicData } from '@/common/tools/utils.js'
export default {
  components: {
    BaseNavbar,
    BaseInput,
  },
  data() {
    return {
      title: '关联设备',
      isFromDevice: false, //设备来的
      isFromIndexScan: false, //首页扫码来的
      systematicCode: '', //系统码
      deviceCode: '', //设备码
    }
  },
  methods: {
    // 扫码获取参数
    getSanArguments() {
      uni.scanCode({
        onlyFromCamera: false,
        scanType: ['qrCode', 'barCode'],
        success: async ({ result, scanType, charSet, path }) => {
          if (result.includes('vscode')) {
            this.systematicCode = getUrlParams(result, 'vscode')
          } else if (result.includes('device_sn')) {
            this.systematicCode = getUrlDynamicData(result, 'device_sn')
          } else {
            this.promptError()
          }
        },
        fail: (error) => {},
      })
    },
    //二维码错误提示
    promptError() {
      this.isShowErr('请扫码正确的二维码,二维码信息')
    },
    selectDeviceCode() {
      uni.navigateTo({
        url: `/pagesB/equipment/EquipmentSelect`,
      })
    },
  },
  onShow(e) {
    /*  #ifndef H5 */
    let pages = getCurrentPages()
    let currPage = pages[pages.length - 1] // 当前页
    if (currPage.data.item) {
      // 有值
      this.deviceCode = currPage.data.item.vCode
    }
    /*#endif */
    /*  #ifdef H5 */
    if (this.vCurrPage.item) {
      // 有值
      this.deviceCode = this.vCurrPage.item.vCode
    }
    /*#endif */
  },
  onLoad(opt) {
    if (opt?.from) {
      if (opt.from == 'device') {
        // 设备绑定来的
        this.isFromDevice = true
      } else if (opt.from == 'index_scan') {
        // 首页扫码来的
        this.isFromIndexScan = true
      }
    }
  },
}
</script>

<style lang="scss" scoped>
page {
  background-color: $pageBgColor;
}
.RelevanceEquipment {
  width: 100%;
  min-height: 100vh;
  box-sizing: border-box;
  overflow: auto;
  .relevance {
    width: 100%;
    height: 100%;
    padding-top: 105rpx;
    .relevance_top {
      display: flex;
      flex-direction: column;
      align-items: center;
      & > view:nth-child(1) {
        display: flex;
        align-items: center;
        margin-bottom: 23rpx;
        font-size: 28rpx;
        font-weight: 600;
      }
      & > img {
        width: 218rpx;
        height: 221rpx;
        margin-bottom: 33rpx;
      }
      .top_2 {
        width: 276rpx;
        height: 78rpx;
        color: white;
        display: flex;
        justify-content: center;
        font-size: 30rpx;
        align-items: center;
        background: url('./../../static/img/icon/relevan-btn.png') no-repeat;
        background-size: 100% 100%;
        margin-bottom: 79rpx;
        font-weight: 600;
      }
    }
    .relevance_center {
      margin-bottom: 21rpx;
    }
    .relevance_bottom {
      margin: 0px auto;
      width: 560rpx;
      height: 79rpx;
      color: white;
      display: flex;
      justify-content: center;
      font-size: 35rpx;
      align-items: center;
      background: url('./../../static/img/icon/relevance-equipment-bg.png')
        no-repeat;
      background-size: 100% 100%;
      box-sizing: border-box;
    }
  }
}
</style>
