<template>
  <view>
    <view class="sticker">
      <BaseNavbar title="公众号小程序管理" />
      <BaseTabs
        v-if="!isFromAdSelectWechat"
        :list="tabList"
        @change="tabChange"
        :isShowBar="false"
        :currentIndex="curTabIndex"
      />
    </view>
    <ComList :loadingType="loadingType">
      <WechatCard
        v-for="item in listData"
        :key="item.app_id"
        :info="item"
        :isFromAdSelectWechat="isFromAdSelectWechat"
        @refresh="refresh"
      />
    </ComList>
    <FixedAddIcon @onAdd="onAdd" />
  </view>
</template>

<script>
import BaseNavbar from "@/components/base/BaseNavbar.vue";
import myPull from "../../mixins/myPull";
import BaseTabs from "../../components/base/BaseTabs.vue";
import ComList from "@/components/list/ComList.vue";
import WechatCard from "../components/cards/WechatCard.vue";
import FixedAddIcon from "@/components/common/FixedAddIcon.vue";
export default {
  components: { BaseNavbar, BaseTabs, ComList, WechatCard, FixedAddIcon },
  mixins: [myPull()],
  data() {
    return {
      tabList: [
        {
          name: "公众号",
          type: 1,
        },
        {
          name: "小程序",
          type: 2,
        },
      ],
      curTabIndex: 0,
      isFromAdSelectWechat: false, //来自广告选择 公众号
    };
  },
  methods: {
    getList(page, done) {
      let data = {
        page,
        type: this.tabList[this.curTabIndex].type, //type1:获取公众号列表，2小程序列表
      };
      this.$u.api.getWechatList(data).then((res) => {
        done(res);
      });
    },
    tabChange(i) {
      this.curTabIndex = i;
      this.refresh();
    },
    onAdd() {
      uni.navigateTo({
        url: `/pagesB/wechat/WechatAdd?from=add&type=${
          this.tabList[this.curTabIndex].type
        }`,
      });
    },
  },
  onLoad(opt) {
    if (opt?.from === "ad_select_wechat") {
      this.isFromAdSelectWechat = true;
      this.tabChange(opt?.type || 0);
    } else {
      this.refresh();
    }
  },
  onShow() {
   /*  #ifndef H5 */
    let pages = getCurrentPages();
    let currPage = pages[pages.length - 1]; // 当前页
    if (currPage.data.isDoRefresh == true) {
      // 是否刷新
      currPage.data.isDoRefresh = false;
      this.refresh();
    }
    /*#endif */
    /*  #ifdef H5 */
    if (this.vCurrPage.isDoRefresh == true) {
      // 是否刷新
      this.vCurrPage.isDoRefresh = false;
      this.refresh();
    }
    /*#endif */
  },
};
</script>
<style lang="scss">
page {
  background-color: $pageBgColor;
}
</style>
<style lang="scss" scoped>
</style>