<template>
  <view class="card" @click="goEdit">
    <view class="content">
      <view class="check" v-if="isShowCheck">
        <BaseCheck :checked.sync="info.isCheck" />
      </view>
      <view class="container">
        <view class="container-top">
          <view class="container-top-title">设备编号：</view>
          <view class="container-top-txt">{{ info.device_sn }}</view>
        </view>
        <view class="container-content">
          <block v-for="(obj, keys) in info" :key="keys">
            <view
              class="container-content-box"
              v-if="keys != 'id' && keys != 'device_sn' && keys != 'isCheck'"
            >
              <view class="top">
                <view class="top-left">{{ keyObj[keys].name }}</view>
                <view
                  class="top-right"
                  v-if="isShowArrow(obj)"
                  @click.stop="showArrow(keys)"
                >
                  <view class="top-right-txt"> 查看 </view>
                  <view
                    class="top-right-arrow"
                    :class="arrowName === keys ? 'rotate' : ''"
                  >
                    <BaseIcon name="arrow-right" color="#0EADE2" size="28" />
                  </view>
                </view>
              </view>
              <view class="bottom" v-if="arrowName === keys">
                <block v-if="keyObj[keys].type === 'swiper'">
                  <swiper class="swiper-box" :indicator-dots="true">
                    <swiper-item v-for="(item, index) in obj" :key="index">
                      <image
                        v-if="item.file_type == 1"
                        class="img"
                        :src="item.img_url"
                        @click.stop="previewImage(item.img_url)"
                      />
                      <video
                        v-else-if="item.file_type == 2"
                        class="img"
                        :src="item.img_url"
                      />
                    </swiper-item>
                  </swiper>
                </block>
                <block v-else-if="keyObj[keys].type === 'img'">
                  <image
                    v-if="obj.file_type == 1"
                    class="img"
                    mode="widthFix"
                    :src="obj.img_url"
                    @click.stop="previewImage(obj.img_url)"
                  />
                  <video
                    v-else-if="obj.file_type == 2"
                    class="img"
                    :src="item.img_url"
                  />
                </block>
                <block v-else-if="keyObj[keys].type === 'wx-img'">
                  <view class="txt">{{ obj.app_name }}</view>
                  <image
                    class="img"
                    mode="widthFix"
                    :src="obj.wx_img"
                    @click.stop="previewImage(obj.wx_img)"
                  />
                </block>
                <block v-else-if="keyObj[keys].type === 'txt'">
                  <view class="txt">{{ obj.content_text || "暂无~" }}</view>
                </block>
                <block v-else-if="keyObj[keys].type === 'wx-swiper'">
                  <view class="wx-swiper">
                    <swiper class="swiper-box" :indicator-dots="true">
                      <swiper-item v-for="(item, index) in obj" :key="index">
                        <view class="txt">{{ item.app_name }}</view>
                        <image
                          class="img"
                          :src="item.wx_img"
                          @click.stop="previewImage(item.wx_img)"
                        />
                      </swiper-item>
                    </swiper>
                  </view>
                </block>
              </view>
            </view>
          </block>
        </view>
      </view>
    </view>
    <view class="btn">
      <view class="del">
        <BaseButton type="default" shape="circle" @onClick="isShowModal = true">
          清空广告
        </BaseButton>
      </view>
    </view>
    <BaseModal
      :show.sync="isShowModal"
      @confirm="delBtn"
      content="该设备所有广告位广告将会清空，此操作不可恢复，是否继续清空？"
    />
  </view>
</template>

<script>
import BaseIcon from "@/components/base/BaseIcon.vue";
import BaseCheck from "@/components/base/BaseCheck.vue";
import BaseButton from "@/components/base/BaseButton.vue";
import BaseModal from "@/components/base/BaseModal.vue";
export default {
  name: "DeviceAdvertsCard",
  components: { BaseIcon, BaseCheck, BaseButton, BaseModal },
  props: {
    info: { type: Object, default: {} },
    isShowCheck: { type: Boolean, default: false },
  },

  data() {
    return {
      arrowName: "",
      keyObj: {
        slide: {
          name: "H5轮播广告",
          type: "swiper",
        },
        alert_index: {
          name: "H5首页弹窗",
          type: "img",
        },
        index_suspension: {
          name: "H5首页悬浮",
          type: "img",
        },
        alert_pay: {
          name: "H5付款完成弹窗",
          type: "img",
        },
        pay_push: {
          name: "完成微信推送广告",
          type: "img",
        },
        screen: {
          name: "屏幕轮播广告",
          type: "swiper",
        },
        bottom_index: { name: "屏幕底部广告", type: "img" },
        top_txt: {
          name: "屏幕顶部文字",
          type: "txt",
        },
        wx_list: {
          name: "屏幕公众号",
          type: "wx-swiper",
        },
        send_mp: {
          name: "推送小程序",
          type: "wx-img",
        },
        send_mp_free: {
          name: "免费小程序",
          type: "wx-img",
        },
        send_mp_free_video: {
          name: "视频小程序",
          type: "wx-img",
        },
        send_mp_jump: {
          name: "转跳小程序",
          type: "wx-img",
        },
      },
      isShowModal: false,
    };
  },

  methods: {
    showArrow(e) {
      this.arrowName = this.arrowName === e ? "" : e;
    },
    //箭头是否显示  有数据显示， 无数据隐藏
    isShowArrow(e) {
      if (!e) return false;
      if (Array.isArray(e)) {
        return e?.length > 0 ? true : false;
      } else {
        return true;
      }
    },
    goEdit() {
      if (this.isShowCheck) {
        this.$emit("onCheck");
      } else {
        uni.navigateTo({
          url: `/pagesB/deviceAdverts/DeviceAdvertsAdd?from=edit&device_sn=${this.info.device_sn}&mid=${this.info.id}`,
        });
      }
    },
    //预览图片
    previewImage(urls) {
      uni.previewImage({
        urls: [urls],
      });
    },
    delBtn() {
      let data = {
        mid: this.info.id,
      };
      this.$u.api.resetBind(data).then((res) => {
        this.isShowSuccess("删除成功", 0, () => {
          this.refresh();
        });
      })
      .catch((err=>{
        console.log('错误信息',err)
      }))
    },
  },
};
</script>

<style lang="scss" scoped>
.card {
}
.content {
  display: flex;
  align-items: center;
}
.check {
  margin-left: 10rpx;
}
.container {
  flex: 1;
  &-top {
    display: flex;
    padding: 20rpx;
    border-bottom: 2rpx solid $dividerColor;
    font-size: $font-size-large;
    font-weight: 700;
  }
  &-content {
    padding: 20rpx;
    &-box {
      margin-bottom: 20rpx;
      &:last-child {
        margin-bottom: 0;
      }
      .top {
        display: flex;
        justify-content: space-between;
        font-size: $font-size-middle;

        &-left {
          color: $textDarkGray;
        }
        &-right {
          color: $textBlack;
          display: flex;
          justify-content: space-between;
          align-items: center;
          &-txt {
            color: $themeComColor;
          }
          &-arrow {
            margin-left: 10rpx;
            transition: all 0.4s;
          }
          .rotate {
            transform: rotate(90deg);
          }
        }
      }
      .bottom {
        padding: 20rpx 0;

        .swiper-box {
          width: 100%;
          height: 300rpx;
          .img {
            width: 100%;
            height: 300rpx;
          }
        }
        .img {
          width: 100%;
        }
        .txt {
          text-align: center;
        }
      }
    }
  }
}
.btn {
  display: flex;
  justify-content: flex-end;
  padding: 0 20rpx 20rpx;
}
</style>