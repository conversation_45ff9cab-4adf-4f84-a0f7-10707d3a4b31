<template>
  <view>
    <BaseNavbar title="设备营销设置" />
    <view class="content">
      <view class="content-box">
        <view class="content-box-title">设备编号：</view>
        <BaseInput :disabled="true" v-model="device_sn" />
      </view>
      <!-- <view class="content-box">
        <view class="content-box-title">插屏广告价格：</view>
        <BaseInput
          placeholder="请输入插屏广告价格"
          v-model="insterScreenPrice"
          rightText="元 / 条 * 天"
        />
      </view>
      <view class="content-box">
        <view class="content-box-title">屏幕广告价格：</view>
        <BaseInput
          placeholder="请输入屏幕广告价格"
          v-model="screenPrice"
          rightText="元 / 条 * 天"
        />
      </view> -->
      <view class="content-box">
        <view class="content-box-title">轮播图广告价格：</view>
        <BaseInput
          placeholder="请输入轮播图广告价格"
          v-model="rotationChartPrice"
          rightText="元 / 条 * 天"
        />
      </view>
      <view class="content-box">
        <view class="content-box-title">认养设备价格：</view>
        <BaseInput
          placeholder="请输入认养设备价格"
          v-model="investPrice"
          rightText="元 / 年"
        />
      </view>
      <view class="content-box">
        <view class="content-box-title">认养后订单分成比例：</view>
        <BaseInput
          placeholder="请输入认养设备价格"
          v-model="investShare"
          rightText="%"
        />
      </view>

      <!-- 保存按钮 -->
      <view class="footer">
        <view class="footer__buttom">
          <BaseButton @onClick="save">保存</BaseButton>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import BaseButton from "../../components/base/BaseButton.vue";
import BaseInput from "../../components/base/BaseInput.vue";
import BaseNavbar from "@/components/base/BaseNavbar.vue";
export default {
  components: { BaseNavbar, BaseInput, BaseButton },
  data() {
    return {
      device_sn: "",
      insterScreenPrice: "", //插屏广告价格
      screenPrice: "", //屏幕广告价格
      investPrice: "", //认养设备认养价
      investShare: "", //认养设备分成比例

      rotationChartPrice: "", //轮播图广告价格
    };
  },
  methods: {
    getAdPrice() {
      let data = {
        device_sn: this.device_sn,
      };
      this.$u.api.getAdPrice(data).then((res) => {
        res?.data?.forEach((item) => {
          if (item.type == 4) {
            this.screenPrice = item?.price;
          } else if (item.type == 2) {
            this.insterScreenPrice = item?.price;
          } else if (item.type == 10000) {
            this.investPrice = item?.price;
          } else if (item.type == 20000) {
            this.investShare = item?.price;
          } else if (item.type === 1) {
            this.rotationChartPrice = item?.price;
          }
        });
      })
      .catch((err=>{
        console.log('错误信息',err)
      }))
    },
    save() {
      let type_info = [];
      this.screenPrice && type_info.push({ type: 4, price: this.screenPrice });
      this.insterScreenPrice &&
        type_info.push({ type: 2, price: this.insterScreenPrice });

      this.investPrice &&
        type_info.push({ type: 10000, price: this.investPrice });

      this.investShare &&
        type_info.push({ type: 20000, price: this.investShare });
      this.rotationChartPrice &&
        type_info.push({ type: 1, price: this.rotationChartPrice });
      let data = {
        device_sn: this.device_sn,
        type_info,
      };
      this.$u.api.saveAdPrice(data).then((res) => {
        this.isShowSuccess("保存成功", 1, () => {}, false);
      })
      .catch((err=>{
        console.log('错误信息',err)
      }))
    },
  },
  onLoad(opt) {
    this.device_sn = opt?.device_sn || "";
    this.getAdPrice();
  },
};
</script>

<style lang="scss" scoped>
.content {
  padding: 20rpx 30rpx;
  &-box {
    margin-bottom: 20rpx;
    &-title {
      display: flex;
      align-items: center;
      color: $textBlack;
      font-size: $font-size-middle;
      font-weight: bold;
      margin-bottom: 20rpx;
    }
  }
  .footer {
    margin-top: 100rpx;
  }
}
</style>
