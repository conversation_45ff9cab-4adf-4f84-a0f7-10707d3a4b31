<template>
  <view class="card">
    <view class="content">
      <view class="content-box">
        <view>设备编号：</view>
        <view>{{ info.device_sn }}</view>
      </view>
      <view class="content-box">
        <view>订单编号：</view>
        <view>{{ info.order_sn }}</view>
      </view>
      <view class="content-box">
        <view>购买商品：</view>
        <view>
          <view v-for="(item, index) in info.goods_data" :key="index">
            {{ item.goods_name }} - {{ item.goods_num }} x
            {{ item.goods_price }}
          </view>
        </view>
      </view>
      <view class="content-box">
        <view>订单总价：</view>
        <view>{{ info.amount.toFixed(2) }}</view>
      </view>

      <view class="content-box">
        <view>商品运费：</view>
        <view>{{ info.transportation_cost }}</view>
      </view>

      <view class="content-box">
        <view>采 购 人：</view>
        <view>{{ info.user_login }}</view>
      </view>
      <view class="content-box">
        <view>收货姓名：</view>
        <view>{{ info.receiver_name }}</view>
      </view>
      <view class="content-box">
        <view>收货号码：</view>
        <view>{{ info.receiver_phone }}</view>
      </view>
      <view class="content-box">
        <view>详细地址：</view>
        <view>{{ info.receiver_address }}</view>
      </view>
      <view class="content-box">
        <view>订单状态：</view>
        <view class="order-status">{{ statusData }}</view>
      </view>
      <view class="content-box">
        <view>订单时间：</view>
        <view>{{
          $u.timeFormat(info.time * 1000, "yyyy-mm-dd hh:MM:ss")
        }}</view>
      </view>
      <block v-if="info.status == 2 && info.pay_status == 1">
        <view class="content-box">
          <view>快递名称：</view>
          <view>{{ info.expressName }}</view>
        </view>
        <view class="content-box">
          <view>快递单号：</view>
          <view>{{ info.expressNumber }}</view>
        </view>
      </block>
    </view>
    <view
      class="btn"
      v-if="info.pay_status && (info.status == 0 || info.status == 1)"
    >
      <view class="btn-box">
        <BaseButton
          width="200"
          type="default"
          shape="circle"
          @onClick="confirm"
        >
          {{ btnTitle }}
        </BaseButton>
      </view>
    </view>
    <BaseModal
      :show.sync="isShowModal"
      @confirm="confirmModal"
      title="请填写快递信息"
    >
      <view slot="default">
        <view
          ><BaseInput v-model="expressName" placeholder="请输入快递名称"
        /></view>
        <view class="express-number"
          ><BaseInput v-model="expressNumber" placeholder="请输入快递单号"
        /></view>
      </view>
    </BaseModal>
  </view>
</template>

<script>
import BaseButton from "@/components/base/BaseButton.vue";
import BaseInput from "@/components/base/BaseInput.vue";
import BaseModal from "@/components/base/BaseModal.vue";
export default {
  components: { BaseButton, BaseModal, BaseInput },
  name: "SelectedMallCard",
  props: { info: { type: Object, default: {} } },
  computed: {
    statusData() {
      if (this.info.pay_status == 1) {
        switch (this.info.status) {
          case 0:
            this.btnTitle = "已处理";
            return "待处理";
          case 1:
            this.btnTitle = "发货";
            return "已处理";
          case 2:
            return "已发货";
          default:
            return "未知";
        }
      } else {
        return "未付款";
      }
    },
  },
  data() {
    return {
      isShowModal: false,
      expressName: "", //快递名称
      expressNumber: "", //快递单号
      btnTitle: "已处理",
    };
  },
  methods: {
    confirm() {
      if (this.info.status == 0) {
        this.changeStatus();
      } else {
        this.isShowModal = true;
      }
    },
    changeStatus() {
      let data = { id: this.info.id },
        rtn = null;
      if (this.info.status == 0) {
        rtn = this.$u.api.changeStatusProcessed(data);
      } else if (this.info.status == 1) {
        data["express_name"] = this.expressName;
        data["express_number"] = this.expressNumber;
        rtn = this.$u.api.sendOutGoods(data);
      }
      rtn?.then((res) => {
        this.$emit("confirm", this.btnTitle);
      })
      .catch((err=>{
        console.log('错误信息',err)
      }))
    },
    confirmModal() {
      if (!this.expressName && !this.expressNumber)
        return this.isShowErr("请填写正确的快递信息~");
      this.changeStatus();
    },
  },
};
</script>

<style lang="scss" scoped>
.card {
  position: relative;
}
.content {
  padding: 20rpx;
  &-box {
    display: flex;
    line-height: 1.6;
    .order-status {
      color: $themeComColor !important;
    }
    > view {
      &:first-child {
        text-align: right;
        width: 140rpx;
        color: $textDarkGray;
        white-space: nowrap;
        flex-shrink: 0;
      }
      &:last-child {
        color: $textBlack;
      }
    }
  }
}
.btn {
  position: absolute;
  right: 10rpx;
  bottom: 20rpx;
  display: flex;
  justify-content: flex-end;
  &-box {
  }
}
.express-number {
  margin-top: 20rpx;
}
</style>