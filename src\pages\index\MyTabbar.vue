<template>
  <view>
    <u-tabbar v-model="tabbarCurrent" active-color="#333333" inactive-color="#333333" :list="vTabbars||[]" :mid-button="true"
      height="110" @change="change"></u-tabbar>
    <BasePopup :show.sync="isShowPopup" mode="center" width="690" radius="20">
      <view class="sweep">
        <!-- banner -->
        <view class="banner">
          <image  class="image" src="@/static/img/sweep-banner.png" />
          <view @click.stop="isShowPopup = false" class="closeBanner">
            <!-- <img src="" style="height: 56rpx; width: 56rpx" alt /> -->
            <BaseIcon name="close" color="#fff" size="30" />
          </view>
        </view>
        <view class="content">
          <!-- relative -->
          <view class="logo">
            <image  class="image" style="height: 172rpx; width: 172rpx;" :src="vSiteConfig.site_info.site_logo" alt />
          </view>
          <view class="operationContent" v-if="isSuccess">
            <view class="operation">
              <view v-for="(item, index) in sweepFunc" :key="index" class="operation_item deviceDetails" @click="toModule(item.url)">
                <image class="image" v-if="index == 1" src="@/static/img/index_scan/goodsSet.png" alt
                  style="height: 75rpx; width: 75rpx;" @click.stop="toModule(item.url)" />
                <image  class="image" v-else style="height: 75rpx; width: 75rpx;" :src="item.src" alt />
                <view class="item_sm">{{ item.name }}</view>
              </view>
            </view>
          </view>
          <view v-else>
            <view class="fail" :class="!isSuccess ? 'fail' : ''">
              <view class="failDesc">请扫描正确的二维码</view>
              <view class="reSweep" @click="sweepCode">重新扫描</view>
            </view>
          </view>
        </view>
      </view>
    </BasePopup>
  </view>
</template>

<script>
import { getUrlParams, getUrlDynamicData } from '@/common/tools/utils.js'
import BasePopup from '../../components/base/BasePopup.vue'
import BaseIcon from '../../components/base/BaseIcon.vue'
/* #ifdef H5 */
import wx from 'weixin-js-sdk'
/* #endif */
export default {
  components: { BasePopup, BaseIcon },
  data() {
    return {
      mid: '',
      vscode: '',
      device_sn: '',
      /* #ifndef H5 */
      sweepFunc: [],
      isShowPopup: false,
      isSuccess: false,
      /* #endif */
      tabbarCurrent: "",
      /* #ifdef H5 */
      codeUrl: '',
      cameraId: '',
      /* #endif */

    }
  },
  /* #ifdef H5 */
  props: {
    isShowPopup: {
      type: Boolean, //按钮宽度
      default: false,
    },
    isSuccess: {
      type: Boolean, //按钮宽度
      default: false,
    },
    sweepFunc: {
      type: Array,
      default: function () {
        return []; // 使用工厂函数返回一个新的对象
      },
    }

  },
  /* #endif */
  methods: {
    change(e) {
      if (e == 1) {
        //点击了扫码
        /*  #ifndef H5 */
        this.sweepCode()
        /*#endif */
        /*  #ifdef H5 */
        // uni.navigateTo({
        //   url: `/pagesD/Scan/Scan`,
        // })
        if (typeof AlipayJSBridge != 'undefined') {
          AlipayJSBridge.call(
            'scan',
            {
              type: 'qr', // 扫描类型  qr 二维码  / bar 条形码
              // actionType: "scanAndRoute",// 如果只是扫码,拿到码中的内容，这项不用设置都可以
            },
            (res) => {
              // alert(JSON.stringify(res));
              if (res.error == 10) {
                // 错误码为10：用户取消操作
                Toast('取消操作')
              } else if (res.error == 11) {
                // 错误码为11：扫码失败
                Toast('网络异常，请重试')
              } else {
                // res.codeContent为扫码返回的结果
                // window.location.replace(res.codeContent)
                let result = decodeURIComponent(res.codeContent)
                uni.$emit('tabSwitch', { result: result })
              }
            },
          )
        } else if (typeof WeixinJSBridge != 'undefined') {
          let uri = location.href.split('#')[0]
          // let uri = this.vUrl
          let data = {
            url: uri,
          }
          this.$u.api.getJsSign(data)
            .then((res) => {
              wx.config({
                debug: false, // 开启调试模式,调用的所有api的返回值会在客户端alert出来，若要查看传入的参数，可以在pc端打开，参数信息会通过log打出，仅在pc端时才会打印。
                appId: res.appId, // 必填，公众号的唯一标识
                timestamp: res.timestamp, // 必填，生成签名的时间戳
                nonceStr: res.nonceStr, // 必填，生成签名的随机串
                signature: res.signature, // 必填，签名
                jsApiList: ['scanQRCode'], // 必填，需要使用的JS接口列表, 这里只需要调用扫一扫
              })
              wx.ready(function () {
                wx.scanQRCode({
                  needResult: 1, // 默认为0，扫描结果由微信处理，1则直接返回扫描结果，
                  scanType: ['qrCode', 'barCode'], // 可以指定扫二维码还是一维码，默认二者都有
                  success: function (res) {
                    // 扫码成功，跳转到二维码指定页面（res.resultStr为扫码返回的结果）
                    // window.location.replace(res.resultStr);
                    setTimeout(async () => {
                      // window.location.replace(res.resultStr) 
                      let result = decodeURIComponent(res.resultStr)
                      uni.$emit('tabSwitch', { result: result })
                    }, 20)
                  },
                })
              })
            })
            .catch((err) => {
              console.log('错误结果', err)
            })
        } else {
          // uni.navigateTo({
          //   url: `/pagesD/Scan/Scan`,
          // })
          alert('请使用微信打浏览器打开')
        }
        /*#endif */
      }
    },
    sweepCode() {
      uni.scanCode({
        onlyFromCamera: false,
        scanType: ['qrCode', 'barCode'],
        success: async ({ result, scanType, charSet, path }) => {
          result = decodeURIComponent(result)
          if (!result) return
          if (result.includes('vscode')) {
            this.vscode = getUrlParams(result, 'vscode')
            // 小程序扫码蓝牙码
            let data = {
              vscode: result.split('=')[1],
            }
            let rtn = await this.$u.api.getUMVscode(data)
            this.mid = rtn?.id
          } else if (result.includes('mid')) {
            this.mid = getUrlDynamicData(result, 'mid')
          } else if (result.includes('coupon/index/id')) {
            //跳转到优惠券核销界面
            //https://towel.wrlsw.com/response/coupon/index/id/1
            const id = getUrlDynamicData(result, 'id')
            if (!id) return this.isShowErr('核销二维码格式错误~')
            uni.navigateTo({ url: `/pagesB/coupon/CouponCheck?id=${id}` })
            return
          } else {
            if (result.includes('device_sn')) {
              this.device_sn = getUrlDynamicData(result, 'device_sn')
            } else {
              this.device_sn = result
            }
            let data = {
              device_sn: this.device_sn,
            }
            let rtn = await this.$u.api.getUMByDeviceSn(data)
            this.mid = rtn?.id
          }
          if (this.mid > 0) {
            this.sweepFunc = []
            let param = '?from=index_scan&mid=' + this.mid
            if (this.vUserInfo.role_id >= 1 && this.vButtonPermisFour) {
              this.sweepFunc.push({
                name: '绑定' + this.vPointName,
                src: './../../static/img/index_scan/bind.png',
                url: '/pagesB/place/BindPlace' + param,
              })

              this.sweepFunc.push({
                name: '设备操作',
                src: './../../static/img/index_scan/goodsSet.png',
                url:
                  '/pagesB/device/DeviceGoodsList' +
                  param +
                  '&isFrom=replenish',
              })
              // this.sweepFunc.push({
              //   name: "设备商品",
              //   src: "./../../static/img/index_scan/deviceDetails.png",
              //   url:
              //     "/pagesB/device/DeviceGoodsList" +
              //     param +
              //     "&isFrom=device_goods",
              // });

              this.sweepFunc.push({
                name: '领取设备',
                src: './../../static/img/index_scan/finance.png',
                url: '/pagesB/device/DeviceActivate' + param,
              })

              this.sweepFunc.push({
                name: '分配设备',
                src: './../../static/img/index_scan/deviceDetails.png',
                url: '/pagesB/device/DeviceAllot' + param,
              })
              // this.sweepFunc.push({
              //   name: "遥控设备",
              //   src: "./../../static/img/index_scan/remote.png",
              //   url: "/pagesB/remote/RemoteEquipment" + param,
              // });
              // this.sweepFunc.push({
              //   name: "关联设备",
              //   src: "./../../static/img/index_scan/relevance-equipment.png",
              //   url: "/pagesB/relevance/RelevanceEquipment" + param,
              // });
            } else if (this.vUserInfo.role_id == 5) {
              this.sweepFunc.push({
                name: '绑定' + this.vPointName,
                src: './../../static/img/index_scan/bind.png',
                url: '/pagesB/place/BindPlace' + param,
              })

              this.sweepFunc.push({
                name: '设备操作',
                src: './../../static/img/index_scan/goodsSet.png',
                url:
                  '/pagesB/device/DeviceGoodsList' +
                  param +
                  '&isFrom=replenish',
              })

              this.sweepFunc.push({
                name: '领取设备',
                src: './../../static/img/index_scan/finance.png',
                url: '/pagesB/device/DeviceActivate' + param,
              })
            } else if (this.vUserInfo.role_id == 7) {
              // this.sweepFunc.push({
              //   name: "绑定" + this.vPointName,
              //   src: "./../../static/img/index_scan/bind.png",
              //   url: "/pagesB/place/BindPlace" + param,
              // });
              this.sweepFunc.push({
                name: '领取设备',
                src: './../../static/img/index_scan/finance.png',
                url: '/pagesB/device/DeviceActivate' + param,
              })
              this.sweepFunc.push({
                name: '设备操作',
                src: './../../static/img/index_scan/goodsSet.png',
                url:
                  '/pagesB/device/DeviceGoodsList' +
                  param +
                  '&isFrom=replenish',
              })

              // this.sweepFunc.push({
              //   name: "遥控设备",
              //   src: "./../../static/img/index_scan/remote.png",
              //   url: "/pagesB/remote/RemoteEquipment" + param,
              // });
            } else {
              this.sweepFunc.push({
                name: '设备操作',
                src: './../../static/img/index_scan/goodsSet.png',
                url:
                  '/pagesB/device/DeviceGoodsList' +
                  param +
                  '&isFrom=replenish',
              })
              // 直接补货界面
              uni.navigateTo({
                url:
                  '/pagesB/device/DeviceGoodsList' +
                  param +
                  '&isFrom=replenish',
              })
            }
            this.isShowPopup = true
            this.isSuccess = true
          } else {
            this.isShowErr('请扫码正确的二维码,二维码信息' + result, 1)
          }
        },
        fail: (error) => { },
      })
    },
    toModule(url) {
      uni.navigateTo({
        url,
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.sweep {
  .banner {
    position: relative;

    .image {
      width: 100%;
      height: 183rpx;
    }

    .closeBanner {
      position: absolute;
      top: 16rpx;
      right: 16rpx;
    }
  }

  .content {
    position: relative;
    width: 100%;
    height: 450rpx;
    background-color: white;

    .logo {
      position: absolute;
      left: 261rpx;
      top: -95rpx;

      .image {
        width: 172rpx;
        height: 172rpx;
        border-radius: 172rpx;
      }
    }

    .operationContent {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .operation {
        width: 100%;
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-wrap: wrap;
        top: 155rpx;
        padding: 155rpx 30rpx 0;
        transition: all 1s ease-in-out;

        .operation_item {
          width: 24.5%;
          display: flex;
          justify-content: space-between;
          flex-direction: column;
          align-items: center;
          font-size: 30rpx;
          margin-bottom: 10rpx;
          .item_sm {
            margin-top: 26rpx;
          }
        }
      }
    }

    .fail {
      display: flex;
      justify-content: center;
      align-items: center;
      flex-direction: column;
      padding-top: 166rpx;

      .failDesc {
        font-size: 36rpx;
        color: $themeComColor;
      }

      .reSweep {
        text-decoration: underline;
        color: rgba(40, 40, 40, 1);
        font-size: 30rpx;
      }
    }
  }
}

::v-deep .u-border {
  left: 50% !important;
}
</style>
