<template>
  <view class="card">
    <view class="container">
      <view class="content">
        <view class="content-left">
          <view class="title">订单编号：</view>
          <view class="txt">{{ info.order_sn }}</view>
        </view>
        <view class="content-right">
          <view class="status">{{ orderSatua }}</view>
        </view>
      </view>
      <view class="content">
        <view class="content-left">
          <view class="title">续费设备：</view>
          <view class="txt">{{ info.device_sn }}</view>
        </view>
      </view>
      <view class="content">
        <view class="content-left">
          <view class="title">续费金额：</view>
          <view class="txt">{{ info.amount }}</view>
        </view>
      </view>

      <view class="content">
        <view class="content-left">
          <view class="title">拥 有 者 ：</view>
          <view class="txt">{{ info.user_login }}</view>
        </view>
      </view>
      <view class="content">
        <view class="content-left">
          <view class="title">订单时间：</view>
          <view class="txt">{{
            $u.timeFormat(info.add_time * 1000, "yy-mm-dd hh:MM:ss")
          }}</view>
        </view>
      </view>
      <view class="content">
        <view class="content-left">
          <view class="title">续费时长：</view>
          <view class="txt">{{ info.year }}年</view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: "RenewRecordCard",
  props: {
    info: { type: Object, default: {} },
  },
  computed: {
    orderSatua() {
      let oStatus = this.info.order_status;
      let pStatus = this.info.pay_status;

      if (pStatus == 0) {
        return "待付款";
      } else if (pStatus == 1) {
        switch (oStatus) {
          case 1:
            return "订单完成";
            break;
          case 2:
            return "订单异常";
            break;
          case 3:
            return "取消订单";
            break;
        }
      } else {
        return "订单异常";
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.card {
  padding: 20rpx;
}
.container {
  .content {
    display: flex;
    justify-content: space-between;
    margin-bottom: 16rpx;
    &:last-child {
      margin-bottom: 0;
    }
    &-left {
      display: flex;
      font-size: $font-size-middle;

      .title {
        color: $textDarkGray;
        flex-shrink: 0;
      }
      .txt {
        color: $textBlack;
        flex: 1;
        word-break: break-all;
      }
    }
    &-right {
      .status {
        color: $themeComColor;
      }
    }
  }
}
</style>