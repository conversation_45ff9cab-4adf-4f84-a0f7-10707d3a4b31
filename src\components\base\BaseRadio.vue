<template>
  <view class="base-radio">
    <!-- <u-radio-group
      v-model="newValue"
      @change="radioGroupChange"
      :active-color="activeColor"
      :width="width + '%'"
    >
      <u-radio
        v-for="(item, index) in list"
        :key="index"
        :name="item.name"
        :disabled="item.disabled"
        :labelSize="size"
      >
        <view class="rideoCss">{{ item.title }}</view>
      </u-radio>
    </u-radio-group> -->
    <radio-group @change="radioGroupChange">
      <view class="uni-rideo">
        <label
          class="uni-rideo-item"
          v-for="(item, index) in list"
          :key="item.title"
          :style="{ width: `${width}%` }"
        >
          <view>
            <radio
              :value="index.toString()"
              :checked="index === radioIndex"
              :color="activeColor"
              :style="{ transform: `scale(${size / 38})` }"
            />
          </view>
          <view>{{ item.title }}</view>
        </label>
      </view>
    </radio-group>
  </view>
</template>

<script>
export default {
  name: 'BaseRadio',
  props: {
    radioIndex: { type: [Number, String], default: 0 }, //选中索引 双向绑定
    list: {
      //name就是需要绑定的值
      //radio数组
      /** 
       * {
          title: "是",
          name: "0",
          disabled: false,
          selectIndex: 1,
        }
       */
      type: Array,
      default: function () {
        return []
      },
    },
    width: {
      type: [String, Number],
      default: '50',
    },
    size: {
      type: [String, Number],
      default: '28',
    },
  },
  computed: {
    defCsss() {
      return { fontSize: `${this.size}rpx`, width: `${this.width}%` }
    },
  },
  data() {
    return {
      activeColor: '#0eade2',
      //   list: [
      //     {
      //       title: "apple",
      //       name: "0",
      //       disabled: false,
      //     },
      //     { title: "banner", name: "1", disabled: false },
      //     { title: "orange", name: "2", disabled: false },
      //   ],
    }
  },
  methods: {
    // 选中任一radio时，由radio-group触发
    radioGroupChange(e) {
      let index = e.detail.value * 1
      console.log('切换',index)
      this.$emit('changes', index)
      this.$emit('update:radioIndex', index)
    },
  },
}
</script>

<style lang="scss" scoped>
.base-radio {
  .uni-rideo {
    display: flex;
    width: 100%;
    flex-wrap:wrap;
    .uni-rideo-item {
      display: flex;
      align-items: center;
    }
  }
}
</style>
