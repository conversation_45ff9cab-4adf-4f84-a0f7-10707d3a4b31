<template>
    <view class="memberList">
        <view class="allSelect_box">
            <checkbox-group @click="selectAll">
                <label class="uni-list-cell uni-list-cell-pd">
                    <view>
                        <checkbox class="checkbox" :checked="selectilall" />
                        <text class="allSelect">全选</text>
                    </view>
                </label>
            </checkbox-group>
        </view>
        <view class="uni-list">
            <checkbox-group @change="select">
                <label class="uni-list-cell uni-list-cell-pd" v-for="item in list" :key="index">
                    <view>
                        <checkbox :value="item.id" :checked="item.checked" />
                    </view>
                    <view>{{ item.device_sn }}</view>
                </label>
            </checkbox-group>
        </view>
    </view>
</template>
  
<script>

export default {
    name: "Checkbox",
    props: {
        data: {
            //上拉的状态：0-loading前；1-loading中；2-没有更多了
            type: Array,
            default: function () {
                return [];
            }
        },

    },
    data() {
        return {
            selectilall: false,
            list: []
        }
    },
    methods: {
        select(e) {
            const items = this.data
            const values = e.detail.value
            let num = 0
            for (let i = 0, lenI = items.length; i < lenI; i++) {
                this.$set(items[i], 'checked', '')

                for (let j = 0, lenJ = values.length; j < lenJ; j++) {
                    if (items[i].id == values[j]) {
                        this.$set(items[i], 'checked', true)
                        num++
                        break
                    }
                }
            }
            if (num == items.length) {
                this.selectilall = true
            } else {
                this.selectilall = false
            }
        },
        //全选，取消全选
        selectAll(e) {
            let items = this.data;
            let selectilall = this.selectilall;

            if (selectilall == false) {
                this.selectilall = true
                for (var i = 0, lenI = items.length; i < lenI; ++i) {
                    this.$set(items[i], 'checked', true)

                }

            } else {
                this.selectilall = false
                for (var i = 0, lenI = items.length; i < lenI; ++i) {
                    this.$set(items[i], 'checked', '')

                }

            }
        },

    },
    watch: {
        data: {
            handler(newVal, oldVal) {
                this.list = this.data
                let succuseList = this.data.filter(item => item.checked == true)
                if (succuseList.length == this.data.length) {
                    this.selectilall = true
                } else {
                    this.selectilall = false
                }
                this.$emit("checkedata", succuseList);
            },
            immediate: true,
            deep: true
        },

    },
    computed: {

    },


}

</script>
  
<style lang="scss" scoped>
.uni-list-cell {
    margin: 10rpx 0;
    display: flex;
}

.uni-list {
    height: 600rpx;
    overflow-y: scroll;
}
</style>