<template>
  <view>
    <BaseNavbar :title="title" />

    <view class="content">
      <view>
        <view class="title">账号</view>
        <BaseInput placeholder="请输入账号" v-model="user_login" />
      </view>
      <view>
        <view class="title">密码</view>
        <BaseInput placeholder="请输入密码" v-model="user_pass" />
      </view>
      <view>
        <view class="title">姓名</view>
        <BaseInput placeholder="请输入姓名" v-model="user_nickname" />
      </view>
      <view>
        <view class="title">联系人电话</view>
        <BaseInput
          placeholder="请输入联系人电话"
          type="number"
          v-model="mobile"
        />
      </view>
      <view v-if="isFromAdd">
        <view class="title">选择角色</view>
        <BaseInput v-if="roleValue.label"
          :selectValue="roleValue.label"
          :index="roleValue.value"
          placeholder="请选择角色"
          type="select"
          :list="roleList"
          @confirm="roleConfirm"
        />
      </view>
      <view v-if="vphone">
        <view class="title">绑定{{ vPointName }}账号</view>
        <view class="vpointName">
          <view class="vpointName_zhe" @click="selectplace"></view>
          <BaseInput
            :disabled="true"
            v-model="hotelName"
            placeholder="请选择绑定账号"
          />
          <view class="click">
            <u-icon name="arrow-right" color="#2979ff" size="38"></u-icon>
          </view>
        </view>
      </view>
      <view v-if="Divide">
        <view class="title">设置分成比例：</view>
        <BaseInput placeholder="请输入分成比例" v-model="per" rightText="%" />
      </view>
      <view v-if="Divide">
        <view class="title">输入邀请码：</view>
        <BaseInput placeholder="请输入邀请码" v-model="code" />
      </view>
      <view v-if="false">
        <view class="title">是否允许添加商品</view>
        <view class="is-add-goods">
          <BaseRadio :radioIndex.sync="radioIndex" :list="radioList" />
        </view>
      </view>
      <view class="btn">
        <view>
          <BaseButton type="primary" @onClick="confirmAdd">确 认</BaseButton>
        </view>
        <!-- <view class="btn_del" v-if="isFromEdit">
          <BaseButton type="default" @onClick="confirm">删 除</BaseButton>
        </view> -->
      </view>
    </view>
  </view>
</template>

<script>
import BaseButton from '@/components/base/BaseButton.vue'
import BaseCheck from '@/components/base/BaseCheck.vue'
import BaseInput from '@/components/base/BaseInput.vue'
import BaseNavbar from '@/components/base/BaseNavbar.vue'
import BaseRadio from '@/components/base/BaseRadio.vue'
export default {
  components: {
    BaseNavbar,
    BaseButton,
    BaseInput,
    BaseCheck,
    BaseRadio,
  },
  data() {
    return {
      title: '新增用户',
      isFromEdit: false,
      isFromAdd: false,
      roleList: [],
      roleValue:{},
      checked: false,
      hotelName: '',
      radioIndex: 0,
      per: 0,
      code: '',
      hotel_id: '',
      radioList: [
        {
          title: '是',
          name: '0',
          disabled: false,
          selectIndex: 1,
        },
        { title: '否', name: '1', disabled: false, selectIndex: 0 },
      ],
      user_login: '',
      user_pass: '',
      user_nickname: '',
      mobile: '',
      id: '',
    }
  },
  methods: {
    roleConfirm(e) {
      if(this.roleList[e[0].value]){
        this.roleValue = this.roleList[e[0].value]||{}
      }
    },
    confirmAdd() {
      let data = {
          is_allow_add_goods: this.radioList[this.radioIndex].selectIndex,
          mobile: this.mobile,
          user_login: this.user_login,
          user_nickname: this.user_nickname,
          user_pass: this.user_pass,
          hotel_id: this.hotel_id,
          per: this.per,
          code: this.code,
        },
        rtnData = null
      if (this.isFromAdd) {
        data['role_id'] = this.roleValue.roleId
        rtnData = this.$u.api.addUser(data)
      } else if (this.isFromEdit) {
        data['id'] = this.id
        rtnData = this.$u.api.editUser(data)
      }
      rtnData.then((res) => {
        this.isShowSuccess('操作成功', 1, () => {}, true)
      })
    },
    confirmDelet() {},
    //获取角色列表
    getRoleList() {
      let data = {
        id: 0,
      }
      this.$u.api.getRoleAllList(data).then((res) => {
        if (res.length > 0) {
          let i = 0
          res.map((item) => {
            if (item.id > this.vUserInfo.role_id) {
              i++
              return this.roleList.push({
                value: i - 1,
                label: item.role_name,
                roleId: item.id,
              })
            }
          })
          this.roleValue = this.roleList[0]
        }
      })
    },
    //获取用户详情
    getUserDetail(id) {
      let data = {
        id,
      }
      this.$u.api.getUserDetail(data).then((res) => {
        this.user_login = res.user_login
        this.mobile = res.mobile
        this.user_nickname = res.user_nickname
        //radioIndex是选中项所在位置  item.selectIndex才是判断是否可添加商品
        this.radioList.forEach((item) => {
          if (item.selectIndex == res.is_allow_add_goods) {
            this.radioIndex = item.name
          }
        })
      })
    },
    /* 修改场地方 */
    selectplace() {
      uni.navigateTo({
        url: `/pagesB/place/SelectPlace?from=home_device_bind_place`,
      })
    },
  },
  computed: {
    // newChecked: {
    //   // getter
    //   get: function () {
    //     return this.checked;
    //   },
    //   // setter
    //   set: function (val) {
    //     this.$emit("update:checked", val);
    //   },
    // },
    vphone() {
      if (this.roleValue) {
        return this.roleValue.roleId == 8
      } else {
        return false
      }
    },
    Divide() {
      if (this.roleValue) {
        return this.roleValue.roleId == 4
      } else {
        return false
      }
    },
  },
  onLoad(opt) {
    this.roleValue = this.roleList[0]||{} //角色默认选择第一个
    if (opt?.from) {
      this.fromData = opt.from
      if (this.fromData == 'edit') {
        this.title = '编辑用户'
        this.isFromEdit = true
        this.id = opt.id
        this.getUserDetail(opt.id)
      } else if ((this.fromData = 'add')) {
        this.title = '新增用户'
        this.isFromAdd = true
        this.getRoleList()
      }
    }
  },
  onShow(e) {
    /*  #ifndef H5 */
    let pages = getCurrentPages()
    let currPage = pages[pages.length - 1] // 当前页
    if (currPage.data.item) {
      // 有值
      // 修改listData中值
      this.checkHotel = currPage.data.item
      this.hotelName = this.checkHotel.hotelName
      this.hotel_id = this.checkHotel.id
    }
    /*#endif */
    /*  #ifdef H5 */
    // 有值
    // 修改listData中值
    this.checkHotel = this.vCurrPage.item
    this.hotelName = this.checkHotel.hotelName
    this.hotel_id = this.checkHotel.id
    /*#endif */
  },
}
</script>

<style lang="scss" scoped>
.content {
  padding: 30rpx;

  > view {
    margin-bottom: 50rpx;
  }

  .vpointName {
    // border: 1px solid #000;
    position: relative;
    .vpointName_zhe {
      width: 100%;
      height: 100%;
      // border: 1px solid #000;
      position: absolute;
      left: 0;
      top: 0;
      z-index: 999;
    }
  }

  .click {
    font-size: 25rpx;
    color: $font-size-middle;
    width: 52rpx;
    text-align: center;
    display: flex;
    align-items: center;
    height: 80rpx;
    // border: 1px solid #000;
    position: absolute;
    right: 0;
    top: 0;
    z-index: 666;
  }

  .title {
    color: $textBlack;
    font-size: $font-size-middle;
    font-weight: bold;
    margin-bottom: 20rpx;
  }

  .btn {
    margin-top: 100rpx;

    &_del {
      margin-top: 40rpx;
    }
  }

  .is-add-goods {
    margin-left: 20%;
  }

  .hotelName {
    display: flex;
    flex-direction: row;
    align-content: center;
    align-items: center;
    border-bottom: 1rpx solid #e5e5e5;
  }
}
</style>
