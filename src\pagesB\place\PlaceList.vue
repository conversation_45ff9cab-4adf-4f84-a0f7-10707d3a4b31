<template>
  <view>
    <BaseNavbar :title="title" />

    <BaseSearch :placeholder="'请输入' + vPointName + '名称'" @search="search" listType="placeList" />
    <BaseDropdown :options-list="optionsList" @change="change" :num="total" />
    <!-- <BaseList listType="placeList" @searchChange="searchChange" /> -->
    <ComList :loading-type="loadingType">
      <PlaceListCard v-for="item in listData" :key="item.id" :info="item" @goodsTemp="goodsTemp(item.id)"
        @modify="modify(item)" @deviceList="deviceList(item)" @saleGoods="saleGoods(item.id)" @click="goEdit(item)"
        @chargeTemp="chargeTemp(item)" @ckLotter="ckLotter(item)" @more="more(item)" @autoOnOff="autoOnOff(item)"
        @BulkManagement="BulkManagement(item)" />
    </ComList>
    <FixedAddIcon @onAdd="goGoodsAdd" />
    <BaseModal :show.sync="isShowModal" @confirm="confirm">
      <view class="input-percentage" slot="default">
        <BaseInput v-model="percentage" placeholder="请输入比例数值" rightText="%" />
      </view>
    </BaseModal>

    <BaseModal :show.sync="isShowModalChargeTemp" confirmText="保存并应用到设备" cancelText="保存" :mask="true"
      @cancel="confirmChargeTemp(false)" @confirm="confirmChargeTemp(true)">
      <view class="charge">
        <view class="charge-info">
          <view class="charge-rule-money">
            <BaseInput placeholder="请输入金额" v-model="chargeRuleInfo.rule_money" rightText="元/小时" />
          </view>
          <!-- <view class="line">/小时</view> -->
          <!-- <view class="charge-rule-time">
            <BaseInput
              placeholder="请输入时间"
              v-model="chargeRuleInfo.rule_time"
              :rightText="chargeRuleInfo.rule_unit === 2 ? '小时' : '分钟'"
            />
          </view> -->
        </view>
        <!-- <view @click="showSheet = true" class="select-rule">
          选择时间规则
        </view> -->
      </view>
    </BaseModal>
    <!-- 开启免费弹窗 -->
    <BaseModal :show.sync="isShowDelModalFree" @confirm="confirmDelFree" title="温馨提示">
      <view class="modal" :class="{ red: index == 0 || index == 4 }">
        {{ PopUps[index] }}
      </view>
    </BaseModal>
    <!-- 删除场地弹窗 -->
    <BaseModal :show.sync="isShowDelModalMove" @confirm="confirmDelMove" title="温馨提示">
      <view class="modal red">
        您确定要删除{{ vPointName }}吗?
      </view>
    </BaseModal>
    <!-- 开启自动待机弹窗 -->
    <!-- <BaseModal :show.sync="isShowDelModalOnOff" @confirm="confirmDelOnOff" :content="PopUps[index]" title="温馨提示" /> -->
    <BaseModal :show.sync="isShowDelModalOnOff" :content="PopUps[index]" @confirm="confirmDelOnOff" title="温馨提示">
    </BaseModal>
    <!-- 进度条处理 -->
    <BasePopup :show.sync="isShowProgress" :maskClose="false" mode="center" height="400" :closeable="cancel" width="690"
      radius="20">
      <view class="cent_progres">
        <view class="center_pro_title">{{ titles }}进度</view>
        <view class="center_pro_top">
          <view>总设备数：{{ total }}</view>
          <view>已完成：{{ finishNum }}</view>
          <view v-if="!ok">正在操作：{{ device_sn }}</view>
        </view>
        <view class="center_pro">
          <u-line-progress active-color="#2979ff" :percent="ProgressNum"></u-line-progress>
        </view>
        <view class="center_pro_bottom" v-if="ok">
          <text>总数量: {{ total }}</text>
          <text>已完成: {{ finishNum }}</text>
          <text>未完成: {{ total - finishNum }}</text>
          <text>失败: {{ failNum }}</text>
        </view>
        <view class="center_pro_btn">
          <BaseButton type="primary" @onClick="timeOt" :disapble="timeOut">
            取消
          </BaseButton>
        </view>
      </view>
    </BasePopup>
    <BaseModal :show.sync="isShowNameModal" :content="'您的设备将与' + vPointName + '解除绑定，是否继续解绑？'"
      @confirm="confirmUnBind" />
    <!-- <BaseModal
      :show.sync="isShowDelModalckLotter"
      @confirm="confirmDelckLotter"
      content="确认要开启随机任务吗"
      title="温馨提示"
    /> -->
    <u-action-sheet :list="sheetList" v-model="showSheet" :safe-area-inset-bottom="true" :cancel-btn="false"
      @click="selectRuleUnit"></u-action-sheet>
    <BasePopup :show.sync="showTaskType" :maskClose="true" mode="center" height="400" :closeable="true" width="690"
      radius="20">
      <view class="pup_box">
        <!-- <view>
          <button class="default" @click.stop="free()">
            {{ selectItem.is_all_free ? '关闭免费' : '开启免费' }}
          </button>
        </view> -->
        <view v-for="(item, i) in taskTypeList" :key="i">
          <button class="default" @click.stop="comfirmType(item)">
            {{ item }}
          </button>
        </view>
        <view v-if="vButtonPermissions && vButtonPermisAides">
          <button class="default" @click.stop="move()">
            删除{{ vPointName }}
          </button>
        </view>
      </view>
    </BasePopup>
    <BaseBackTop @onPageScroll="onPageScroll" :scrollTop="scrollTop">
    </BaseBackTop>
  </view>
</template>

<script>
import BaseNavbar from '@/components/base/BaseNavbar.vue'
import BaseSearch from '@/components/base/BaseSearch.vue'
import ComList from '@/components/list/ComList.vue'
import myPull from '@/mixins/myPull.js'
import PlaceListCard from '../components/cards/PlaceListCard.vue'
import BasePopup from '../../components/base/BasePopup.vue'
import BaseModal from '@/components/base/BaseModal.vue'
import BaseInput from '@/components/base/BaseInput.vue'
import FixedAddIcon from '@/components/common/FixedAddIcon.vue'
// import Checkout from '@/components/checkbox/Checkbox.vue'
import BaseButton from '@/components/base/BaseButton.vue'
// import BaseList from "@/components/base/BaseList.vue"
import { AddValueInObject } from '@/wxutil/list'
import BaseDropdown from '@/components/base/BaseDropdown.vue'
import BaseBackTop from '@/components/base/BaseBackTop.vue'
export default {
  components: {
    BaseNavbar,
    BaseSearch,
    ComList,
    PlaceListCard,
    BasePopup,
    BaseModal,
    BaseInput,
    FixedAddIcon,
    // Checkout,
    BaseButton,
    // BaseList,
    BaseDropdown,
    BaseBackTop
  },
  data() {
    BaseInput
    return {
      title: `${this.vPointName}管理`,
      isShowModal: false,
      percentage: '', //分成比例
      hotel_name: '', //点位名称
      owner_login: '', //账户
      user_login: '', //拥有者
      selectItem: {}, //当前选择的item
      WarterNum: 0, //补水量
      isFromAdSelectPlace: false, //是否是广告编辑 选择点位页面过来的？
      isShowModalChargeTemp: false, //充电模板弹窗
      isShowDelModalFree: false, //免费弹窗
      isShowDelModalMove: false, //删除场地方弹窗
      isShowDelModalOnOff: false, //开待机弹窗
      isShowDelModalckLotter: false, //随机任务弹窗
      isShowcheckout: false, //批量添加水量弹窗
      isShowWaterModal: false, //添加水量弹窗
      // isShowPopup: false, //批量管理
      isShowStartModal: false, //批量启动
      isShowMusicModal: false, //是否显示输入音乐时长
      isShowLightModal: false, // 是否关灯
      isShowMusicStopModal: false, //关闭提示音
      isShowNameModal: false, //接触绑定
      isShowProgress: false, //进度条
      adjust_time: 1, //播放时
      length_time: 1, //启动时常
      total: 0, //设备总数
      finishNum: 0, //已经完成的设备数
      device_sn: '', //正在操作的设备编号
      cancel: false, //控制取消按钮显示
      failNum: 0, //失败的数
      ProgressNum: 0, //进度
      timeOut: false, //暂停
      ok: false, //完成
      chargeRuleInfo: {},
      hotel_id: '',
      titles: '批量启动',
      // items: {},
      dataList: [],
      sheetList: [
        // {
        //   text: "分钟",
        //   color: "#333",
        //   fontSize: 28,
        //   rule_unit: 1,
        // },
        {
          text: '小时',
          color: '#333',
          fontSize: 28,
          rule_unit: 2,
        },
      ],
      showSheet: false,
      PopUps: {
        0: '您确定要开启免费功能吗',
        1: '您确定要关闭免费功能吗',
        2: '您确定要开启自动待机功能吗',
        3: '您确定要关闭自动待机功能吗',
      },
      index: 0,
      id: 0,
      CheckoutList: [],
      checkoutDatalist: [],
      showTaskType: false, //更多弹窗
      taskTypeList: ['销量统计'],
      hotelName: "",
      optionsList: [
        {
          title: '全部',
          options: [
            { label: '全部', value: 0, status: 0 },
            {
              label: '我的',
              value: 1,
              status: 1,
            },
            {
              label: '下级',
              value: 2,
              status: 2,
            },
          ],
          value: 0,
        },
      ],
      range: 0,
      total: 0,
      scrollTop: 0
    }
  },
  methods: {
    onPageScroll(e) {
      this.scrollTop = e.scrollTop;
    },
    change(item) {
      this.optionsList = item
      let index = item[0].value
      this.range = item[0].options[index].status
      // console.log("🚀 ~ this.status", this.status);
      this.refresh()
    },
    /* 更多选项 */
    comfirmType(item) {
      if (item == '销量统计') {
        this.showTaskType = false
        uni.navigateTo({
          url: `/pagesB/dataAnalysis/DataAnalysis?from=placeList&hotelName=${this.selectItem.hotelName}&hotel_id=${this.selectItem.id}`,
        })
      }else if(item=='电子围栏'){
        this.showTaskType = false
        uni.navigateTo({
          url: `/pagesB/map/MapRail?hotel_id=${this.selectItem.id}`,
        })
      }

    },
    /* 暂停和继续 */
    timeOt() {
      this.timeOut = true
      this.ok = true
      this.cancel = true
    },
    /* 防抖 */
    setTime() {
      let timestamp = new Date().valueOf()
      if (timestamp - this.lastOrderTime < 5000) {
        uni.showToast({
          title: `其他指令正在执行请等${5 - ((timestamp - this.lastOrderTime) / 1000).toFixed(0) + 1
            }秒后进行其他操作`,
          icon: 'none',
          duration: 1500,
        })
        // this.lastOrderTime = timestamp;
        return false
      } else {
        this.lastOrderTime = timestamp
        return true
      }
    },
    // 播放音乐时长
    openMusic() {
      this.isShowMusicModal = true
    },
    // 播放音乐时长
    confirmMusicLength() {
      if (!this.setTime()) {
        return
      }
      this.startMusic(this.adjust_time)
    },
    // 播放音乐
    startMusic(time) {
      this.isShowProgress = true
      this.titles = '批量播放音乐'
      let data = {
        // device_sn: this.device_sn,
        length_time: time, //获取子组件传来的数据
      }
      const requestCallback = async (data) => {
        return this.$u.api.startMusic(data)
      }

      this.sendRequests(this.dataList, data, 3000, requestCallback)
      // this.$u.api.startMusic(data).then((res) => {
      //   console.log(res);
      //   this.isShowSuccess(res.msg);
      // });
    },
    // 开启控制灯
    openLightEnd() {
      if (!this.setTime()) {
        return
      }
      let data = {
        status: 1,
      }
      // console.log("批量开启灯光",);
      this.titles = '批量开启灯光'
      this.isShowProgress = true
      const requestCallback = async (data) => {
        return this.$u.api.turnLight(data)
      }

      this.sendRequests(this.dataList, data, 3000, requestCallback)
      // this.$u.api.turnLight(data).then((res) => {
      //   console.log("启动设备结果4 ：", res);
      //   this.light = false
      //   // this.isShowSuccess("操作成功", 1, () => { }, true);
      //   this.isShowTwo("操作成功", 1);
      // });
    },
    // 关闭控制灯
    openLightStop() {
      this.isShowLightModal = true
    },
    confirmLight() {
      if (!this.setTime()) {
        return
      }
      // 控制灯
      let data = {
        status: 0,
      }
      this.titles = '批量关闭灯光'
      this.isShowProgress = true
      const requestCallback = async (data) => {
        return this.$u.api.turnLight(data)
      }

      this.sendRequests(this.dataList, data, 3000, requestCallback)
      // console.log("启动设备结果参数 3 ：", data);
      // this.$u.api.turnLight(data).then((res) => {
      //   console.log("启动设备结果4 ：", res);
      //   this.light = true
      //   // this.isShowSuccess("操作成功", 1, () => { }, true);
      //   this.isShowTwo("操作成功", 1);
      // });
    },
    // 开启提示音
    async turnTipMusicEnd() {
      if (!this.setTime()) {
        return
      }
      let data = {
        device_sn: this.device_sn,
        status: 1,
      }
      // console.log('启动设备结果参数 3 ：', data)
      try {
        await this.$u.api.turnTipMusic(data)
        this.music = false
        // this.isShowSuccess("操作成功", 1, () => { }, true);
        this.isShowTwo('操作成功', 1)
      } catch (error) {
      }

    },
    // 关闭提示音
    turnTipMusicStop() {
      this.isShowMusicStopModal = true
    },
    async confirmMusicStopLength() {
      if (!this.setTime()) {
        return
      }
      let data = {
        device_sn: this.device_sn,
        status: 0,
      }
      // console.log('启动设备结果参数 3 ：', data)
      try {
        await this.$u.api.turnTipMusic(data)
        this.music = true
        // this.isShowSuccess("操作成功", 1, () => { }, true);
        this.isShowTwo('操作成功', 1)
      } catch (error) {
        // console.log('启动设备结果参数 3 ：', error)
      }

    },
    /* 批量接触绑定 */
    onUnbind(item) {
      // 已经绑定了点位 弹出是否解绑
      // this.unBindItem = item;
      this.isShowNameModal = true
    },
    confirmUnBind() {
      //确认解绑
      let data = {}
      this.titles = '批量接除绑定'
      this.isShowProgress = true
      const requestCallback = async (data) => {
        return this.$u.api.unbindHotel(data)
      }

      this.sendRequests(this.dataList, data, 3000, requestCallback)
      // this.$u.api.unbindHotel(data).then((res) => {
      //   this.isShowSuccess("解绑成功", 0, () => this.refresh());
      // });
      // console.log("确认解绑");
    },
    deviceGoods(device_sn) {
      uni.navigateTo({
        url: `/pagesB/device/DeviceGoodsList?from=device&device_sn=${device_sn}`,
      })
    },
    clicke(i) {
      this.isShowPopup = false
      if (i == 1) {
        this.confirmCheckout()
      } else if (i == 2) {
        this.start(0)
      } else if (i == 3) {
        this.openLightEnd()
      } else if (i == 4) {
        this.openLightStop()
      } else if (i == 5) {
        this.onUnbind()
      } else if (i == 6) {
        this.openMusic()
      }
    },
    // 游戏设备，启动游戏设备
    start(i) {
      // 补货管理才能有启动
      this.selectIndex = i
      this.isShowStartModal = true
      // console.log('启动设备结果参数 1 ：', i)
    },
    confirmStart() {
      // 开启游戏设备
      this.isShowProgress = true

      this.startGameDevice(this.selectIndex + 1, this.length_time)
    },
    startGameDevice(channel, time) {
      // 启动设备
      // 启动设备

      let data = {
        channel: channel, // 货道
        length_time: time,
      }
      // console.log('启动设备结果参数 3 ：', channel, time)
      // for (let i = 0; i < this.dataList.length; i++) {

      // }
      this.titles = '批量启动'
      // 示例用法：每个请求之间等待1000毫秒（1秒）

      // const intervalBetweenRequests = 1000; // 1000毫秒 = 1秒
      // 定义回调函数
      const requestCallback = async (data) => {
        return this.$u.api.startUM(data)
      }

      this.sendRequests(this.dataList, data, 3000, requestCallback)

      // this.$u.api.startUM(data).then((res) => {
      //   console.log("启动设备结果4 ：", res);
      //   this.isShowSuccess("操作成功", 1, () => { }, true);
      // });
    },
    goGoodsAdd() {
      uni.navigateTo({ url: '/pagesB/place/PlaceAdd?from=add' })
    },
    async sendRequests(dataList, datas, interval, requestCallback) {
      for (const url of dataList) {
        if (!this.timeOut) {
          this.device_sn = url.device_sn

          let data = {
            ...datas,
            device_sn: this.device_sn,
          }
          // console.log('请求模板参数', data)

          try {
            // 在这里处理请求的响
            // const response = await this.$u.api.startUM(data)
            const response = await requestCallback(data)
            this.finishNum++
          } catch (error) {
            // 在这里处理请求的错误
            this.failNum++
          }
          let time = this.ProgressNum * 1 + Math.ceil(100 / this.total)
          if (time >= 100) {
            this.ProgressNum = 100
            this.cancel = true
            this.ok = true
          } else {
            this.ProgressNum = time * 1
          }
          // 在每个请求之后等待一段时间
          await this.sleep(interval)
        }
      }
    },

    // 辅助函数：等待指定的时间
    sleep(ms) {
      return new Promise((resolve) => setTimeout(resolve, ms))
    },

    goodsTemp(id) {
      uni.navigateTo({
        url: '/pagesB/device/DeviceGoodsList?from=place&id=' + id,
      })
    },

    goEdit(item) {
      if (this.isFromAdSelectPlace) {
        let pages = getCurrentPages()
        let prevPage = pages[pages.length - 2] //上一个页面
        //直接调用上一个页面的setData()方法，把数据存到上一个页面中去
        prevPage.setData({
          ad_select_item: item,
        })
        uni.navigateBack({ delta: 1 })
      } else {
        uni.navigateTo({
          url:
            '/pagesB/place/PlaceAdd?from=edit&data=' +
            encodeURIComponent(JSON.stringify(item)),
        })
      }
    },
    deviceList(item) {
      uni.navigateTo({
        url: `/pagesB/device/DeviceList?from=place&dianwei=${item.dianwei}&hotel_id=${item.id}`,
      })
    },
    saleGoods(id) {
      uni.navigateTo({ url: `/pagesB/goods/GoodsList?from=place&id=${id}` })
    },
    //修改分成 弹出modal
    modify(item) {
      this.selectItem = item
      this.isShowModal = true
    },
    //modal点击确认 修改分成
    async confirm() {
      let data = {
        role_id: 6,
        per_user_id: this.selectItem.dianwei,
        per: this.percentage,
      }
      // 获取数据
      try {
        await this.$u.api.divideMoney(data)
        this.isShowSuccess('设置成功', 0, () => this.refresh())
      } catch (error) {
        console.log(error)
      }

      this.percentage = ''
    },
    searchChange(val) {

      this.hotelName = val

      this.search(val)

    },
    search(val) {
      // this.device_sn = val;
      this.hotel_name = val
      let list = AddValueInObject(this.vServerList.placeList, val)
      this.$u.vuex(`vServerList.placeList`, list);
      this.refresh()
    },
    async getList(page, done) {
      try {
        let data = {
          hotel_name: this.hotel_name,
          owner_login: this.owner_login,
          page,
          user_login: this.user_login,
          limit: 10,
          range: this.range
        }
        let res = await this.$u.api.getMyHotels(data)
        if (page == 1) {
          this.total = res.total || 0
        }
        done(res.data)
      } catch (error) {
        console.log(error)
      }

    },
    // 充电模板
    async chargeTemp(item) {
      this.selectItem = item
      let data = {
        hotel_id: item.id,
      }
      try {
        let res = await this.$u.api.getHotelRechargeRule(data)
        if (res) {
          let { rule_money, rule_time, rule_unit } = res
          if (res.rule_unit === 1) {
            rule_unit = 2
            rule_money = rule_money * 60
          }
          this.chargeRuleInfo = {
            rule_money,
            rule_time,
            rule_unit,
          }
        } else {
          this.chargeRuleInfo = {
            rule_money: '',
            rule_time: 1,
            rule_unit: 2,
          }
        }
        this.isShowModalChargeTemp = true
      } catch (error) {
        console.log(error)
      }

    },
    selectRuleUnit(i) {
      this.chargeRuleInfo.rule_unit = this.sheetList[i]?.rule_unit ?? 2
    },
    // 确认编辑模板
    async confirmChargeTemp(isUse) {
      try {
        let data = {
          rule_money: this.chargeRuleInfo.rule_money,
          rule_time: 1,
          rule_unit: 2,
          hotel_id: this.selectItem.id,
        }
        if (isUse) {
          await this.$u.api.addHotelRechargeRuleAndCopy(data)
          this.isShowSuccess('保存并应用成功')
        } else {
          awaitthis.$u.api.addHotelRechargeRule(data)
          this.isShowSuccess('编辑成功')
        }
      } catch (error) {
        console.log(error)
      }

    },
    //跳转随机界面
    ckLotter(item) {
      uni.navigateTo({
        url: `/pagesD/random/RandomTasks?from=place&hotel_id=${item.id}`,
        // url: `/pagesD/lottery/Lottery`,
      })
    },
    /* 更多 */
    more(item) {
      this.selectItem = item
      this.showTaskType = true
    },
    /* 免费开关 */
    free() {
      if (this.selectItem.is_all_free !== 1) {
        this.index = 0
      } else if (this.selectItem.is_all_free == 1) {
        this.index = 1
      }
      this.showTaskType = false
      this.isShowDelModalFree = true
      this.id = this.selectItem.id
      // this.selectItem = item
    },
    /* 免费开关弹窗 */
    async confirmDelFree() {
      let is_all_free = 0
      if (this.index == 0) {
        is_all_free = 1
      } else if (this.index == 1) {
        is_all_free = 0
      }
      let params = {
        id: this.id,
        is_all_free,
      }
      try {
        let res = await this.$u.api.setUpdateHotelAllFreeStatus(params)
        // this.refresh();
        let index = this.listData.findIndex(
          (items) => items.id === this.selectItem.id,
        ) // 查找id为2的元素索引
        if (index !== -1) {
          // 如果找到了
          if (this.selectItem.is_all_free == 0) {
            this.selectItem.is_all_free = 1
          } else if (this.selectItem.is_all_free == 1) {
            this.selectItem.is_all_free = 0
          }
          this.listData.splice(index, 1, this.selectItem)
        }


      } catch (error) {
        console.log('error', error)
      }

    },
    /* 删除场地方 */
    move() {
      this.showTaskType = false
      this.isShowDelModalMove = true
      this.id = this.selectItem.id
      // this.selectItem = item
    },
    /* 免费开关弹窗 */
    async confirmDelMove() {
      try {
        let params = {
          hotel_id: this.id,
        }
        await this.$u.api.deleteHotel(params)

        this.isShowSuccess('删除成功', 0, () => this.refresh())
      } catch (error) {
        console.log('失败返回信息', error)
      }

    },
    /* 自动待机 */
    autoOnOff(item) {
      if (item.is_auto_on_off !== 1) {
        this.index = 2
      } else if (item.is_auto_on_off == 1) {
        this.index = 3
      }
      this.isShowDelModalOnOff = true
      this.id = item.id
      this.items = item
    },
    /* 自动开关弹窗 */
    async confirmDelOnOff() {
      let is_auto_on_off = 0
      if (this.index == 2) {
        is_auto_on_off = 1
      } else if (this.index == 3) {
        is_auto_on_off = 0
      }

      let params = {
        id: this.id,
        is_auto_on_off,
      }
      try {
        let res = await this.$u.api.setUpdateHotelAutoOnOffStatus(params)
        // this.refresh();
        // item.is_auto_on_off=!item.is_auto_on_off
        let index = this.listData.findIndex(
          (items) => items.id === this.items.id,
        ) // 查找id为2的元素索引
        if (index !== -1) {
          // 如果找到了
          if (this.items.is_auto_on_off == 0) {
            this.items.is_auto_on_off = 1
          } else if (this.items.is_auto_on_off == 1) {
            this.items.is_auto_on_off = 0
          }
          this.listData.splice(index, 1, this.items)
        }


      } catch (error) {
        console.log('批量管理', error)
      }


    },
    /* 批量管理 */
    BulkManagement(item) {
      // this.isShowWaterModal = true
      if (item.machine_num >= 50) {
        return this.isShowErr('设备过多无法批量处理')
      }

      uni.navigateTo({
        url:
          '/pagesD/progress/ProgressList?from=edit&data=' +
          encodeURIComponent(JSON.stringify(item)),
      })
      // this.isShowPopup = true
      // console.log('获取ho', item)
      // this.hotel_id = item.id
      // this.total = item.machine_num
      // this.finishNum = 0
      // this.ProgressNum = 0
      // this.failNum = 0
      // let data = {
      //   dianweiid: item.dianwei,
      //   hotel_id: this.hotel_id,
      //   limit: this.total
      // }
      // console.log('点击数据', data)
      // this.$u.api.getHotelMachines(data).then(res => {
      //   this.dataList = res.data
      //   console.log('设备列表数据', this.dataList)
      // })
    },
    /* 修改后的列表 */

    chckedata(e) {
      this.checkoutDatalist = e
    },
    /* 批量添加水量弹窗 */
    confirmCheckout() {
      this.isShowWaterModal = true
    },
    /* 补充水量 */
    checkNumber(item) {
      // this.WarterNum = item > 3 ? 3 : item < 0 ? 0 : item;
      // console.log(this.WarterNum, item, item * 1 > 3 || item * 1 < 0);
      if (item >= 3) {
        this.WarterNum = 3.0
        this.isShowErr('最大为3升自动修改为3升')
        // return
      } else if (item < 0) {
        this.WarterNum = 0.0
        this.isShowErr('最小为0升自动修改为0升')
        // return
      } else if (!item) {
        this.WarterNum = 0.0
        // return
      } else if (this.WarterNum.toString().split('.').pop().length > 1) {
        this.WarterNum = Math.floor(item * 10) / 10
        this.isShowErr('最多一位小数补充为' + this.WarterNum)
        // return
      }
      // this.WarterNum = this.WarterNum.replace(/[1-3]/g,'')
    },
    /* 确认补水 */
    async confirmWater() {
      this.checkNumber(this.WarterNum)
      let data = {
        hotel_id: this.hotel_id,
        cbm: this.WarterNum,
      }
      setTimeout(() => {
        try {
          const res = this.$u.api.setupdateHotelAllCbm(data)
          this.isShowErr('更改成功')
        } catch (e) {
          this.isShowErr('更改失败')
        }
      }, 700)
    },
  },

  onLoad(options) {
    if (options?.from === 'ad_select_place') {
      this.isFromAdSelectPlace = true
    }
    this.title = this.vPointName + '管理'
    if (this.vInputDisable) {
      this.length_time = this.vTime
    }
    this.refresh()
  },
  onShow() {
    /*  #ifndef H5 */
    let pages = getCurrentPages()
    let currPage = pages[pages.length - 1] // 当前页
    if (currPage.data.isDoRefresh == true) {
      // 是否刷新
      currPage.data.isDoRefresh = false
      this.searchValue = ''
      this.refresh()
    }
    /*#endif */
    /*  #ifdef H5 */

    if (this.vCurrPage.isDoRefresh == true) {
      // 是否刷新
      this.vCurrPage.isDoRefresh = false;
      this.searchValue = "";
      this.refresh();
    }
    /*#endif */
  },
  computed: {
    tiem() {
      if (!this.vGamingTime > 0) {
        return 0
      }
      return this.WarterNum * this.vGamingTime
    },
  },
  mixins: [myPull()],
}
</script>

<style lang="scss" scoped>
.pup_box {
  padding: 20rpx;
  padding-top: 40rpx;
  display: flex;
  flex-wrap: wrap;

  >view {
    width: 270rpx;
    margin: 20rpx;
  }

  .default {
    padding: 5rpx 0;
    font-size: 35rpx;
  }
}

// .charge {
//   &-item {
//     display: flex;

//     align-items: center;
//     font-size: $font-size-middle;
//     &-title {
//       color: $textBlack;
//     }
//     &-txt {
//       color: $textDarkGray;
//     }
//   }
// }

.center_pro_title {
  text-align: center;
}

.cent_progres {
  padding: 0 30rpx;
  margin-top: 28rpx;

  .center_pro_top {
    // display: flex;
    width: 100%;

    // flex-wrap: wrap;
    view {
      margin: 5rpx 0;
      // flex: 1;
      // border: 1px solid #000;
      // width:100%;
    }
  }

  .center_pro_bottom {
    display: flex;

    >text {
      // border: 1px solid #000;
      margin-right: 25rpx;
    }
  }

  .center_pro {
    // height: 18rpx;
    // border: 1px solid #000;
    // display: flex;
    // justify-content: center;
    margin-bottom: 10rpx;
  }

  .center_pro_btn {
    // border: 1px solid #000;
    margin: 20rpx 0;
  }
}

.default {
  text-align: center;
  font-size: 26rpx;
}

.center_btn {
  padding: 0 15rpx;
  // border: 1px solid #000;
  margin-top: 80rpx;
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;

  >view {
    margin: 15rpx;
  }
}

.red {
  color: orangered;
}

.modal {
  text-align: center;
}

.charge {
  .charge-info {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .line {
    margin: 0 20rpx;
  }

  &-rule-money {
    flex: 1;
  }

  &-rule-time {
    flex: 1;
  }

  .default {
    text-align: center;
    font-size: 26rpx;
  }

  .select-rule {
    color: $themeComColor;
    text-align: center;
    font-weight: 700;
    margin-top: 20rpx;
  }
}
</style>
