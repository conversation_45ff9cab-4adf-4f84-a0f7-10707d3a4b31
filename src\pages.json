{
	"easycom": {
		"^u-(.*)": "uview-ui/components/u-$1/u-$1.vue"
	},
	"pages": [ //pages数组中第一项表示应用启动页，参考：https://uniapp.dcloud.io/collocation/pages
		{
			"path": "pages/login/Login",
			"style": {
				"navigationBarTitleText": "登录"
			}
		},
		{
			"path": "pages/login/Forget",
			"style": {
				"navigationBarTitleText": "忘记密码"
			}
		},
		{
			"path": "pages/index/index",
			"style": {
				"navigationBarTitleText": "",
				"enablePullDownRefresh": true
			}
		},
		{
			"path": "pages/index/SweepScan",
			"style": {
				"navigationBarTitleText": ""
			}
		},
		{
			"path": "pages/index/Personal",
			"style": {
				"navigationBarTitleText": "我的",
				"navigationStyle": "custom"
			}
		}
	],
	"subPackages": [
		{
			"root": "pagesB",
			"pages": [
				{
					"path": "device/DeviceList",
					"style": {
						"navigationBarTitleText": "设备列表",
						"enablePullDownRefresh": true // 可以上拉刷新
					}
				},
				{
					"path": "device/DeviceGoodsList",
					"style": {
						"navigationBarTitleText": "设备套餐",
						"enablePullDownRefresh": true // 可以上拉刷新
					}
				},
				{
					"path": "device/DeviceActivate",
					"style": {
						"navigationBarTitleText": "领取设备"
					}
				},
				{
					"path": "device/DeviceDetail",
					"style": {
						"navigationBarTitleText": "设备详情"
					}
				},
				{
					"path": "device/DeviceAd",
					"style": {
						"navigationBarTitleText": "设备广告"
					}
				},
				{
					"path": "device/DeviceGoodsEdit",
					"style": {
						"navigationBarTitleText": "编辑商品"
					}
				},
				{
					"path": "device/DeviceAllot",
					"style": {
						"navigationBarTitleText": "设备分配"
					}
				},
				{
					"path": "mapMode/MapMode",
					"style": {
						"navigationBarTitleText": "地图模式"
					}
				},
				{
					"path": "goods/GoodsList",
					"style": {
						"navigationBarTitleText": "我的商品",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "goods/GoodsAdd",
					"style": {
						"navigationBarTitleText": "新增商品"
					}
				},
				{
					"path": "place/PlaceList",
					"style": {
						"navigationBarTitleText": "点位管理",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "place/PlaceAdd",
					"style": {
						"navigationBarTitleText": "新增点位"
					}
				},
				{
					"path": "user/UserList",
					"style": {
						"navigationBarTitleText": "用户管理",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "user/UserAdd",
					"style": {
						"navigationBarTitleText": "新增用户"
					}
				},
				{
					"path": "user/SelectUser",
					"style": {
						"navigationBarTitleText": "选择用户"
					}
				},
				{
					"path": "renew/RenewList",
					"style": {
						"navigationBarTitleText": "续费管理",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "renew/RenewConfirmOrder",
					"style": {
						"navigationBarTitleText": "确认订单"
					}
				},
				{
					"path": "renew/RenewRecord",
					"style": {
						"navigationBarTitleText": "续费记录",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "finance/FinanceList",
					"style": {
						"navigationBarTitleText": "财务管理",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "finance/Withdrawal",
					"style": {
						"navigationBarTitleText": "余额提现"
					}
				},
				{
					"path": "finance/WithdrawalRecord",
					"style": {
						"navigationBarTitleText": "提现记录",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "finance/FinanceAudit",
					"style": {
						"navigationBarTitleText": "财务审核",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "order/OrderList",
					"style": {
						"navigationBarTitleText": "订单管理",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "order/ChargeOrder",
					"style": {
						"navigationBarTitleText": "充电订单管理",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "order/OrderMaintain",
					"style": {
						"navigationBarTitleText": "订单维护",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "order/OrderMaintainApply",
					"style": {
						"navigationBarTitleText": "订单维护申请"
					}
				},
				{
					"path": "replenish/ReplenishList",
					"style": {
						"navigationBarTitleText": "补货列表",
						"enablePullDownRefresh": true,
						"navigationStyle": "custom"
					}
				},
				{
					"path": "replenish/ReplenishRecord",
					"style": {
						"navigationBarTitleText": "补货记录",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "screen/ScreenList",
					"style": {
						"navigationBarTitleText": "屏幕管理",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "screen/ScreenDetails",
					"style": {
						"navigationBarTitleText": "屏幕详情"
					}
				},
				// {
				// 	"path": "screen/ScreenAdd",
				// 	"style": {
				// 		"navigationBarTitleText": "新增屏幕"
				// 	}
				// },
				{
					"path": "screen/ScreenUploadAd",
					"style": {
						"navigationBarTitleText": "上传屏幕广告"
					}
				},
				{
					"path": "cloud/CloudCollege",
					"style": {
						"navigationBarTitleText": "云学院"
					}
				},
				{
					"path": "cloud/LatelyLiveList",
					"style": {
						"navigationBarTitleText": "最近直播"
					}
				},
				// {
				// 	"path": "cloud/CloudVideoDetails",
				// 	"style": {
				// 		"navigationBarTitleText": "视频详情"
				// 	}
				// },
				{
					"path": "place/BindPlace",
					"style": {
						"navigationBarTitleText": "设备绑定"
					}
				},
				
				{
					"path": "relevance/RelevanceEquipment",
					"style": {
						"navigationBarTitleText": "关联设备"
					}
				},
				{
					"path": "equipment/EquipmentSelect",
					"style": {
						"navigationBarTitleText": "设备选择"
					}
				},
				{
					"path": "place/SelectPlace",
					"style": {
						"navigationBarTitleText": "选择商户",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "notice/NoticeList",
					"style": {
						"navigationBarTitleText": "消息中心"
					}
				},
				{
					"path": "notice/NoticeDetail",
					"style": {
						"navigationBarTitleText": "系统消息"
					}
				},
				{
					"path": "purchase/PurchaseList",
					"style": {
						"navigationBarTitleText": "集采商城"
					}
				},
				{
					"path": "purchase/PurchaseConfirmOrder",
					"style": {
						"navigationBarTitleText": "确认订单"
					}
				},
				{
					"path": "purchase/PurchaseManage",
					"style": {
						"navigationBarTitleText": "集采管理",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "score/ScoreList",
					"style": {
						"navigationBarTitleText": "积分管理",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "score/ScoreDetails",
					"style": {
						"navigationBarTitleText": "积分记录",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "score/ScoreTaskAdd",
					"style": {
						"navigationBarTitleText": "新增任务"
					}
				},
				{
					"path": "adverts/AdvertsAuditList",
					"style": {
						"navigationBarTitleText": "广告审核",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "adverts/AdvertsDetails",
					"style": {
						"navigationBarTitleText": "广告审核详情"
					}
				},
				{
					"path": "selectedMall/SelectedMall",
					"style": {
						"navigationBarTitleText": "精选商城",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "advertsManage/AdvertsManage",
					"style": {
						"navigationBarTitleText": "广告管理",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "advertsManage/AdverstAdd",
					"style": {
						"navigationBarTitleText": "添加广告"
					}
				},
				{
					"path": "deviceAdverts/DeviceAdverts",
					"style": {
						"navigationBarTitleText": "设备广告",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "deviceAdverts/DeviceAdvertsAdd",
					"style": {
						"navigationBarTitleText": "新增和编辑设备广告"
					}
				},
				{
					"path": "wechat/Wechat",
					"style": {
						"navigationBarTitleText": "公众号管理",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "wechat/WechatAdd",
					"style": {
						"navigationBarTitleText": "新增公众号",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "invest/InvestList",
					"style": {
						"navigationBarTitleText": "认养管理",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "dataAnalysis/DataAnalysis",
					"style": {
						"navigationBarTitleText": "数据分析"
					}
				},
				{
					"path": "coupon/Coupon",
					"style": {
						"navigationBarTitleText": "优惠券管理"
					}
				},
				{
					"path": "coupon/CouponList",
					"style": {
						"navigationBarTitleText": "优惠券列表",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "coupon/CouponRecord",
					"style": {
						"navigationBarTitleText": "优惠券领取记录",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "coupon/CouponSetMeal",
					"style": {
						"navigationBarTitleText": "优惠券套餐购买"
					}
				},
				{
					"path": "coupon/CouponCheck",
					"style": {
						"navigationBarTitleText": "优惠券核销",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "coupon/CouponAdd",
					"style": {
						"navigationBarTitleText": "添加优惠券"
					}
				},
				{
					"path": "coupon/CouponWifi",
					"style": {
						"navigationBarTitleText": "创建WiFi"
					}
				},
				{
					"path": "coupon/CouponWifiShare",
					"style": {
						"navigationBarTitleText": "我的分享海报"
					}
				},
				{
					"path": "map/MapRail",
					"style": {
						"navigationBarTitleText": "电子围栏"
					}
				}

			]
		},
		{
			"root": "pagesC",
			"pages": [
				{
					"path": "remote/RemoteEquipment",
					"style": {
						"navigationBarTitleText": "设备遥控器"
					}
				},
				{
					"path": "help/HelpCenter",
					"style": {
						"navigationBarTitleText": "帮助中心"
					}
				},
				{
					"path": "about/AboutUs",
					"style": {
						"navigationBarTitleText": "关于我们"
					}
				},
				{
					"path": "set/SetUp",
					"style": {
						"navigationBarTitleText": "设置"
					}
				},
				{
					"path": "set/ChangeInfo",
					"style": {
						"navigationBarTitleText": "修改密码和手机号"
					}
				},
				{
					"path": "webView/WebView"
				}
			]
		},
		{
			"root": "pagesD",
			"pages": [
				{
					"path": "vip/addVip",
					"style": {
						"navigationBarTitleText": "会员设置"
					}
				},
				{
					"path": "vip/setVip",
					"style": {
						"navigationBarTitleText": "会员设置"
					}
				},
				{
					"path": "user/UserChange",
					"style": {
						"navigationBarTitleText": "切换账号"
					}
				},
				{
					"path": "user/UserList",
					"style": {
						"navigationBarTitleText": "管理账号"
					}
				},
				{
					"path": "lottery/Lottery",
					"style": {
						"navigationBarTitleText": "抽奖"
					}
				}
				,
				{
					"path": "random/RandomTasks",
					"style": {
						"navigationBarTitleText": "任务管理"
					}
				},
				{
					"path": "random/RandomTasksAdd",
					"style": {
						"navigationBarTitleText": "添加随机任务"
					}
				},
				{
					"path": "Invitation/Invitation",
					"style": {
						"navigationBarTitleText": "我的邀请"
					}
				},
				{
					"path": "Remittance/Remittance",
					"style": {
						"navigationBarTitleText": "实名身份认证"
					}
				},
				{
					"path": "progress/ProgressList",
					"style": {
						"navigationBarTitleText": "批量管理"
					}
				},
				
				{
					"path": "bindList/BindList",
					"style": {
						"navigationBarTitleText": "批量绑定"
					}
				},
				
				{
					"path": "History/History",
					"style": {
						"navigationBarTitleText": "行为记录"
					}
				},
				{
					"path": "History/OrderHistory",
					"style": {
						"navigationBarTitleText": "订单指令详情"
					}
				},
				
				{
					"path": "dashboards/Dashboards",
					"style": {
						"navigationBarTitleText": "数据看板",
						"enablePullDownRefresh": true
					}
				},
				
				{
					"path": "Renewal/RenewalAdd",
					"style": {
						"navigationBarTitleText": "添加续费"
					}
				},
				
				{
					"path": "Renewal/RenwalSelect",
					"style": {
						"navigationBarTitleText": "续费选择"
					}
				},
				
				{
					"path": "Scan/Scan",
					"style": {
						"navigationBarTitleText": "扫一扫"
					}
				}
			]
		}
	],
	"globalStyle": {
		"navigationBarTextStyle": "black",
		"navigationBarBackgroundColor": "#F8F8F8",
		"backgroundColor": "#F8F8F8",
		"navigationBarTitleText": "",
		"navigationStyle": "custom"
	},
	"tabBar": {
		"list": [
			{
				"pagePath": "pages/index/index"
			},
			{
				"pagePath": "pages/index/SweepScan"
			},
			{
				"pagePath": "pages/index/Personal"
			}
		]
	}
}