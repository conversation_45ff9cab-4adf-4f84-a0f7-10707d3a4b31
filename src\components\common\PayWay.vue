<template>
  <view class="pay">
    <view class="info">
      <view class="top">
        <view class="title">支付方式</view>
        <view class="order-money">订单金额：{{ orderMoney }}</view>
      </view>
      <view class="type">
        <view
          class="type-box"
          v-for="(item, index) in payWayList"
          :key="index"
          @click.stop="selectWay(item.type)"
        >
          <view class="type-box-left">
            <image class="img" :src="item.icon" />
            <view class="name">{{ item.name }}</view>
          </view>
          <view class="type-box-right">
            <view v-if="item.type === 0" class="balance">
              余额：{{ vUserInfo.user.cash || "0.00" }}
            </view>
            <BaseCheck :checked="currentIndex === item.type" />
          </view>
        </view>
      </view>
    </view>
    <view class="sub-btn">
      <BaseButton type="primary" @onClick="subBtn">确认</BaseButton>
    </view>
  </view>
</template>

<script>
import BaseButton from "../base/BaseButton.vue";
import BaseCheck from "../base/BaseCheck.vue";
export default {
  components: { BaseCheck, BaseButton },
  name: "PayWay",
  props: {
    isShowBalacnceModel: { type: Boolean, default: true }, //是否显示余额支付
    orderMoney: { type: [Number,String], default: null }, //订单金额
  },
  data() {
    return {
      isCheck: true,
      payWayList: [
        {
          type: 0,
          icon: require("@/static/img/icon/pay-way-balance.png"),
          name: "余额支付",
          payType: "balance",
        },
        {
          type: 1,
          icon: require("@/static/img/icon/pay-way-wx.png"),
          name: "微信支付",
          payType: "online",
        },
      ],
      currentIndex: 0,
    };
  },
  methods: {
    selectWay(e) {
      this.currentIndex = e;
    },
    subBtn() {
      let type = this.payWayList[this.currentIndex].type;
      let payType = this.payWayList[this.currentIndex].payType;
      if (type === 0) {
        if (
          parseFloat(this.orderMoney) > parseFloat(this.vUserInfo.user.cash)
        ) {
          return this.isShowErr("您的余额不足,请选择其他付款方式~");
        } else {
          this.$emit("confirmPay", type, payType);
        }
      } else if (type === 1) {
        this.getOpenid(type, payType);
      }
    },
    getOpenid(type, payType) {
      if (!this.vPayOpenid) {
        let that = this;
        uni.login({
          provider: "weixin",
          success: (res) => {
            that.$u.api.getPayOpenid({ code: res.code }).then((res) => {
              let rows = res.user_info;
              this.$u.vuex("vPayOpenid", rows?.openid);
              this.$emit("confirmPay", type, payType);
            });
          },
          fail: (error) => {
            this.isShowErr("获取用户信息失败,请重试~");
          },
        });
      } else {
        this.$emit("confirmPay", type, payType);
      }
    },
  },
  mounted() {
    this.currentIndex = this.isShowBalacnceModel ? 0 : 1;
  },
};
</script>

<style lang="scss" scoped>
.pay {
  padding: 20rpx 20rpx 0;
}
.info {
  .top {
    display: flex;
    justify-content: space-between;
    .title {
      color: $textBlack;
      font-weight: 700;
      font-size: $font-size-xlarge;
    }
    .order-money {
      color: $textWarn;
      font-size: $font-size-middle;
    }
  }

  .type {
    &-box {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 50rpx 0;
      border-bottom: 2rpx solid $dividerColor;
      &:last-child {
        border-bottom: none;
      }
      &-left {
        display: flex;
        align-items: center;
        color: $textBlack;
        font-size: $font-size-middle;
        .img {
          width: 54rpx;
          height: 54rpx;
        }
        .name {
          margin-left: 21rpx;
          font-weight: 700;
        }
      }
      &-right {
        display: flex;
        align-items: center;
        .balance {
          margin-right: 30rpx;
          color: $themeComColor;
        }
      }
    }
  }
}
.sub-btn {
  margin-bottom: 80rpx;
}
</style>