<template>
  <view class="wthdrawal-card">
    <view class="balance">
      <view class="top">
        <view class="title">提现金额</view>
        <view class="enterCash flexRowAllCenter">
          <view>￥</view>
          <view class="balance-value">
            <input
              class="inputMoney"
              v-model="financeForm.balance"
              placeholder="请输入提现金额"
              placeholder-style="font-size:50rpx"
            />
            <view class="moneyrightText">
              <view class="moneybtn" @click="monenyAll()">
                  全部提现
              </view>
              <view class="moneytxt">
                余额￥{{balance}}
              </view>
            </view>
            <!-- '最多转出' + maxMoney + '元' -->
          </view>
        </view>
      </view>
      <view class="remarks flexRowAllCenter">
        <view class="remarks-title">备注</view>
        <view class="remarks-value">
          <BaseInput
            v-model="financeForm.remarks"
            bgColor="#fff"
            placeholder="请输入备注信息"
          />
        </view>
      </view>
    </view>
    <view class="info">
      <view class="user-name flexRowAllCenter" v-if="way != 4">
        <view>真实姓名</view>
        <view>
          <BaseInput
            v-model="financeForm.name"
            bgColor="#fff"
            placeholder="请输入真实姓名"
            height="86"
          />
        </view>
      </view>
      <view class="user-number flexRowAllCenter" v-if="way != 2 && way != 4">
        <view>{{ name }}号</view>
        <view>
          <BaseInput
            v-model="financeForm.number"
            bgColor="#fff"
            :placeholder="'请输入' + name + '号'"
            height="86"
          />
        </view>
      </view>
    </view>
    <view class="confirm">
      <BaseButton type="primary" @onClick="confirm">确认提现</BaseButton>
    </view>
  </view>
</template>

<script>
import BaseButton from '@/components/base/BaseButton.vue'
import BaseInput from '@/components/base/BaseInput.vue'
export default {
  name: 'WithdrawalCard',
  components: { BaseInput, BaseButton },
  props: {
    type: {
      type: Number,
      default: 0,
    },
    maxMoney: {
      type: [String,Number],
      default: 5000,
    },
    name: { type: String, default: '' },
    way: {
      type: Number,
      default: 0,
    },
    balance:{
      type:[String,Number],
      default:0
    }
  },
  data() {
    return {
      financeForm: {
        balance: 0,
        remarks: '',
        name: '',
        number: '',
      },
    }
  },
  methods: {
    confirm() {
      // if (Number(this.financeForm.balance) > Number(this.maxMoney))
      //   return this.isShowErr("提现金额超过最大提现金额~");
      this.$emit('confirm', this.financeForm)
    },
    monenyAll(){
      this.financeForm.balance=this.balance
    }
  },
}
</script>

<style lang="scss" scoped>
.wthdrawal-card {
  padding: 30rpx;

  .balance {
    padding-top: 30rpx;
    background-color: $uni-bg-color;
    border-radius: $cardRadius;
    color: $textBlack;
    font-size: $font-size-base;

    .top {
      padding: 0 20rpx;
      padding-bottom: 20rpx;
      border-bottom: 2rpx solid $dividerColor;

      .title {
        font-weight: bold;
        font-size: $font-size-xlarge;
        margin-bottom:30rpx;
      }

      .enterCash {
        margin: 10rpx 0;
       
        font-size: 50rpx;
        height: 100rpx;
        align-items: flex-end;
        justify-content: flex-start;
        .inputMoney {
          font-weight: bold;
          height: 75rpx;
          font-size: 75rpx;
          line-height: 75rpx;
          padding: 0 10rpx;
          &::placeholder{
            color:red;
            font-size: 20px; /* 设置字体大小 */
            /* 其他样式属性 */
          }
          width:400rpx;
          // border:2rpx solid red;
          margin-right:10rpx;
        }
        .balance-value {
          // width: 100%;
          display:flex;
          .moneyrightText{
            width:180rpx;
            // border:2rpx solid red;
            text-align:right;
            .moneybtn{
              font-size:24rpx;
              color:#0EADE2;
              margin-bottom:10rpx;
            }
            .moneytxt{
              font-size:25rpx;
              color:#999;
            }
          }
        }
      }
    }

    .remarks {
      padding: 10rpx 20rpx;
      font-size:30rpx;
      &-title {
        width: 80rpx;
        font-weight: bold;
      }

      &-value {
        width: 100%;
      }
    }
  }

  .info {
    margin-top: 20rpx;
    border-radius: $cardRadius;
    background-color: $uni-bg-color;

    > view {
      &:first-child {
        border-bottom: 2rpx solid $dividerColor;
      }

      > view {
        &:first-child {
          width: 150rpx;
          font-weight: bold;
          font-size: $font-size-base;
          text-align: right;
        }

        &:last-child {
          width: 100%;
        }
      }
    }
  }

  .confirm {
    margin-top: 80rpx;
  }
}
</style>
