/* 年月日起 */
export const subtractDaysAndFormat = (inputDays) => {
  const currentTimestamp = Date.now()
  const oneDayMilliseconds = 24 * 60 * 60 * 1000 * inputDays
  const previousDayTimestamp = currentTimestamp - oneDayMilliseconds
  var targetDate = new Date(previousDayTimestamp)
  var year = targetDate.getFullYear()
  var month = targetDate.getMonth() + 1 // 月份从0开始，所以需要加1
  var day = targetDate.getDate()
  var formattedResult = year + '-' + month + '-' + day
  return formattedResult
}
