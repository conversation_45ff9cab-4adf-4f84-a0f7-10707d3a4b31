<template>
  <view>
    <BaseNavbar :title="title" />
    <BaseSearch :showScan="true" :hotelName="hotelName" type-img="place" placeholder="请输入设备编号"
      @onClickIcon="onClickIcon" @search="search" @onClickScan="onClickScan" listType="device" />
    <!-- <BaseList  @searchChange="searchChange" /> -->
    <BaseDropdown :options-list="optionsList" @change="change" :num="deviceTotal" />
    <ComList :loading-type="loadingType">
      <view v-for="(items, i) in pagesData.length" :key="i" :id="'pages-' + i">
        <view :class="'list' + currentIndex + 'i' + i" v-if="i < currentIndex + 2 && i > currentIndex - 2"
          class="border">
          <DeviceListCard class="list-card" v-for="(item, index) in pagesData[i]" :key="index" :info="item"
            :goShow="isShowGo" @onUnbind="onUnbind(item)" @deviceGoods="deviceGoods(item.device_sn)"
            @repl="repl(item.device_sn)" @onCard="onCard(item)" @remote="remote(item)" @updataTime="updataTime(item)"
            @autoOnOff="autoOnOff(item)" @onOff="onOff(item)" @goStart="goStart(item)" @others="other(item)" />
        </view>
        <view v-else :style="{ height: pageHeight[i] + 'px' }" class="border">

        </view>
      </view>

    </ComList>
    <BasePopup :show.sync="isShowPopup" mode="top" :customStyle="customStyle">
      <DeviceScreener ref="DeviceScreener" @confirm="confirmScreenner"></DeviceScreener>
    </BasePopup>
    <!-- 开启开关机 -->
    <!-- <BaseModal :show.sync="isShowDelModalOnOff" @confirm="confirmDelOnOff" :content="PopUps[index]" title="温馨提示">
    </BaseModal> -->
    <!-- 设备启用禁用 -->
    <!-- <disableBaseModal :show="isShowDelModalOff" @confirmDelOff="confirmDelOff" @cancel="cancel" :index="index">
    </disableBaseModal> -->
    <!-- <BaseModal :show.sync="newShow" @cancel="cancel" 
    @confirm="confirmDelOff">
    
    </BaseModal> -->
    <!--  弹窗 -->
    <BaseModal :title="modelTitle" :show.sync="isShowStartModal" :content="message" @confirm="confirmOk"
      :class="index == 6 ? 'modalPad' : ''">
      <view slot="default" v-if="index == 7">
        <BaseInput v-model="length_time" :disabled="vInputDisable" placeholder="请输入启动时长(分钟)" />
      </view>
      <BaseRadio v-if="index == 6" :radioIndex.sync="radioIndex" :list="reasonList" />
      <view v-if="index == 4" class="reason">
        <view class="title">请选择要禁用的理由</view>
        <BaseRadio :radioIndex.sync="onOffIndex" :list="onOffList" />
        <BaseInput v-if="onOffIndex == 3" v-model="reason" placeholder="请输入禁用理由" />
      </view>
    </BaseModal>
    <BasePopup :show.sync="showTaskType" :maskClose="true" mode="center" height="400" :closeable="true" width="690"
      radius="20">
      <view class="pup_box">
        <view v-for="(item, i) in taskTypeList" :key="i" class="pup_item">
          <button class="default" @click.stop="comfirmType(i)">
            {{ item }}
          </button>
        </view>
        <view v-if="unBindItem.isShowLEStauts" class="pup_item">
          <button class="default" @click.stop="stopTime()">
            停顿间隔时间
          </button>
        </view>
      </view>
    </BasePopup>
    <!-- <BaseBackTop v-if="lastScrollTop > systemHeight" listShow @onScroll="onScroll">
    </BaseBackTop> -->
    <BaseBackTop v-if="scrollTop > systemHeight" listShow @onScroll="onScroll">
    </BaseBackTop>
  </view>
</template>

<script>
import BaseDropdown from '@/components/base/BaseDropdown.vue'
import BaseNavbar from '@/components/base/BaseNavbar.vue'
import BaseSearch from '@/components/base/BaseSearch.vue'
import myPull from '@/mixins/myPull.js'
import ComList from '@/components/list/ComList.vue'
import DeviceListCard from '../components/cards/DeviceListCard.vue'
import BaseModal from '@/components/base/BaseModal.vue'
import BasePopup from '../../components/base/BasePopup.vue'
import DeviceScreener from '../components/screener/DeviceScreener.vue'
import BaseInput from '@/components/base/BaseInput.vue'
// import BaseButton from '@/components/base/BaseButton.vue'
import BaseBackTop from '@/components/base/BaseBackTop.vue'
// import Selectlist from '../components/selects/Selectlist.vue'
import disableBaseModal from '@/components/disable/disableBaseModal'
import { subtractDaysAndFormat } from '@/wxutil/times'
import BaseRadio from '@/components/base/BaseRadio.vue'
// import BaseList from '@/components/base/BaseList.vue'
import { AddValueInObject } from '@/wxutil/list'
// import virtualList from '@/components/list/virtualList.vue'
export default {
  components: {
    BaseNavbar,
    BaseSearch,
    BaseDropdown,
    ComList,
    DeviceListCard,
    BaseModal,
    BasePopup,
    DeviceScreener,
    BaseInput,
    // BaseButton,
    disableBaseModal,
    // Selectlist,
    BaseRadio,
    // BaseList,
    BaseBackTop,
    // virtualList
  },
  data() {
    return {
      title: '设备列表',
      fromData: '',
      isFromHome: false,
      isFromPlace: false,
      isFromScreenBindDevice: false,
      isShowStartModal: false,
      isShowSelectModal: false,
      length_time: 1,
      optionsList: [
        {
          title: '全部',
          options: [
            { label: '全部', value: 0, status: 0, tiem: '' },
            // {
            //   label: '在线',
            //   value: 1,
            //   status: 1,
            //   time: ''
            // },
            // {
            //   label: '离线',
            //   value: 2,
            //   status: 3,
            //   time: ''
            // },
            {
              label: '3天未成交',
              value: 1,
              status: 4,
              time: 3,
            },
            {
              label: '7天未成交',
              value: 2,
              status: 7,
              time: 7,
            },
            {
              label: '30天未成交',
              value: 3,
              status: 30,
              time: 30
            },
          ],
          value: 0,
        },
      ],
      isShowModal: false,
      unBindItem: {},
      room_num: Number,
      status: 0,
      dianwei: 0,
      device_sn: '',
      hotel_id: 0,
      owner: '', //上级账号
      is_bind_hotel: false,
      deviceTotal: 0, //设备总数
      isShowPopup: true,
      isBind: 0, //筛选是否绑定 0全部 1已经绑定 2未绑定
      isShowDelModalOnOff: false, //开待机弹窗
      isShowDelModalOff: false, //禁用设备弹窗
      isShowGo: false, //启动其他设备来的
      hotelName: '', //搜索
      PopUps: {
        0: '您确定要开启免费功能吗',
        1: '您确定要关闭免费功能吗',
        2: '您确定要开启自动待机功能吗',
        3: '您确定要关闭自动待机功能吗',
        4: '您确定要禁用该设备吗',
        5: '您确定要启用该设备吗',
      }, //弹窗提示
      index: 0, //弹窗索引
      id: 0, //提交id
      reason: '', //禁用理由
      showTaskType: false,
      taskTypeList: ['查看行为记录', '开启开机自检', '关闭开机自检'],
      options: [
        { label: '0', value: 0 },
        { label: '2', value: 2 },
        { label: '3', value: 3 },
        { label: '4', value: 4 },
        { label: '5', value: 5 },
      ],
      tabValue: 0,
      reasonList: [
        {
          title: '0秒',
          name: '0',
          disabled: false,
          selectIndex: 0,
        },
        {
          title: '2秒',
          name: '1',
          disabled: false,
          selectIndex: 2,
        },
        {
          title: '3秒',
          name: '2',
          disabled: false,
          selectIndex: 3,
        },
        {
          title: '4秒',
          name: '3',
          disabled: false,
          selectIndex: 4,
        },
        {
          title: '5秒',
          name: '4',
          disabled: false,
          selectIndex: 5,
        },
      ],
      radioIndex: 0,
      onOffList: [{
        title: "没电量了",
        name: "0",
        disabled: false,
        selectIndex: 0,

      }, {

        title: "无网络",
        name: "1",
        disabled: false,
        selectIndex: 1,

      },
      //  {
      //   title: "没泡泡液",
      //   name: "2",
      //   disabled: false,
      //   selectIndex: 2,


      // },
       {

        title: "其他故障",
        name: "3",
        disabled: false,
        selectIndex: 3,

      }, {
        title: "其他理由",
        name: "4",
        disabled: false,
        selectIndex: 4,

      }],
      onOffIndex: 0,
      modelTitle: '温馨提示',
      customStyle: {
        top: 110 + this.vStatusBarHeight + this.vNavBarHeight + 'rpx',
      },
      scrollTop: 0,
      currentIndex: 0,          // 当前页数 pageNum
      pageHeight: [],           // 每屏高度存储
      systemHeight: 0,           // 屏幕高度
      scrollControlDisabled: false,//是否只能滑动一屏
    }
  },
  methods: {
    computedHeight() {
      const query = uni.createSelectorQuery().in(this);
      for (let i = 0; i < this.pagesData.length; i++) {
        query.select('#pages-' + i).boundingClientRect(data => {
          // console.log('进来计算高度',data)
          if (data && data.height > 20) {
            this.pageHeight[i] = data.height;
          }
        }).exec();
      }

    },
    confirmOk() {
      if (this.index == 6) {
        this.confirmSelect()
      } else if (this.index == 7) {
        this.confirmStart()
      } else if (this.index == 8) {
        this.confirmUnBind()
      } else if (this.index == 4 || this.index == 5) {
        let title = ''
        if (this.onOffIndex == 4) {
          title = this.reason
        } else {
          title = this.onOffList[this.onOffIndex].title
        }
        this.confirmDelOff(title)
      }
    },
    onScroll() {
      this.scrollControlDisabled = true;
      this.scrollTop = 0
      this.currentIndex = 0
      uni.pageScrollTo({
        scrollTop: 0,
        duration: 100,
        complete: () => {
          setTimeout(() => {
            // 滚动完成后恢复滚动控制逻辑
            // this.scrollControlDisabled = false;
            this.scrollControlDisabled = false;
          }, 300)

        }
      });
    },
    changeBtn(i) {
      this.tabValue = i
    },
    /* 选择及 */
    async confirmSelect() {
      try {
        let parames = {
          device_sn: this.unBindItem.device_sn,
          stopTime: this.reasonList[this.radioIndex].selectIndex,
        }
        await this.$u.api.setStartAndStopTime(parames)
        this.radioIndex = 0
        this.isShowSuccess('设置成功')
      } catch (error) {
        console.log('间隔失败返回', error)
        this.radioIndex = 0
      }

    },
    cancel() {
      this.radioIndex = 0
      // this.isShowDelModalOff = false
      this.isShowStartModal = false
    },
    loading(title, isShowLoading) {
      if (isShowLoading) {
        uni.showLoading({
          title,
          mask: true,
        })
      } else {
        uni.hideLoading()
      }
    },
    /* 更新时间 */
    async updataTime(item) {
      let parames = {
        device_sn: item.device_sn,
      }
      await this.$u.api.getAllStatus(parames)
      setTimeout(() => {
        this.loading('更新中', true)
      }, 100)
      setTimeout(async () => {
        try {
          let res = await this.$u.api.getUMDetail(parames)
          this.unBindItem = item

          let index = this.listData.findIndex(
            (items) => items.id === this.unBindItem.id,
          ) // 查找id为2的元素索引
          let pagesIndex = this.pagesData[this.currentIndex].findIndex((items) => items.id === this.unBindItem.id); // 查找id为2的元素索引
          if (index !== -1) {
            // 如果找到了
            this.unBindItem.isShowLEStauts_str = res.isShowLEStauts_str
            this.listData.splice(index, 1, this.unBindItem)
            this.pagesData[this.currentIndex].splice(pagesIndex, 1, this.unBindItem);
            this.loading()
            this.isShowErr('更新成功')
          }
        } catch (err) {
          console.log('错误', err)
        }

      }, 3000)
    },
    other(item) {
      // this.device_sn = item.device_sn
      if(item.isCheckOnOff){
        this.taskTypeList=['查看行为记录','开启开机自检','关闭开机自检']
      }else{
        this.taskTypeList=['查看行为记录']
      }
      this.showTaskType = true
      this.unBindItem = item
    },
    async comfirmType(i) {
      try {
        this.showTaskType = false
        let e = this.taskTypeList[i]
        if (e == '查看行为记录') {
          uni.navigateTo({
            url:
              '/pagesD/History/History?data=' +
              encodeURIComponent(JSON.stringify(this.unBindItem)),
          })
        } else if (e == '开启开机自检') {
          let data = {
            device_sn: this.unBindItem.device_sn,
            status: 1,
          }
          await this.$u.api.setPowerOnTestStatus(data)
          this.isShowSuccess('操作成功')
        } else if (e == '关闭开机自检') {
          let data = {
            device_sn: this.unBindItem.device_sn,
            status: 0,
          }
          await this.$u.api.setPowerOnTestStatus(data)
          this.isShowSuccess('操作成功')
        }
      } catch (error) {
        console.log('error', error)
      }

    },
    stopTime() {
      this.showTaskType = false
      this.modelTitle = '请选择间隔时间'
      // this.isShowSelectModal = true
      this.index = 6
      this.isShowStartModal = true
      this.tabValue = 0
    },
    onCard(item) {
      if (this.isFromScreenBindDevice) {
        /*  #ifndef H5 */
        let pages = getCurrentPages()
        // let currPage = pages[pages.length - 1] //当前页面
        let prevPage = pages[pages.length - 2] //上一个页面
        //直接调用上一个页面的setData()方法，把数据存到上一个页面中去
        prevPage.setData({
          item: item,
        })
        /*#endif */
        /*  #ifdef H5 */
        this.vCurrPage.item = item
        /*#endif */

        uni.navigateBack({
          delta: 1,
        })
      } else {
        uni.navigateTo({
          url:
            '/pagesB/device/DeviceDetail?from=device_list&device_sn=' +
            item.device_sn,
        })
      }
    },
    change(item) {
      this.optionsList = item
      let index = item[0].value
      this.isStatus = item[0].options[index].status
      if (index < 1) {
        this.lastest_order_time = ''
      } else {
        this.lastest_order_time = subtractDaysAndFormat(
          item[0].options[index].time,
        )
      }
      this.refresh()
    },
    /* 开启自动待机 */
    autoOnOff(item) {
      if (item.is_auto_on_off !== 1) {
        this.index = 2
      } else if (item.is_auto_on_off == 1) {
        this.index = 3
      }
      this.isShowDelModalOnOff = true
      // this.id = item.id
      this.unBindItem = item
    },
    /* 自动开关弹窗 */
    confirmDelOnOff() {
      let is_auto_on_off = 0
      if (this.index == 2) {
        is_auto_on_off = 1
      } else if (this.index == 3) {
        is_auto_on_off = 0
      }

      let params = {
        id: this.unBindItem.id,
        is_auto_on_off,
      }
    },
    /* 禁用设备 */
    onOff(item) {
      if (item.use_status == 1) {
        this.index = 4
        this.modelTitle = ''
      } else if (item.use_status == 2) {
        this.index = 5
        this.modelTitle = '温馨提示'
      } else {
        this.index = 5
        this.modelTitle = '温馨提示'
      }
      // this.isShowDelModalOff = true
      this.isShowStartModal = true
      this.unBindItem = item
      // this.unBindItem = item
    },
    /* 禁用设备弹窗 */
    async confirmDelOff(text) {
      try {
        // this.isShowDelModalOff = false

        this.isShowStartModal = false
        let use_status = 0
        if (this.index == 4) {

          use_status = 2
        } else if (this.index == 5) {

          use_status = 1
        }
        let title = ''
        if (use_status == 2) {
          title = text
        }
        let params = {
          device_sn: this.unBindItem.device_sn,
          use_status,
          reason: title,
        }
        let res = await this.$u.api.setupdateMachineStatus(params)
        // this.refresh();
        let index = this.listData.findIndex(
          (items) => items.id === this.unBindItem.id,
        ) // 查找id为2的元素索引
        let pagesIndex = this.pagesData[this.currentIndex].findIndex((items) => items.id === this.unBindItem.id); // 查找id为2的元素索引
        if (index !== -1) {
          // 如果找到了
          if (this.unBindItem.use_status == 1) {
            this.unBindItem.use_status = 2
            this.unBindItem.reason = title
          } else if (this.unBindItem.use_status == 2) {
            this.unBindItem.use_status = 1
            this.unBindItem.reason = title
          }
          this.listData.splice(index, 1, this.unBindItem)
          this.pagesData[this.currentIndex].splice(pagesIndex, 1, this.unBindItem);
        }
      } catch (error) {
        console.log('错误信息', error)
      }

    },
    /*启动设备 */
    goStart(item) {
      // this.device_sn = item.device_sn
      this.unBindItem = item
      this.index = 7
      this.modelTitle = '启动设备'
      this.isShowStartModal = true
    },
    async confirmStart() {
      try {
        // 开启游戏设备
        let data = {
          device_sn: this.unBindItem.device_sn,
          channel: 1, // 货道
          length_time: this.length_time,
        }

        await this.$u.api.startUM(data)
        // this.isShowSuccess("操作成功", 1, () => { }, true);
        this.isShowSuccess("操作成功");
      } catch (error) {
        console.log('启动设备失败：', error)
      }

    },
    onClickIcon(e) {
      if (e == 0) {
        uni.navigateTo({ url: '/pagesB/mapMode/MapMode?from=device' })
      } else if (e == 1) {
        this.isShowPopup = !this.isShowPopup
      }
    },
    async getList(page, done) {
      // console.log('进来请求', this.isFromHome)
      try {
        this.isShowPopup = false
        let data = {
          device_sn: this.device_sn,
          room_num: this.room_num,
          status: this.status, // 是否在线
          dianweiid: this.dianwei,
          hotel_id: this.hotel_id,
          page: page,
          owner: this.owner, //上级账号
          lastest_order_time: this.lastest_order_time,
        }
        if (this.isStatus < 4) {
          data['status'] = this.isStatus
        }
        let rtnData = null
        if (this.isBind) {
          data['is_bind_hotel'] = this.isBind == 1 ? true : false
        }
        if (this.isFromHome || this.isFromScreenBindDevice) {
          rtnData = await this.$u.api.getUserUMs(data)
        } else if (this.isFromPlace) {
          rtnData = await this.$u.api.getHotelMachines({
            ...data,
            is_bind_hotel: this.is_bind_hotel,
          })
        }

        if (rtnData) {
          if (page == 1) {
            this.deviceTotal = rtnData?.total
          }
          done(rtnData?.data)
          if (this.pagesData.length > this.pageHeight.length) {
            this.$nextTick(() => {
              this.computedHeight()
            });
          }

        }
      } catch (error) {
        console.log(error)
      }

    },
    onUnbind(item) {
      if (item.dianweiid && item.dianweiid > 0) {
        // 已经绑定了点位 弹出是否解绑
        this.unBindItem = item
        // this.isShowModal = true
        this.isShowStartModal = true
        this.modelTitle = '温馨提示'
        this.index = 8

      } else {
        // 如果没有绑定点位，跳转到绑定点位界面
        uni.navigateTo({
          url:
            `/pagesB/place/BindPlace?from=device&device_sn=` +
            item.device_sn +
            '&mid=' +
            item.id,
        })
      }
    },
    async confirmUnBind() {
      try {
        //确认解绑
        let data = {
          device_sn: this.unBindItem.device_sn,
        }

        await this.$u.api.unbindHotel(data)
        this.isShowSuccess('解绑成功', 0, () => this.refresh())
      } catch (e) {
        console.log('e', e)
      }

    },
    deviceGoods(device_sn) {
      uni.navigateTo({
        url: `/pagesB/device/DeviceGoodsList?from=device&device_sn=${device_sn}`,
      })
    },
    // searchChange(val) {
    //   this.hotelName = val
    //   this.search(val)
    // },
    search(val) {
      // console.log('进来几次')
      this.device_sn = val
      let list = AddValueInObject(this.vServerList.device, val)
      this.$u.vuex(`vServerList.device`, list)
      this.refresh()
    },
    repl(device_sn) {
      uni.navigateTo({
        url:
          '/pagesB/device/DeviceGoodsList?from=replenish&device_sn=' +
          device_sn,
      })
    },
    /* 遥控 */
    remote(item) {
      uni.navigateTo({
        url:
          `/pagesC/remote/RemoteEquipment?from=replenish&device_sn=` +
          item.device_sn +
          '&mid=' +
          item.id,
      })
    },
    //点击扫码icon
    onClickScan(result, option) {
      if (!result) return this.isShowErr('请扫描正确二维码~')
      if (option) {
        if (option.vscode) {
          this.device_sn = option.vscode
        } else if (option.device_sn) {
          this.device_sn = option.device_sn
        } else if (option.mid) {
          this.device_sn = option.mid
        } else {
          this.device_sn = result
        }
      } else {
        this.device_sn = result
        // this.isShowErr("未扫描设备码~");
      }
      this.refresh()
      this.device_sn = ''
    },
    confirmScreenner(e) {
      this.device_sn = e.device_sn
      this.room_num = e.room_num
      this.status = e.status
      // this.dianwei = e.dianwei;
      this.hotel_id = e.dianwei
      this.owner = e.owner
      // this.isBind = e.is_bind_hotel;
      this.isShowPopup = false
      this.refresh()
    },
  },
  computed: {
    message() {
      if (this.index === 8) {
        return '您的设备将与' + this.vPointName + '解除绑定，是否继续解绑？';
      } else if (this.index === 5) {
        return '您确定要启用该设备吗？';
      } else {
        return '';
      }
    }
  },
  onLoad(opt) {
    this.fromData = opt?.from;
    this.is_bind_hotel = false;
    this.isFromPlace = false;
    this.isShowGo = false;
    this.isFromScreenBindDevice = false;
    this.isFromHome = false;

    const config = {
      place: () => {
        this.title = `${this.vPointName}设备`;
        this.isFromPlace = true;
        this.dianwei = opt.dianwei;
        this.hotel_id = opt.hotel_id;
        this.is_bind_hotel = true;
      },
      go: () => {
        this.title = `${this.vPointName}设备`;
        this.isFromPlace = true;
        this.isShowGo = true;
        this.dianwei = opt.dianwei;
        this.hotel_id = opt.hotel_id;
        this.is_bind_hotel = true;
      },
      screen_bind_device: () => {
        this.title = '选择设备';
        this.isFromScreenBindDevice = true;
      },
      device: () => {
        this.title = '设备列表';
        this.isFromScreenBindDevice = true;
        this.optionsList[0].value = opt.status;
        this.optionsList[0].title =
          opt.status === '1' ? '在线' :
            opt.status === '2' ? '异常' :
              opt.status === '3' ? '离线' : '全部';
        this.status = opt.status;
      },
      home: () => {
        this.title = '设备列表';
        this.hotelName = opt.device_sn;
        this.isFromHome = true;
        this.search(opt.device_sn);
      },
      default: () => {
        this.isFromHome = true;
      }
    };

    // 执行对应的处理函数
    if (config[this.fromData]) {
      config[this.fromData]();
      if (this.fromData === 'home') {
        return; // 退出 onLoad
      }
    } else {
      config.default();
    }

    // 如果输入框不可编辑，设置时间长度
    if (this.vInputDisable) {
      this.length_time = this.vTime;
    }

    this.customStyle = {
      top: 110 + this.vStatusBarHeight + this.vNavBarHeight + 'rpx',
    };

    // 触发数据刷新
    this.refresh();
    this.systemHeight = uni.getSystemInfoSync().windowHeight; // 获取屏幕高度
    // console.log('屏幕高度', this.systemHeight);
    console.log('refs', this.$refs)
  },

  onShow() {
    let isDoRefresh, item;
    // 获取当前页的数据（针对不同平台）
    /*  #ifndef H5 */
    let pages = getCurrentPages();
    let currPage = pages[pages.length - 1]; // 当前页
    isDoRefresh = currPage.data.isDoRefresh;
    item = currPage.data.item;
    currPage.data.isDoRefresh = false;
    /*#endif */

    /*  #ifdef H5 */
    isDoRefresh = this.vCurrPage.isDoRefresh;
    item = this.vCurrPage.item;
    this.vCurrPage.isDoRefresh = false;
    /*#endif */

    // 是否刷新
    if (isDoRefresh) {
      this.refresh();
    }
    // 更新设备筛选器中的酒店信息
    if (item && this.isShowPopup) {
      let checkHotel = item;
      this.$refs.DeviceScreener.hotelName = checkHotel.hotelName;
      this.$refs.DeviceScreener.hotel_id = checkHotel.id;
    }

  },
  onPageScroll(event) {
    if (!event.scrollTop || this.scrollControlDisabled) {
      return;
    }
    let pageScrollTop = event.scrollTop;
    this.scrollTop = event.scrollTop;
    if (this.pagesData.length > this.pageHeight.length) {
      this.currentIndex = this.pagesData.length - 1;
      this.$nextTick(() => {
        this.computedHeight()
      });
    } else {
      let scrollTop = 0;
      for (let i = 0; i < this.pageHeight.length; i++) {
        scrollTop += this.pageHeight[i];
        if (scrollTop > pageScrollTop + this.systemHeight) {
          this.currentIndex = i;
          break;
        }
      }
    }
    // console.log('pageData.length',this.currentIndex,this.pagesData.length>this.pageHeight,this.pagesData.length,this.pageHeight)
  },
  mixins: [myPull()],
}
</script>
<style lang="scss">
page {
  background-color: $pageBgColor;
}
</style>

<style lang="scss" scoped>
.title {
  margin-bottom: 20rpx;
}

.border {
  border: 2rpx solid transparent;
  box-sizing: border-box;
  /* 包含边框和内边距在元素的总宽高内 */
}

.pup_box {
  padding: 20rpx;
  padding-top: 40rpx;
  display: flex;
  flex-wrap: wrap;

  .pup_item {
    width: 270rpx;
    margin: 20rpx;
  }

  .default {
    height: 100rpx;
    font-size: 35rpx;
    display: flex;
    align-items: center;
  }
}

.modalPad {
  ::v-deep .u-model__content {
    padding: 15rpx 50rpx;
  }

  ::v-deep .u-radio {
    margin: 10rpx 0;
  }
}
</style>
